# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/05/08


import pandas as pd

# Raw data from the user, split into columns and two rows
data = {
    'Metric': [
        'total_records', 'citedocnum_non_null_count', 'citedocnum_null_count', 'citedocnum_null_rate',
        'min_citedocnum', 'max_citedocnum', 'avg_citedocnum', 'var_citedocnum',
        'citedocnum_25th_percentile', 'citedocnum_50th_percentile', 'citedocnum_75th_percentile',
        'prioritynum_non_null_count', 'prioritynum_null_count', 'prioritynum_null_rate',
        'min_prioritynum', 'max_prioritynum', 'avg_prioritynum', 'var_prioritynum',
        'prioritynum_25th_percentile', 'prioritynum_50th_percentile', 'prioritynum_75th_percentile',
        'familynum_non_null_count', 'familynum_null_count', 'familynum_null_rate',
        'min_familynum', 'max_familynum', 'avg_familynum', 'var_familynum',
        'familynum_25th_percentile', 'familynum_50th_percentile', 'familynum_75th_percentile',
        'filcrecornum_non_null_count', 'filcrecornum_null_count', 'filcrecornum_null_rate',
        'min_filcrecornum', 'max_filcrecornum', 'avg_filcrecornum', 'var_filcrecornum',
        'filcrecornum_25th_percentile', 'filcrecornum_50th_percentile', 'filcrecornum_75th_percentile',
        'rpcnum_non_null_count', 'rpcnum_null_count', 'rpcnum_null_rate',
        'min_rpcnum', 'max_rpcnum', 'avg_rpcnum', 'var_rpcnum',
        'rpcnum_25th_percentile', 'rpcnum_50th_percentile', 'rpcnum_75th_percentile',
        'tstnum_non_null_count', 'tstnum_null_count', 'tstnum_null_rate',
        'min_tstnum', 'max_tstnum', 'avg_tstnum', 'var_tstnum',
        'tstnum_25th_percentile', 'tstnum_50th_percentile', 'tstnum_75th_percentile',
        'pattypenum_non_null_count', 'pattypenum_null_count', 'pattypenum_null_rate',
        'min_pattypenum', 'max_pattypenum', 'avg_pattypenum', 'var_pattypenum',
        'pattypenum_25th_percentile', 'pattypenum_50th_percentile', 'pattypenum_75th_percentile',
        'pagecount_non_null_count', 'pagecount_null_count', 'pagecount_null_rate',
        'min_pagecount', 'max_pagecount', 'avg_pagecount', 'var_pagecount',
        'pagecount_25th_percentile', 'pagecount_50th_percentile', 'pagecount_75th_percentile'
    ],
    'Row1': [
        16734077, 16734077, 0, 0, None, None, None, None, None, None, None,
        16734077, 0, 0, -1, 44, 0.544835487, 0.7152226, 0.06072348, 1, 1,
        16734077, 0, 0, -1, 275, 0.704883096, 1.236324747, 1, 1, 1,
        34631, 16699446, 99.79305103, 1, 21, 1.235424908, 1.046391404, 1, 1, 1,
        206995, 16527082, 98.76303306, 1, 14, 1.222763835, 0.328641449, 1, 1, 1,
        749300, 15984777, 95.52231055, 1, 23, 1.361963166, 0.435093704, 1, 1, 2,
        16734077, 0, 0, -1, 87, 2.367789511, 4.083222281, 1, 2, 3,
        1731390, 15002687, 89.65350763, 1, 2892, 9.152513876, 44.32466748, 6, 7, 10
    ],
    'Row2': [
        None, None, None, None, None, None, None, None, None, None, None,
        None, None, None, None, None, None, None, None, None, None,
        None, None, None, None, None, None, None, None, None, None,
        None, None, None, None, None, None, None, None, None, None,
        None, None, None, None, None, None, None, None, None, None,
        None, None, None, None, None, None, None, None, None, None,
        None, None, None, None, None, None, None, None, None, None
    ]
}

# Create a DataFrame
df = pd.DataFrame(data)

# Transpose the DataFrame to switch rows and columns
df_transposed = df.set_index('Metric').transpose()

df_transposed
