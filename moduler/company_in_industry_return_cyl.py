# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/11
from collections import defaultdict

from core.excel_base import ExcelBase
from utils.log_util import create_logger

LOGGER = create_logger("TEST", "qiyuan_data.log")


class CompanyInIndustryReturnCYL(ExcelBase):
    """
    公司行业及产业链数据
    输入：行业名称
    输出：{
        industryId：行业id
        IndustryName：行业
        cyl_name：产业链
        }
    """

    def __init__(self):
        super(CompanyInIndustryReturnCYL, self).__init__()
        self.cyl_map = self.query_china_data()

    def process(self, ind):
        return self.query_cmp_industry(ind) or dict()

    def query_cmp_industry(self, ind):
        sql_statement = """
        SELECT industryId, IndustryName FROM sq_comp_all_industry WHERE IndustryName='{}' limit 1;
        """
        query_schema = dict(db_key="db_seeyii_128", sql_statement=sql_statement.format(ind))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            ind_id = item["industryId"]
            cyl_name_set = self.cyl_map.get(ind_id)
            if cyl_name_set:
                item["cyl_name"] = ",".join(list(cyl_name_set))
            return item

    def query_china_data(self):
        result = dict()
        sql_statement = """
        SELECT b.industryId as second_id, a.name AS cyl_name 
        from sq_oa_chain a LEFT JOIN sq_oa_chain_industry b on a.id=b.chainId"""
        query_schema = dict(db_key="db_seeyii_128", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            second_id = item["second_id"]
            cyl_name = item["cyl_name"]
            result.setdefault(second_id, set())
            result[second_id].add(cyl_name)
        return result


if __name__ == '__main__':
    p = CompanyInIndustryReturnCYL()
    p.process()
