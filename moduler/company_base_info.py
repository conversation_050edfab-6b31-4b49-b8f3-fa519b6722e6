# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/05/13
from core.excel_base import ExcelBase


class CompanyGS(ExcelBase):
    """
    查询工商基本信息
    输入：[names...]
    输出：{
            name:{
                ...

            }
        }
        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `compName` varchar(512) DEFAULT NULL COMMENT '公司名称',
  `compEngName` varchar(255) DEFAULT NULL COMMENT '英文名',
  `compNewName` varchar(255) DEFAULT NULL COMMENT '公司新名称',
  `provinceCode` int(20) DEFAULT NULL COMMENT '所属省份代码',
  `parentOrgName` varchar(255) DEFAULT NULL COMMENT '上级机构名称',
  `legalPersonName` varchar(125) DEFAULT NULL COMMENT '法人姓名',
  `legalPersonType` int(4) DEFAULT NULL COMMENT '法人类型',
  `regNum` varchar(64) DEFAULT NULL COMMENT '工商注册码',
  `creditCode` varchar(255) DEFAULT NULL COMMENT '统一社会信用代码',
  `taxpayerId` varchar(255) DEFAULT NULL COMMENT '纳税人识别号',
  `orgCode` varchar(64) DEFAULT NULL COMMENT '组织机构代码',
  `orgAprvUnit` varchar(125) DEFAULT NULL COMMENT '组织机构批准单位',
  `compType` varchar(255) DEFAULT NULL COMMENT '公司类型',
  `businessScope` varchar(4096) DEFAULT NULL COMMENT '经营范围',
  `regStatus` varchar(255) DEFAULT NULL COMMENT '企业状态',
  `regAddr` varchar(255) DEFAULT NULL COMMENT '注册地址',
  `regCapital` decimal(19,4) DEFAULT NULL COMMENT '注册资金（万元）',
  `actualCapital` decimal(19,4) DEFAULT NULL COMMENT '实收注册资金（万元）',
  `currencyCode` varchar(125) DEFAULT NULL COMMENT '币种',
  `regOrg` varchar(255) DEFAULT NULL COMMENT '登记机关',
  `estiblishDate` date DEFAULT NULL COMMENT '成立日期',
  `aprvPublDt` date DEFAULT NULL COMMENT '核准日期',
  `startDate` date DEFAULT NULL COMMENT '营业期限开始日期',
  `endDate` date DEFAULT NULL COMMENT '营业期限终止日期',
    """
    def __init__(self):
        super(CompanyGS, self).__init__()

    def process(self, names):
        result = dict()
        for idx in range(0, len(names), 100):
            name_list = names[idx:idx + 100]
            base_info_list = self.query_mysql_data(name_list)
            for item in base_info_list:
                name = item["compName"]
                result.setdefault(name, item)
        return result

    def query_mysql_data(self, names):
        name_str = "','".join(names)
        sql_statement = """SELECT * from sy_cd_ms_base_normal_comp_list 
                WHERE dataStatus!=3 and compName in ('{}');"""
        query_schema = dict(db_key="xskv2", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    p = CompanyGS()
    p.process()
