# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/6/2
from core.excel_base import ExcelBase

"""
输入：[name,...]
输出：{
    name:[
     {
        compName	公司名称
        rewardSources	奖励来源
        rewardName	奖励名称
        rewardProject	奖励项目
        rewardYear	奖励年份
        areaCategory	奖励级别
    },
    ...
        ],
    ...

}
"""


class CompanyRewardMysql(ExcelBase):
    def __init__(self):
        super(CompanyRewardMysql, self).__init__()

    def process(self, names):
        result = dict()
        for idx in range(0, len(names), 50):
            name_list = names[idx:idx + 50]
            result.update(self.query_reward_data(name_list))
        return result

    def query_reward_data(self, names):
        result = dict()
        name_str = "','".join(names)
        sql_statement = """SELECT * from dwd_me_buss_rewd where dataStatus !=3 and compName in ('{}');"""
        query_schema = dict(db_key="db_seeyii", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result.setdefault(item["compName"], list())
            result[item["compName"]].append(item)
        return result
