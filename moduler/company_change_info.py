# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/11
from collections import defaultdict
from core.excel_base import ExcelBase

"""
输入：[names...]
输出：{
    name:[
        {
          `compName` varchar(512) DEFAULT NULL COMMENT '公司名称',
      `chgType` varchar(512) DEFAULT NULL COMMENT '变动类型',
      `beChg` mediumtext DEFAULT NULL COMMENT '变动前',
      `afChg` mediumtext DEFAULT NULL COMMENT '变动后',
      `changeTime` datetime DEFAULT NULL COMMENT '变动时间',
        }
    ]

}
"""


class CompanyChange(ExcelBase):
    def __init__(self):
        super(CompanyChange, self).__init__()

    def process(self, name_list):
        result = defaultdict(list)
        for idx in range(0, len(name_list), 100):
            names = name_list[idx:idx + 100]
            items = self.query_holder_data(names)
            for item in items:
                name = item["compName"]
                result[name].append(item)
        return result

    def query_holder_data(self, names):
        name_str = "','".join(names)
        sql_statement = """SELECT * from sy_cd_ms_base_gs_comp_change where compName in ('{}') and dataStatus!=3"""
        query_schema = dict(db_key="tidb_135", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    p = CompanyChange()
    p.process()
