# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/11
from collections import defaultdict
from core.excel_base import ExcelBase

"""
输入：[names...]
输出：{
    name:[
        {
        `compName` varchar(200) NOT NULL COMMENT '公司名称',
          `shName` varchar(255) NOT NULL COMMENT '股东名称',
          `subAmount` decimal(30,4) DEFAULT NULL COMMENT '认缴出资额',
          `subTime` datetime DEFAULT NULL COMMENT '认缴出资时间',
          `subType` varchar(500) DEFAULT NULL COMMENT '认缴出资方式',
          `subUnit` varchar(50) DEFAULT NULL COMMENT '认缴出资单位',
          `actulCapital` decimal(19,4) DEFAULT NULL COMMENT '实缴出资额',
          `actulTime` datetime DEFAULT NULL COMMENT '实缴出资时间',
          `actulType` varchar(500) DEFAULT NULL COMMENT '实缴出资方式',
          `actulUnit` varchar(50) DEFAULT NULL COMMENT '实缴出资单位',
        `holdRatio` decimal(30,6) DEFAULT NULL COMMENT '持股比例',
        }
    ]

}
"""

class CompanyHolderOld(ExcelBase):
    def __init__(self):
        super(CompanyHolderOld, self).__init__()

    def process(self, name_list):
        result = defaultdict(list)
        for idx in range(0, len(name_list), 100):
            names = name_list[idx:idx + 100]
            items = self.query_holder_data(names)
            for item in items:
                name = item["cname"]
                result[name].append(item)
        return result

    def query_holder_data(self, names):
        name_str = "','".join(names)
        sql_statement = """SELECT compName as cname, shName as shareholder_name, holdRatio
         from sy_cd_ms_sh_gs_shlistnew WHERE dataStatus!=3 and compName in ('{}');"""
        query_schema = dict(db_key="tidb_135", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    p = CompanyHolderOld()
    p.process()
