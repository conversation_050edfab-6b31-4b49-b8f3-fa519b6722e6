# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/05/13
from core.excel_base import ExcelBase


class CompanyRep(ExcelBase):
    """
    公司年报信息
    """

    def __init__(self):
        super(CompanyRep, self).__init__()

    def process(self, name_list):
        result = dict()
        for idx in range(0, len(name_list), 50):
            names = name_list[idx: idx + 50]
            data = self.query_cmp_area(names)
            result.update(data)
        return result

    def query_cmp_area(self, names):
        result = dict()
        name_str = "','".join(names)
        sql_statement = """
        SELECT 
            compName,
            max(fiscalYear) as report_year,
            contactTel as phone_number, 
            contactAddr as postal_address, 
            contactEmail as email
        from sy_cd_me_news_gs_annual_report 
        WHERE 
            compName in ('{}') 
            and dataStatus != 3"""
        query_schema = dict(db_key="tidb_135", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result.setdefault(item["compName"], item)
        return result


if __name__ == '__main__':
    p = CompanyRep()
    p.process()
