# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/11
from collections import defaultdict
from core.excel_base import ExcelBase

"""
输入：[names...]
输出：{
    name:[
        {
        name:公司名称，
        shareholder_name:股东名称
        holdRatio：持股比例
        investAmount：认缴金额
        }
    ]
}
"""


class CompanyHolder(ExcelBase):
    """
    此模块中吊销的公司没有股东
    0: '其他', 1: '存续', 2: '吊销，未注销', 3: '已告解散', 4: '撤销', 5: '停业', 6: '成立中', 7: '仍注册', 8: '清算', 9: '在业', 10: '其他',
    11: '注销', 12: '正常', 13: '证书废止', 14: '吊销', 15: '开业', 16: '吊销，已注销', 17: '证书过期', 18: '迁入', 19: '迁出', 20: '冻结',
    """
    def __init__(self):
        super(CompanyHolder, self).__init__()

    def process(self, name_list):
        result = defaultdict(list)
        for idx in range(0, len(name_list), 100):
            names = name_list[idx:idx + 100]
            items = self.query_holder_data(names)
            for item in items:
                name = item["cname"]
                result[name].append(item)
        return result

    def query_holder_data(self, names):
        name_str = '","'.join(names)
        sql_statement = """
        SELECT  IF(b.compName IS NOT NULL, b.compName, a.shName) AS shareholder_name, 
        c.compName AS cname, holdRatio, investAmount FROM sy_cd_ms_sh_gs_shlist_new a 
        LEFT JOIN sy_cd_ms_base_gs_comp_info_new c ON  a.compCode=c.compCode AND c.dataStatus!=3
        LEFT JOIN  sy_cd_ms_base_gs_comp_info_new b ON  a.shId=b.compCode AND b.dataStatus!=3
        WHERE  a.isValid =1 AND a.dataStatus != 3 AND c.compName IN ("{}")  
        and infoSource=2 and c.regStatus not in (2,3,4,5,11,14,16,17,19)"""
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    p = CompanyHolder()
    p.process()
