# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/21
from collections import defaultdict

from core.excel_base import ExcelBase


class InCategory(ExcelBase):
    """
    查询公司标签
    输入：[category,...]
    输出：{
        name:{
            company_code,
            st_name,
            cname,
            category
            }
        }
    """

    def __init__(self):
        super(InCategory, self).__init__()

    def process(self, cat_list):
        result = defaultdict()
        items = self.query_company_category(cat_list)
        for item in items:
            name = item["cname"]
            result[name] = item
        return result

    def query_company_category(self, cat_list):
        query_schema = {
            "db_name": "category_data",
            "collection_name": "company_category",
            "query_condition": {"category": {"$in": cat_list}, "is_valid": True},
            "query_field": {"company_code": 1, "st_name": 1, "_id": 0,
                            "category": 1, "cname": 1}}
        query_result = self._data_server.call("query_item", query_schema) or list()
        return query_result


if __name__ == '__main__':
    p = InCategory()
    p.process()
