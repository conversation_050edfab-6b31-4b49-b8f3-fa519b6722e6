# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/02/20
from core.excel_base import ExcelBase


class SheBao(ExcelBase):
    """
     职工社保人数信息
      name:{
             'name': ('公司名称', 0),
            'endowment_insurance': ('城镇职工基本养老保险', 1),
            'unemployment_insurance': ('失业保险', 2),
            'medical_insurance': ('职工基本医疗保险', 3),
            'employment_injury_insurance': ('工伤保险', 4),
            'maternity_insurance': ('生育保险', 5),
              },
      ...

    """
    def __init__(self):
        super(SheBao, self).__init__()
        self.db_key = 'prism1'

    def process(self, names):
        items = list()
        for idx in range(0, len(names), 100):
            name_list = names[idx:idx + 100]
            data_list = self.query_mysql_data(name_list)
            items.extend(data_list)
        return self.result_for_report_year(items)

    def query_mysql_data(self, names):
        name_str = "','".join(names)
        sql_statement = """
        SELECT name, report_year, c.* from company as a right join annual_report as b  on b.company_id=a.id 
        right join report_social_security_info as c  on b.id=c.annaulreport_id where a.name in ('{}');
        """
        query_schema = dict(db_key=self.db_key, sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list

    @staticmethod
    def result_for_report_year(data_list):
        ret_dic = dict()
        for data in data_list:
            ret_dic.setdefault(data['name'], list())
            ret_dic[data['name']].append(data)

        for k, v in ret_dic.items():
            ret_dic[k] = max(v, key=lambda x: x['report_year'])
        return ret_dic


if __name__ == '__main__':
    p = SheBao()
    p.process()
