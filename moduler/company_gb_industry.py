# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/11
from core.excel_base import ExcelBase


class CompanyGBIndustry(ExcelBase):
    """
    国标行业
    输入：公司名单
    输出：{
        company_name:{
            ind_m:门类，
            ind_d:大类，
            ind_z:中类
            }
        ...
        }
    """

    def __init__(self):
        super(CompanyGBIndustry, self).__init__()

    def process(self, name_list):
        result = dict()
        ind_map = self.query_mysql_ind()
        for idx in range(0, len(name_list), 50):
            names = name_list[idx: idx + 50]
            name_dict = self.query_cmp_ind(names)
            for name, cat in name_dict.items():
                if not cat:
                    continue
                ind = ind_map.get(int(cat))
                if not ind:
                    continue
                result.setdefault(name, ind)
        return result

    def query_cmp_ind(self, names):
        result = dict()
        name_str = "','".join(names)
        sql_statement = """SELECT compName, industryId as categoryCode from 
        sy_cd_ms_base_gs_comp_info_new where compName in ('{}') and dataStatus !=3"""
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result.setdefault(item["compName"], item["categoryCode"])
        return result

    def query_mysql_ind(self):
        t_map = {3: "ind_m", 4: "ind_d", 5: "ind_z"}
        result = dict()
        sql_statement = """SELECT constCode, constValue, constValueDesc from sy_cd_mt_sys_const 
        where constCode in (3, 4, 5) and dataStatus !=3;"""
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            new_item = dict()
            const_value = item.get("constValue")
            constCode = item["constCode"]
            field = t_map[constCode]
            constValueDesc = item.get("constValueDesc")
            new_item[field] = constValueDesc
            result.setdefault(const_value, dict())
            result[const_value].setdefault(field, constValueDesc)
        return result


if __name__ == '__main__':
    p = CompanyGBIndustry.run(["浙江金凤凰电力科技有限公司"])
    print(p)
