# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/11
from collections import defaultdict
from core.excel_base import ExcelBase

cat_map = {
    "PrivateFundCompany": "私募基金管理公司（备案）", "PublicFundCompany": "公募基金管理公司", "SecurityCompany": "证券公司",
    "FuturesCompany": "期货公司", "InvestCompanyOfBroker": "券商私募投资子公司", "Inv100003": "券商另类投资子公司", "SYAA10000": "券商资产管理子公司",
    "SYAU10000": "集团公司投资平台", "PublicChildCompany": "基金子公司", "LargeCommercialBank": "大型商业银行",
    "JointStockCommercialBank": "股份制商业银行", "PrivateBank": "民营银行", "RuralCommercialBank": "农村商业银行",
    "ForeignBank": "外资银行", "CityCommercialBank": "城市商业银行", "DevelopmentBank": "开发性金融机构", "PolicyBank": "政策性银行",
    "AssetManagementCompany": "金融资产管理公司", "FinancialLeaseCompany": "金融租赁公司", "TrustCompany": "信托公司",
    "FinanceCompany": "集团财务公司", "InsuranceCompany": "保险公司", "InsuranceAssetCompany": "保险资产管理公司",
    "GovGXInvestCompany": "政府高新投资平台", "GovCYInvestCompany": "政府产业引导基金", "NationalInvestmentPlatform": "国家投资平台",
    "FundOfFund": "母基金", "InvestmentConsultCompany": "证券投资咨询公司", "UnrecordedPrivateFundCompany": "私募基金管理公司（未备案）",
    "SYAA20000": "海外知名私募机构", "PrivateFund": "私募基金（已备案）", "SpecialAccountFund": "基金专户",
    "SecurityAssetManagementPlan": "券商资管计划", "FuturesAssetManagementPlan": "期货资管计划", "BrokerFund": "券商直投基金",
    "PublicFund": "公募基金", "UnrecordedFund": "私募基金（未备案）", "UnrecordedOtherAsset": "未备案三类资产",
    "manage_scala_zero": "管理规模为零", "one_year_manage_scala_zero": "登记一年以上管理规模为零",
    "hand_in_under_register_25_p": "管理人实缴资本低于注册资本25%", "hand_in_le_100_w": "管理人实缴资本低于100万",
    "not_normal_liquidation": "非正常清算", "identification": "异常机构", "sham_statement": "虚假填报",
    "mater_rial_omission": "重大遗漏", "eight_line": "违反八条底线", "bad_credit": "相关主体存在不良诚信记录",
    "is_lost_contact_mechanism": "失联机构", "SYAB10000": "成立超过1年未有股权投资", "SYAB20000": "仅有个人股东/LP", "SYAC10000": "有上市公司参与",
    "GzwCentralCompany": "国资委下属央企", "JgCentralCompany": "央属金融机构", "CbsCentralCompany": "央属出版社",
    "TzptCentralCompany": "国家投资平台", "BwCentralCompany": "其他部委下属央企", "JysCentralCompany": "交易所相关企业",
    "UniversityCompany": "高校直属投资企业", "FinanceHoldingsCompany": "金融控股集团", "University": "高校",
    "UniversityFirstInvCompany": "高校企业投资一级公司", "UniversitySecondInvCompany": "高校企业投资二级公司", "Com900001": "民营产业集团",
    "SYAE10001": "外商投资企业-第一类", "Com900002": "外商投资企业", "Com900004": "全国工商联民营企业500强", "Com900005": "标准制定企业",
    "ACompany": "A股公司", "CN10001": "A股-主板", "CN10002": "A股-创业板", "CN10003": "A股-中小板", "CN10004": "A股-科创板",
    "US10000": "美股公司", "US10001": "美股-NYSE", "US10002": "美股-NASDAQ", "US10003": "美股-AMEX", "HK10000": "港股上市公司",
    "HK10001": "港股-主板", "HK10002": "港股-创业板", "ThirdCompany": "三板公司", "CN30001": "三板-创新层", "CN30002": "三板-基础层",
    "CN30003": "三板-精选层", "FourthCompany": "四板公司", "OthersCompany": "非挂牌上市公司", "SYAU20000": "企业个人持股平台",
    "SYAU30000": "有企业个人持股平台", "ThirdDelistCompany": "三板终止挂牌公司", "ADelistCompany": "A股退市公司",
    "InvestedCompany": "已有投资机构参与", "US20000": "拟美股上市", "HK20000": "拟港股上市", "HK21001": "拟港股上市-注册地在境内",
    "HK21002": "拟港股上市-注册地在境外但由境内控制", "HK21003": "拟港股上市-注册地在境外且由境外控制", "StartGuidanceCompany": "已启动IPO辅导",
    "ApplyIPOCompany": "已申请IPO排队", "ApproveIPOCompany": "IPO通过", "RefuseIPOCompany": "IPO被否", "StopIPOCompany": "IPO终止",
    "PrepareIPOCompany": "拟IPO上市", "PrepareThirdCompany": "拟挂牌三板", "ThirdBrokerCompany": "三板-做市转让",
    "ThirdProtocolCompany": "三板-集合竞价", "Com900003": "独角兽企业", "SYAE20004": "科创板未通过", "SYAE20003": "科创板申报中止",
    "SYAE20002": "科创板申报终止", "SYAE20001": "科创板申报企业", "SYAE20000": "科创板潜力企业", "SYAF10000": "四板-{}",
    "SYAG90000": "国有企业", "SYAG10000": "国有独资企业", "SYAG20000": "国有全资企业", "SYAG30000": "国有控股企业", "SYAG40000": "国有实际控制企业",
    "SYAG50000": "国有参股企业", "SYAG60000": "国资参股基金", "SYAG70000": "国资管理基金", "CentralCompany": "中央直属国企",
    "ProvincialCompany": "省级直属国企", "CityCompany": "市级直属国企", "CentralFirstInvCompany": "央企一级子公司",
    "CentralSecondInvCompany": "央企二级子公司", "ProvincialFirstInvCompany": "省企一级子公司",
    "ProvincialSecondInvCompany": "省企二级子公司", "CityFirstInvCompany": "市企一级子公司", "CitySecondInvCompany": "市企二级子公司",
    "CountyCompany": "县级直属国企", "CountyFirstInvCompany": "县企一级子公司", "CountySecondInvCompany": "县企二级子公司",
    "SYAH10000": "国资参股基金所投企业", "SYAH20000": "国资管理基金所投企业", "SYAI10001": "初创期企业", "SYAI10002": "成长期企业",
    "SYAI10003": "成熟期企业", "SYAI20000": "境外架构美元融资", "SYAJ10000": "债券违约记录", "SYAJ20000": "所担保债券发行人违约",
    "SYAK10000": "开庭公告记录", "SYAK10001": "法律诉讼记录", "SYAK10002": "法院公告记录", "SYAK10003": "法院执行记录", "SYAK10004": "法院失信记录",
    "SYAL10000": "工商行政处罚", "SYAL20000": "股权有出质", "SYAL30000": "经营异常", "SYAL40000": "有动产抵押", "SYAL50000": "有知识产权出质",
    "SYAM10000": "专项证照-{}", "SYAN10000": "上市公司供应商", "SYAN20000": "央企供应商", "SYAN30000": "政府供应商", "SYAN40000": "金融机构供应商",
    "SYAO10000": "高新技术企业", "SYAO20000": "科技型中小企业", "SYAO30000": "技术创新示范企业", "SYAO30001": "市级技术创新示范企业",
    "SYAO30002": "省级技术创新示范企业", "SYAO30003": "国家级技术创新示范企业", "SYAO40000": "专精特新/小巨人企业", "SYAO40001": "市级专精特新/小巨人企业",
    "SYAO40002": "省级专精特新/小巨人企业", "SYAO40003": "国家级专精特新/小巨人企业", "SYAO50000": "拥有工业设计中心", "SYAO50001": "拥有市级工业设计中心",
    "SYAO50002": "拥有省级工业设计中心", "SYAO50003": "拥有国家级工业设计中心", "SYAO60000": "技术先进型服务企业", "SYAO60001": "市级技术先进型服务企业",
    "SYAO60002": "省级技术先进型服务企业", "SYAO60003": "国家级技术先进型服务企业", "SYAO70000": "拥有工程技术研究中心", "SYAO70001": "拥有市级工程技术研究中心",
    "SYAO70002": "拥有省级工程技术研究中心", "SYAO70003": "拥有国家级工程技术研究中心", "SYAO80000": "拥有企业重点实验室", "SYAO80001": "拥有市级企业重点实验室",
    "SYAO80002": "拥有省级企业重点实验室", "SYAO80003": "拥有国家级企业重点实验室", "SYAO90000": "拥有院士专家工作站", "SYAO90001": "拥有市级院士专家工作站",
    "SYAO90002": "拥有省级院士专家工作站", "SYAO90003": "拥有国家级院士专家工作站", "SYAOA0000": "拥有科技成果转化项目", "SYAOA0001": "拥有市级科技成果转化项目",
    "SYAOA0002": "拥有省级科技成果转化项目", "SYAOA0003": "拥有国家级科技成果转化项目", "SYAOB0000": "服务型制造示范企业", "SYAOB0001": "市级服务型制造示范企业",
    "SYAOB0002": "省级服务型制造示范企业", "SYAOB0003": "国家级服务型制造示范企业", "SYAOC0000": "新型研发机构", "SYAOC0001": "市级新型研发机构",
    "SYAOC0002": "省级新型研发机构", "SYAOC0003": "国家级新型研发机构", "SYAOD0000": "拥有企业技术中心", "SYAOD0001": "拥有市级企业技术中心",
    "SYAOD0002": "拥有省级企业技术中心", "SYAOD0003": "拥有国家级企业技术中心", "SYAOE0000": "拥有高新技术成果转化项目", "SYAOE0001": "拥有市级高新技术成果转化项目",
    "SYAOE0002": "拥有省级高新技术成果转化项目", "SYAOE0003": "拥有国家级高新技术成果转化项目", "SYAOF0000": "拥有高新技术企业研究开发中心",
    "SYAOF0001": "拥有市级高新技术企业研究开发中心", "SYAOF0002": "拥有省级高新技术企业研究开发中心", "SYAOF0003": "拥有国家级高新技术企业研究开发中心",
    "SYAOG0000": "制造业单项冠军", "SYAOG0001": "市级制造业单项冠军", "SYAOG0002": "省级制造业单项冠军", "SYAOG0003": "国家级制造业单项冠军",
    "SYAOH0000": "瞪羚企业", "SYAOI0000": "拥有众创空间", "SYAOI0001": "拥有市级众创空间", "SYAOI0002": "拥有省级众创空间",
    "SYAOI0003": "拥有国家级众创空间", "SYAOJ0000": "科改示范企业", "SYAOJ0001": "市级科改示范企业", "SYAOJ0002": "省级科改示范企业",
    "SYAOJ0003": "国家级科改示范企业", "SYAP10001": "起草{}个国家标准", "SYAP10002": "起草{}个行业标准", "SYAP10003": "起草{}个地方标准",
    "SYAP10004": "起草{}个企业标准", "SYAP10005": "起草{}个团体标准", "SYAQ10001": "{}个国家级奖励", "SYAQ10002": "{}个省级奖励",
    "SYAQ10003": "{}个市级奖励", "SYAR10000": "{}个专利", "SYAR20001": "{}个软件著作权", "SYAR20002": "{}个作品著作权",
    "SYAR30000": "{}个网站备案", "SYAR40000": "{}个商标", "SYAS10000": "国资私募管理人", "SYAS20000": "外资私募管理人", "SYAS30000": "有外资参与",
    "SYAS40000": "有上市公司参与",
    "SYAS70000": "战略新兴行业", "SYAT10000": "中基协注销机构", "SYAV10000": "行业规范准入企业", "HK30000": "香港公司", "HK31001": "香港-注册非香港公司",
    "HK31002": "香港-有限责任合伙", "HK31003": "香港-根据特别条例成立的法人团体", "HK31004": "香港-曾登记押记的非注册海外法团", "HK31005": "香港-互惠基金",
    "HK31006": "香港-非注册公司", "HK31007": "香港-开放式基金型公司", "HK31008": "香港-注册受托人法团", "HK31009": "香港-私人股份有限公司",
    "HK31010": "香港-公众无限公司", "HK31011": "香港-公众股份有限公司", "HK31012": "香港-私人无限公司", "HK31013": "香港-担保有限公司",
    "SYAX11000": "香港证券公司", "SYAX11001": "牌照-证券交易", "SYAX11002": "牌照-期货合约交易", "SYAX11003": "牌照-杠杆式外汇交易",
    "SYAX11004": "牌照-就证券提供意见", "SYAX11005": "牌照-就期货合约提供意见", "SYAX11006": "牌照-就机构融资提供意见", "SYAX11007": "牌照-提供自动化交易服务",
    "SYAX11008": "牌照-提供证券保证金融资", "SYAX11009": "牌照-提供资产管理", "SYAX11010": "牌照-提供信贷评级服务", "SYAY10000": "国内投资机构",
    "SYAZ10000": "事业单位", "SYBA10000": "谱系", "SYBB10000": "社会组织", "SYBB10001": "社会组织-慈善组织", "SYBC10000": "拥有科技成果",
    "SYBC20000": "拥有地方科技成果", "SYBC30000": "拥有国家科技成果"
}


class CompanyCategory(ExcelBase):
    """
    查询公司标签
    输入：[name,...]
    输出：{
        name:{
            company_code,
            st_name,
            cname,
            category
            }
        }
    """

    def __init__(self):
        super(CompanyCategory, self).__init__()

    def process(self, names):
        result = defaultdict()
        for idx in range(0, len(names), 50):
            name_list = names[idx:idx + 50]
            items = self.query_company_category(name_list)
            for item in items:
                name = item["cname"]
                result[name] = item
        return result

    def query_company_category(self, names):
        query_schema = {
            "db_name": "category_data",
            "collection_name": "company_category",
            "query_condition": {"cname": {"$in": names}, "is_valid": True},
            "query_field": {"company_code": 1, "st_name": 1, "_id": 0,
                            "category": 1, "cname": 1}}
        query_result = self._data_server.call("query_item", query_schema) or list()
        return query_result
