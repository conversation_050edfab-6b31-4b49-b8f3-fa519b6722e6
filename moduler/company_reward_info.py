# -*- encoding:utf-8 -*-
# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
#
# <AUTHOR> <EMAIL>
# Date:   2018-11-15

from elasticsearch import Elasticsearch

from cfg.config import NODE_CFGS, es_user, es_pwd
from core.excel_base import ExcelBase

"""
政府奖励
输入：[company_name...]
输出：dict{
        company_name:[
            {'category': ['ACompany'], 
            'alias_name_list': ['瑞鹄汽车模具有限公司', '瑞鹄汽车模具股份有限公司'], 
            'source_category': '2', 
            'period': '2018', 
            'reward_name': '2018年安徽省数字化车间', 
            'cname': '瑞鹄汽车模具股份有限公司', 
            'reward_project': '轻量化车身成型模具数字化车间', 
            'reg_capital': 18360.0, 
            'area_info': {'province_id': '340000', 'city_id': '340200', 'province': '安徽省', 'city': '芜湖市'}, 
            'area_category': '省级', 
            'source': '安徽省经济和信息化委员会', 
            'area': '安徽省', 
            'companyName': '瑞鹄汽车模具股份有限公司'}
            ...
            ]
        }
"""


class Reward(ExcelBase):
    def __init__(self):
        super(Reward, self).__init__()
        self.__es = Elasticsearch(
            NODE_CFGS, http_auth=(es_user, es_pwd))
        self.__es_index = "company_reward_v2"
        self.__es_type = "reward"

    def process(self, name_list):
        reward_dict = dict()
        for name in name_list:
            reward_info = self.get_reward_info(name)
            if reward_info:
                for info in reward_info:
                    area_category = info.get("area_category", "")
                    if area_category == "0":
                        info["area_category"] = ""
                    if area_category == "1":
                        info["area_category"] = "国家级"
                    if area_category == "2":
                        info["area_category"] = "省级"
                    if area_category == "3":
                        info["area_category"] = "市级"
                    info["companyName"] = name
                reward_dict[name] = reward_info
        return reward_dict

    def get_reward_info(self, company_name):
        # for company_name in self.company_list:
        search_body = self.get_search_body(company_name)
        query_result = self.__es.search(
            index=self.__es_index,
            doc_type=self.__es_type,
            body=search_body)
        result_list = self.transform_es_result(query_result)
        return result_list

    def get_search_body(self, cname):
        search_body = {
            "size": 4000,
            "query": {"bool": {"must": [
                {"term": {
                    "alias_name_list.raw": {
                        "value": cname}}}]}}}
        return search_body

    def transform_es_result(self, results):
        result_list = list()
        raw_data_list = results.get("hits", {}).get("hits", [])
        if not raw_data_list:
            return result_list
        for raw_data in raw_data_list:
            value_data = raw_data.get("_source")
            if not value_data:
                continue
            result_list.append(value_data)
        return result_list


if __name__ == '__main__':
    p = Reward()
    print(p.process(["北京龙图通信息技术有限公司"]))
