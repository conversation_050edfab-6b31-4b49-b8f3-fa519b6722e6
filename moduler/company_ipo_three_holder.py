# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/04/21
from collections import defaultdict

from core.excel_base import ExcelBase


class CompanySBIPOHolder(ExcelBase):
    """三板IPO股东数据(证监会渠道披露的股东)"""
    """
    {
        company:[
            {
            shHolderName:股东名称
            holderRTO:持股比例
            compName:公司名称
            holderamt:持股数(万股)
            limitHolderamt:有限售持股数(万股)
            unlimHolderamt:无限售持股数(万股)
            curchg:本期持股变动数(万股)
            endDate:日期
            }
        ...
        ]
    }
    """
    def __init__(self):
        super(CompanySBIPOHolder, self).__init__()

    def process(self, name):
        last_item = self.query_last_data(name)
        if not last_item:
            return {}
        data_dict = self.query_mysql_data(last_item, name)
        return data_dict

    def query_mysql_data(self, last_item, name):
        result = defaultdict(list)
        sql = """SELECT nt.SHName AS shHolderName, CONCAT(ROUND(nt.HoldingRatioEnd*100,2), '%') AS holderRTO,
        ROUND(nt.HoldSumEnd/10000,2) AS holderamt, ROUND(nt.`HoldChange`/10000,2) AS curchg,
        ROUND(nt.UnRestriHoldSum/10000,2) AS unlimHolderamt, ROUND(nt.`RestriHoldSum`/10000,2) AS limitHolderamt,
        DATE_FORMAT(nt.`EndDate`,'%Y-%m-%d') AS endDate
        FROM`nq_top10sh` nt WHERE nt.`CompanyCode` ="{companyCode}"
        AND DATE_FORMAT(nt.endDate, '%Y-%m-%d') = "{endDate}" ORDER BY nt.SH ASC 
        """
        query_schema = dict(db_key="db_seeyii_128", sql_statement=sql.format(**last_item))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result[name].append(item)
        return result

    def query_last_data(self, name):
        sql = """
        SELECT max(DATE_FORMAT(a.endDate, '%Y-%m-%d')) as endDate, a.companyCode as companyCode, b.CompanyName
        from nq_top10sh as a JOIN  `sq_sk_basicinfo`as b where a.companyCode=b.companyCode and 
        b.CompanyName in ({}) and b.setype='103' GROUP BY a.companyCode"""
        names = [name]
        if "（" in name:
            names += [name.replace("（", "(").replace("）", ")").strip()]
        name_str = ",".join(['{!r}'.format(i) for i in names])
        query_schema = dict(db_key="db_seeyii_128", sql_statement=sql.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            return item


if __name__ == '__main__':
    p = CompanySBIPOHolder()
    print(p.process("好买财富管理股份有限公司"))