# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/11
from collections import defaultdict

from core.excel_base import ExcelBase
from utils.log_util import create_logger

LOGGER = create_logger("TEST", "qiyuan_data.log")


class CompanyIndustryAndCYL(ExcelBase):
    """
    公司行业及产业链数据
    输入：[name，...]
    输出：{
        name:[
            {
            compName:公司名称，
            industryId：视野二级行业id，
            IndustryName：视野二级行业名称，
            cyl_name:产业链1,产业链2 ...
            }
                ]
            ...
        }
    """

    def __init__(self):
        super(CompanyIndustryAndCYL, self).__init__()
        self.cyl_map = self.query_china_data()

    def process(self, names):
        result = defaultdict(list)
        for idx in range(0, len(names), 50):
            name_list = names[idx:idx + 50]
            ind_info = self.query_cmp_industry(name_list)
            for item in ind_info:
                name = item["compName"]
                result[name].append(item)
        return result

    def query_cmp_industry(self, names):
        sql_statement = """
        SELECT compName, industryId, IndustryName FROM `db_seeyii`.`sq_comp_all_industry` f WHERE f.compname in ({})
        UNION
        SELECT companyname as compName, industryId, IndustryName FROM 
        `sq_sk_basicinfo` a, sq_comp_industry b  WHERE  a.secucode=b.secucode and  a.companyname in ({});
        """
        name_str = ','.join(['{!r}'.format(name) for name in names])
        query_schema = dict(db_key="db_seeyii_128", sql_statement=sql_statement.format(name_str, name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            ind_id = item["industryId"]
            cyl_name_set = self.cyl_map.get(ind_id)
            if cyl_name_set:
                item["cyl_name"] = ",".join(list(cyl_name_set))
        return result_list

    def query_china_data(self):
        result = dict()
        sql_statement = """
        SELECT b.industryId as second_id, a.name AS cyl_name 
        from sq_oa_chain a LEFT JOIN sq_oa_chain_industry b on a.id=b.chainId"""
        query_schema = dict(db_key="db_seeyii_128", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            second_id = item["second_id"]
            cyl_name = item["cyl_name"]
            result.setdefault(second_id, set())
            result[second_id].add(cyl_name)
        return result


if __name__ == '__main__':
    p = CompanyIndustryAndCYL()
    p.process()
