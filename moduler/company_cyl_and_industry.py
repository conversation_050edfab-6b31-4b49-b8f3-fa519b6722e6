# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/11
from collections import defaultdict

from core.excel_base import ExcelBase


class ChinaToExcel(ExcelBase):
    """
    产业链数据
    输入：产业链名称list
    输出：[
            'cyl_name': ('产业链名称', 0),
            "second_industry_name": 1,
            "second_industry_id": 1,
            "first_industry_name": 1,
            "first_industry_id": 1
            company_name:公司名称
        ]

        SELECT c.name AS c_name,  b.industryId as second_id, b.industryName,b.compName
        from sq_oa_chain_industry a, sq_comp_all_industry  b, sq_oa_chain c
        where a.industryId=b.industryId and c.id=a.chainId and c.name='汽车制造产业链'
    """

    def __init__(self):
        super(ChinaToExcel, self).__init__()
        self._excel_name = self.name_add_date("新能源汽车产业链 数据.xlsx")
        self.field_cfg = {
            'cyl_name': ('产业链名称', 0),
            'second_industry_name': ('二级行业', 1),
            'company_name': ('公司名称', 2)}

    def process(self, china):
        result = list()
        for c_name, ind_set in self.query_china_data(china).items():
            for ind in ind_set:
                items = self.query_industry_data(ind)
                for item in items:
                    item["cyl_name"] = c_name
                result.extend(items)
        # self.save_to_excel(self.field_cfg, {"sheet1": result})
        return result

    def query_china_data(self, china):
        result = defaultdict(set)
        sql_statement = """
	    SELECT b.industryId as second_id, b.chainId, a.name AS c_name 
	    from sq_oa_chain a LEFT JOIN sq_oa_chain_industry b on a.id=b.chainId
	     where  a.name in ({});"""
        name_str = ','.join(['{!r}'.format(name) for name in china])
        query_schema = dict(db_key="db_seeyii_128", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            c_name = item["c_name"]
            second_id = item["second_id"]
            result[c_name].add(second_id)
        return result

    # def query_industry_data(self, _id):
    #     sql_statement = """
    #             SELECT compName, industryId, IndustryName FROM `db_seeyii`.`sq_comp_all_industry` f
    #             WHERE f.industryId='{}'
    #             """
    #     query_schema = dict(db_key="db_seeyii_128", sql_statement=sql_statement.format(_id, _id))
    #     result_list = self._data_server.call("query_sql_item", query_schema) or list()
    #     return result_list

    def query_industry_data(self, _id):
        result = list()
        query_schema = {
            "db_name": "industry_data",
            "collection_name": "company_industry",
            "query_condition": {"second_industry_id": _id, "is_valid": 1},
            "query_field": {"company_name": 1, "_id": 0,
                            "second_industry_name": 1,
                            "second_industry_id": 1,
                            "first_industry_name": 1,
                            "first_industry_id": 1}}
        query_result = self._data_server.call("query_item", query_schema) or list()
        for item in query_result:
            if ("second_industry_name" not in item
                    or "second_industry_id" not in item):
                continue
            result.append(item)
        return result


if __name__ == '__main__':
    cyl = ["新能源汽车产业链"]
    p = ChinaToExcel()
    print(len(p.process(cyl)))
