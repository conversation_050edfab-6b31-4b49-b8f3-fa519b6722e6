# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/11
from collections import defaultdict
from core.excel_base import ExcelBase


class CompanyInCmpIndustry(ExcelBase):
    """
    公司行业
    输入：[name，...]
    输出：{
        name:[
            {
            "company_name": 1,
            "second_industry_name": 1,
            "second_industry_id": 1,
            "first_industry_name": 1,
            "first_industry_id": 1,
            }
                ]
            ...
        }
    """

    def __init__(self):
        super().__init__()

    def process(self, names):
        result = defaultdict(list)
        for idx in range(0, len(names), 50):
            name_list = names[idx:idx + 50]
            ind_info = self.query_cmp_industry(name_list)
            for item in ind_info:
                name = item["company_name"]
                result[name].append(item)
        return result

    def query_cmp_industry(self, names):
        result = list()
        query_schema = {
            "db_name": "industry_data",
            "collection_name": "company_industry",
            "query_condition": {"company_name": {"$in": names}, "is_valid": 1},
            "query_field": {"company_name": 1, "_id": 0,
                            "second_industry_name": 1,
                            "second_industry_id": 1,
                            "first_industry_name": 1,
                            "first_industry_id": 1,
                            "is_valid": 1}}
        query_result = self._data_server.call("query_item", query_schema) or list()
        for item in query_result:
            if "second_industry_name" not in item or "second_industry_id" not in item or item["is_valid"] == 0:
                continue
            result.append(item)
        return result


if __name__ == '__main__':
    p = CompanyInCmpIndustry()
    p.process()
