# -*- encoding:utf-8 -*-
# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
#
# <AUTHOR> <EMAIL>
# Date:   2018-10-10

from collections import defaultdict
from elasticsearch.helpers import scan
from elasticsearch import Elasticsearch

from cfg.config import NODE_CFGS, es_user, es_pwd
from core.excel_base import ExcelBase


"""
起草标准
输入：[company_name...]
输出：dict{
        company_name:[
            {'companyName': '深圳市海目星激光智能装备股份有限公司'
            'category': ['ACompany'], 
            'permition_id': 'T/SCSS 010-2017', 
            'draft_org': '深圳市海目星激光智能装备股份有限公司', 
            'publish_date': '2017-01-01', 
            'detail_id': 'eyJxdWVyeV9jb25kaXRpb24iOiB7InN0YW5kYXJkX25hbWUiOiAiXHU2NjdhXHU2MTY3XHU1N2NlXHU1ZTAyXHU2NjdhXHU2MTY3XHU1ZGU1XHU1MzgyXHU1ZWZhXHU4YmJlXHU2MzA3XHU1MzU3IiwgInBlcm1pdGlvbl9pZCI6ICJUL1NDU1MgMDEwLTIwMTcifSwgImNvbGxlY3Rpb24iOiAiY29tcGFueV9zdGFuZGFyZF9pbmZvX2dyb3VwIn0=', 
            'alias_name_list': ['深圳市海目星激光科技有限公司', '深圳市海目星激光智能装备股份有限公司'], 
            'standard_type': '团体标准', 
            'standard_name': '智慧城市智慧工厂建设指南', 
            'area': {'province_id': '440000', 'city_id': '440300', 'province': '广东省', 'city': '深圳市'}, 
            'publish_year': 2017, 
            'team_name': '深圳市智慧城市研究会', 
            'reg_capital': 20000.0},
            ...
            ]
        }
"""


class StandardInfo(ExcelBase):
    def __init__(self):
        super(StandardInfo, self).__init__()
        self.__es = Elasticsearch(
            NODE_CFGS, http_auth=(es_user, es_pwd))
        self.__es_index = "company_standard_v2"
        self.__es_type = "company_standard"

    def process(self, names):
        standard_info_dict = dict()
        for name in names:
            standard_info = self.query_standard_info(name)
            if standard_info:
                for info in standard_info:
                    info["companyName"] = name
                standard_info_dict[name] = standard_info
        return standard_info_dict

    def query_standard_info(self, cname):
        search_query = {
            "query": {
                "term": {
                    "alias_name_list.raw": {
                        "value": cname
                    }
                }
            }
        }
        results = scan(self.__es, search_query, index=self.__es_index, doc_type=self.__es_type, scroll="1000m",
                       timeout="100m")
        if not results:
            return None
        result_list = list()
        for result in results:
            if result:
                raw_data_dict = result.get("_source", {})
                result_list.append(raw_data_dict)
            else:
                continue
        return result_list


if __name__ == '__main__':
    p = StandardInfo()
    print(p.process(["北京龙图通信息技术有限公司"]))
