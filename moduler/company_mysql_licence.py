# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/25
from core.excel_base import ExcelBase


class MysqlLicence(ExcelBase):
    """
    输入：[name...]
    输出：{
        name:[
            {
            'compName': ('公司名称', 0),
            'licenceBeginDate': ('生效日期', 1),
            'licenceCategory': ('证照分类', 2),
            'licenceType': ('证照类型', 3),
            'publishOrg': ('发布机关', 4),
            'details': ('证照详情', 5),
            }
        ]
    }
    """

    def __init__(self):
        super(MysqlLicence, self).__init__()

    def process(self, names):
        result = dict()
        for idx in range(0, len(names), 50):
            name_list = names[idx:idx + 50]
            result.update(self.query_licence_data(name_list))
        return result

    def query_licence_data(self, names):
        result = dict()
        name_str = "','".join(names)
        sql_statement = """SELECT * from dwd_me_buss_perm where dataStatus !=3 and compName in ('{}');"""
        query_schema = dict(db_key="db_seeyii", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result.setdefault(item["compName"], list())
            result[item["compName"]].append(item)
        return result
