# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/02/22
from core.excel_base import ExcelBase


class CompanyStaffData(ExcelBase):
    """
    工商高管
    name:[
        {
        'cmpName': ('公司名称', 0),
        'staffName': ('高管名称', 1),
        'position': ('职务', 2),
        }
    ]
    """
    def __init__(self):
        super(CompanyStaffData, self).__init__()

    def process(self, names):
        result = dict()
        for idx in range(0, len(names), 50):
            name_list = names[idx:idx + 50]
            result.update(self.query_mysql_data(name_list))
        return result

    def query_mysql_data(self, names):
        result = dict()
        name_str = "','".join(names)
        sql_statement = """SELECT a.cName as staffName, b.compName as cmpName, b.staffName as position 
                FROM `sy_cd_ms_hum_gs_person_info` a, `sy_cd_ms_staf_gs_comp_staf` b 
                WHERE a.personalCode = b.personalCode AND b.compName in ('{}');"""
        query_schema = dict(db_key="tidb_135", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result.setdefault(item["cmpName"], list())
            result[item["cmpName"]].append(item)
        return result


if __name__ == '__main__':
    p = CompanyStaffData()
    p.process([])
