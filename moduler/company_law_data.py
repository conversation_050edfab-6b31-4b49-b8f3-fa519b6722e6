# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/02/19
import base64
from core.excel_base import ExcelBase


class CompanyLawData(ExcelBase):
    def __init__(self):
        super(CompanyLawData, self).__init__()
        self.db_key = ""
        self.sql = ""

    def process(self, names):
        pass

    def query_mysql_data(self, names):
        name_str = "','".join(names)
        sql_statement = self.sql
        query_schema = dict(db_key=self.db_key, sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


class CompanyCPWS(CompanyLawData):
    """
    法律诉讼|裁判文书

    'plaintiff': ('原告/上诉人', 0),
    'plaintiffs': ('原告们', 1),
    'defendant': ('被告/被上诉人', 2),
    'defendants': ('被告们', 3),
    'thirdParty': ('第三人', 4),
    'thirdParties': ('第三人们', 5),
    'caseId': ('案号', 6),
    'caseType': ('案件类型', 7),
    'documentType': ('文书类型', 8),
    'provinceCode': ('省份编码', 9),
    'lawCourt': ('法院', 10),
    'judgeTime': ('裁判日期', 11),
    'submitTime': ('发布时间', 12),
    'documentTitle': ('文书标题', 13),
    'documentHead': ('文书首部', 14),
    'documentText': ('文书正文', 15),
    'documentTail': ('文书尾部', 16),
    'partiesInfo': ('当事人们信息', 17),
    'lawsuitRecord': ('申诉记录', 18),
    'caseFact': ('案件事实', 19),
    'judgeReason': ('判决理由', 20),
    'judgeConclusion': ('判决结论', 21),
    'lawCaseReason': ('案由', 22),
    'trialProcedure': ('审理程序', 23),
    'lawBasis': ('法律依据', 24),
    """

    def __init__(self):
        super(CompanyCPWS, self).__init__()
        self.db_key = "tidb_135"
        self.sql = """
        SELECT 'pla',a.plaintiff,a.plaintiffs,a.defendant,a.defendants,a.thirdParty,a.thirdParties,b.* FROM sy_cd_me_lega_suso_anay as a JOIN sy_cd_me_lega_suso as b on 
        a.lawsuitId = b.sourceId WHERE a.plaintiff in ('{}')  AND a.dataStatus != 3 AND b.dataStatus != 3
         UNION 
        SELECT 'def',a.plaintiff,a.plaintiffs,a.defendant,a.defendants,a.thirdParty,a.thirdParties,b.* FROM sy_cd_me_lega_suso_anay as a JOIN sy_cd_me_lega_suso as b on 
        a.lawsuitId = b.sourceId WHERE a.defendant in ('{}')  AND a.dataStatus != 3 AND b.dataStatus != 3
         UNION 
        SELECT 'thi',a.plaintiff,a.plaintiffs,a.defendant,a.defendants,a.thirdParty,a.thirdParties,b.* FROM sy_cd_me_lega_suso_anay as a JOIN sy_cd_me_lega_suso as b on 
        a.lawsuitId = b.sourceId WHERE a.thirdParty in ('{}')  AND a.dataStatus != 3 AND b.dataStatus != 3 
        """
        self.field_map = {
            "pla": "plaintiff",
            "def": "defendant",
            "thi": "thirdParty",
        }

    def process(self, names):
        result = dict()
        for idx in range(0, len(names), 5):
            name_list = names[idx:idx + 5]
            data_list = self.query_mysql_data(name_list)
            for item in data_list:
                doc = item.get("documentText")
                if doc:
                    try:
                        n_doc = self.base64_decode(doc)
                        item["documentText"] = n_doc
                    except:
                        pass
                for field_name in ['pla', 'def', 'thi']:
                    if field_name in item:
                        name = item[self.field_map[field_name]]
                        result.setdefault(name, list())
                        result[name].append(item)
        return result

    def query_mysql_data(self, names):
        name_str = "','".join(names)
        sql_statement = self.sql
        query_schema = dict(db_key=self.db_key, sql_statement=sql_statement.format(name_str, name_str, name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list

    @staticmethod
    def base64_decode(_str):
        str_url = base64.b64decode(_str).decode("utf-8")
        return str_url


class CompanyFYGG(CompanyLawData):
    """
    法院公告

     name:[
        {
        'party': ('当事人', 0),
        'court': ('公告人', 1),
        'punishDetail': ('案由', 2),
        'noticeType': ('公告类型', 3),
        'content': ('案件内容', 4),
        'ctimeDate': ('公示宣布日', 5),
      },
      ...
      ]
    """

    def __init__(self):
        super(CompanyFYGG, self).__init__()
        self.db_key = "seeyii_assets_database"
        self.sql = """
         SELECT party,court,punishDetail,noticeType,content,ctimeDate
         FROM sy_cd_me_lega_fago_v2 WHERE dataStatus != 3 AND party IN ('{}') 
        """

    def process(self, names):
        result = dict()
        for idx in range(0, len(names), 10):
            name_list = names[idx:idx + 10]
            data_list = self.query_mysql_data(name_list)
            for item in data_list:
                name = item["party"]
                result.setdefault(name, list())
                result[name].append(item)
        return result


class CompanyKTGG(CompanyLawData):
    """
    开庭公告

    name:[
      {
        'party': ('当事人（公示对象）', 0),
        'eventTime': ('开庭日期', 1),
        'plaintiff': ('原告', 2),
        'defendant': ('被告', 3),
        'court': ('法院', 4),
        'caseReason': ('案由', 5),
        'courtroom': ('法庭', 6),
        'contractors': ('承办部门', 7),
        'planDate': ('排期日期', 8),
        'caseNo': ('案号', 9),
        'judge': ('审判长', 10),
        'content': ('公告内容', 11),
          },
      ...
      ]
    """

    def __init__(self):
        super(CompanyKTGG, self).__init__()
        self.db_key = "tidb_135"
        self.sql = """
         SELECT id,party,eventTime,plaintiff,defendant,court,caseReason,
        courtroom,contractors,planDate,caseNo,judge,content 
        FROM sy_cd_me_lega_kati WHERE dataStatus != 3 AND party IN ('{}')
        """

    def process(self, names):
        result = dict()
        for idx in range(0, len(names), 10):
            name_list = names[idx:idx + 10]
            data_list = self.query_mysql_data(name_list)
            for item in data_list:
                name = item["party"]
                result.setdefault(name, list())
                result[name].append(item)
        return result
