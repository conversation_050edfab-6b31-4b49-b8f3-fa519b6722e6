# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/11
from core.excel_base import ExcelBase


class InIndustry(ExcelBase):
    """
    产业链数据
    输入：行业名list
    输出：[
        {
           "company_name": 1,
            "second_industry_name": 1,
            "second_industry_id": 1,
            "first_industry_name": 1,
            "first_industry_id": 1
        },
        ...
        ]
    """

    def __init__(self):
        super(InIndustry, self).__init__()

    def process(self, ind_list):
        result = list()
        for idx in range(0, len(ind_list), 10):
            inds = ind_list[idx: idx + 10]
            result_list = self.query_cmp_industry(inds)
            result.extend(result_list)
        return result

    def query_cmp_industry(self, names):
        result = list()
        query_schema = {
            "db_name": "industry_data",
            "collection_name": "company_industry",
            "query_condition": {"second_industry_name": {"$in": names}, "is_valid": 1},
            "query_field": {"company_name": 1, "_id": 0,
                            "second_industry_name": 1,
                            "second_industry_id": 1,
                            "first_industry_name": 1,
                            "first_industry_id": 1,
                            "is_valid": 1}}
        query_result = self._data_server.call("query_item", query_schema) or list()
        for item in query_result:
            if "second_industry_name" not in item or "second_industry_id" not in item or item["is_valid"] == 0:
                continue
            result.append(item)
        return result


if __name__ == '__main__':
    p = InIndustry()
    p.process()
