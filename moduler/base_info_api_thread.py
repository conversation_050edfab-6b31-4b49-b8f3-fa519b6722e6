# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2020/12/21
import json
import http.client
import traceback
import requests
import time

from core.excel_base import ExcelBase
from core.thread_base import BaseMaster, BaseWorker


class BaseInfoMaster(BaseMaster, ExcelBase):
    """
    输入: [company_name...]
    输出：dict{
            companyName:{
                'companyName': '振华石油控股有限公司',
                'legal_person_name': '刘雨濛',
                'url': 'https://www.seeyii.com/',
                'base': 'bj',
                'first_industry': [{'industry_id': 15, 'industry_name': '金融'}],
                'company_id': 35119283,
                'reg_status': '存续（在营、开业、在册）',
                'from_time': '2014-11-18',
                'second_industry': [{'industry_id': 1242, 'industry_name': '金融数据平台'}],
                'establish_date': '2014-11-18',
                'estiblish_time': '2014-11-18',
                'reg_location': '北京市海淀区知春路甲48号2号楼14层2-6-17C',
                'reg_address': '北京市海淀区知春路甲48号2号楼14层2-6-17C',
                'reg_number': '110108018167349',
                'to_time': '2064-11-17',
                'business_scope': '金融信息服务（未经许可不得开展金融业务）；技术开发、技术推广、技术转让、技术咨询、技术服务；软件开发；应用软件服务；销售计算机、软件及辅助设备；企业管理咨询；企业策划；经济贸易咨询；会议服务；承办展览展示活动；市场调查；数据处理（数据处理中的银行卡中心、PUE值在1.5以上的云计算数据中心除外）；基础软件服务；组织文化艺术交流活动（不含营业性演出）；设计、制作、代理、发布广告；经营电信业务。（市场主体依法自主选择经营项目，开展经营活动；以及依法须经批准的项目，经相关部门批准后依批准的内容开展经营活动；不得从事国家和本市产业政策禁止和限制类项目的经营活动。）',
                'company_org_type': '有限责任公司(自然人投资或控股)',
                'taxpapyer_number': '91110108*********X',
                'org_number': '*********',
                'english_name': 'Beijing Shiye Jinfu Financial Services Information Technology Co.,Ltd.',
                'reg_capital': '356.81万人民币',
                'credit_code': '91110108*********X',
                'main_staff': [{'position': '监事', 'name': '邓连喜'},
                                {'position': '董事', 'name': '程鹏'},
                                {'position': '董事', 'name': '曲倩倩'},
                                {'position': '董事长,经理', 'name': '刘雨濛'},
                                {'position': '董事', 'name': '张灿'},
                                {'position': '董事', 'name': '朱海发'}]}
                'reg_province_code': '110000',
                'reg_province': '北京市',
                'reg_city_code': '110102',
                'reg_city': '西城区',
                'reg_district_code': '110102',
                'reg_district': '西城区'
                phone_number：联系电话
                postal_address：办公地址
                email：email
                company_business：业务亮点
                licence：证照
                company_nature：性质标签
                market：所属市场
            }
}
    """

    def __init__(self):
        super(BaseInfoMaster, self).__init__()
        self.task_queue = None
        self.signal_queue = None
        self.result_queue = None
        self.put_signal = "put"
        self.finish_signal = "finish"
        self.worker_n = 1
        self.worker = BaseInfoWorker

    def process(self, names):
        self.start_worker()
        # item_list = self.query_cmp_names()
        for name in names:
            self.allocating_task(name)
        self.wait_task_finish()
        # finish signal.
        self.allocating_finish_signal()

        result = dict()
        while True:
            try:
                data_dict = self.result_queue.get(timeout=20)
                result[data_dict["companyName"]] = data_dict
            except Exception as e:
                print(e)
                break
        print("base_info finish.")
        return result
        # self.save_data_excel(result)

    def query_cmp_names(self):
        sql = """
        SELECT cName as companyName,  regProvince, regCity, regDistrict 
        from dwd_ms_cn_sydw_base_info where regProvince='广西壮族自治区' and dataStatus!=3;"""
        query_schema = dict(db_key="seeyii_db", sql_statement=sql)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list

    def save_data_excel(self, result):
        field_cfg = {
            'companyName': ('公司名称', 0),
            'credit_code': ('统一信用代码', 1),
            'legal_person_name': ('法定代表人', 2),
            'reg_capital': ('注册资本', 3),
            'establish_date': ('成立时间', 4),
            'reg_status': ('注册状态', 5),
            'company_org_type': ('公司组织类型', 6),
            'reg_province': ('省', 7),
            'reg_city': ('市', 8),
            'reg_district': ('区', 9),
            'reg_location': ('注册地址', 10),
            'postal_address': ('办公地址', 11),
            'phone_number': ('联系电话', 12),
            'business_scope': ('经营范围', 13),
            'company_nature': ('性质标签', 14),
            'market': ('所属市场', 15),
            'reword': ('政府奖励', 16),
            'licence': ('专项证照', 17),
            'stand': ('起草标准', 18),
            'company_business': ('业务亮点', 19)}

        self._excel_name = self.name_add_date("基本信息POC数据.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})


class BaseInfoWorker(BaseWorker):
    def __init__(self, worker_name, task_queue, signal_queue, result_queue, data_server):
        super(BaseInfoWorker, self).__init__(worker_name, task_queue, signal_queue, result_queue, data_server)
        self.result_queue = result_queue
        self.task_queue = task_queue
        self.signal_queue = signal_queue
        self.finish_signal = "finish"
        self._data_server = data_server

    def process_task(self, name):
        try:
            item = dict()
            item["companyName"] = name
            # 年报信息
            rep_info = self.query_rep_info(name)
            phone_number = rep_info.get("content", dict()).get("phone_number", "")
            item["phone_number"] = phone_number
            postal_address = rep_info.get("content", dict()).get("postal_address", "")
            item["postal_address"] = postal_address
            email = rep_info.get("content", dict()).get("email", "")
            item["email"] = email
            # 工商信息
            base_info = self.query_gs_info(name) or dict()
            item.update(base_info)
            # 标签信息
            cat_dict = self.query_category_info(name)
            category_data = self.category_data_process(cat_dict)
            item.update(category_data)
            # 区域信息
            item.update(self.query_cmp_area(name) or dict())
            self.result_queue.put(item)
        except Exception as exc:
            err_msg = traceback.format_exc()
            err_msg = " ## ".join(err_msg.split("\n"))
            err_msg = " ## ".join([err_msg, str(exc)])
            print(err_msg)
        finally:
            self.signal_queue.get(timeout=10)

    def query_cmp_area(self, name):
        sql_statement = """SELECT provinceName as reg_province,  cityName as reg_city, 
        district as reg_district from sy_cd_ms_base_comp_geo 
        where compName='{}' and dataStatus !=3;"""
        query_schema = dict(db_key="tidb_135", sql_statement=sql_statement.format(name))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            return item

    @staticmethod
    def category_data_process(cat_dict):
        category_dict = dict()
        # 过滤掉 政府奖励 证照 起草标准 的标签
        # filter_cat = [
        #     "SYAP10001#", "SYAP10002#", "SYAP10003#", "SYAP10004#", "SYAM10000#",
        #     "SYAP10005#", "SYAQ10001#", "SYAQ10002#", "SYAQ10003#"]
        filter_cat = []
        for cat_key in ["company_nature", "market"]:
            category_dict.setdefault(cat_key, ','.join([i["name"] for i in cat_dict.get(cat_key, list())]))
        company_business = cat_dict.get("company_business", list())
        c_bus = list()
        for item in company_business:
            cat = item["category"]
            name = item["name"]
            if "SYAM10000" in cat:
                names = [i for i in name.split("-") if i != '证照']
                category_dict.setdefault("licence", list())
                category_dict["licence"].extend(names)

            if cat not in filter_cat:
                c_bus.append(name)
        category_dict["company_business"] = ','.join(c_bus)

        licence = category_dict.get("licence")
        if licence:
            category_dict["licence"] = ",".join(licence)
        return category_dict

    @staticmethod
    def query_rep_info(raw_name):
        result_list = list()
        headers = {"Content-type": "application/x-www-form-urlencoded",
                   "Accept": "text/plain"}
        httpClient = http.client.HTTPConnection("10.10.7.212", 9101, timeout=60)
        # httpClient = http.client.HTTPConnection("60.205.212.21", 9102, timeout=30)
        cookie, limit_num = None, 20
        num = 0
        while True:
            response_content = None
            try:
                request_1_1 = {
                    "user_id": "8c416bf9ccf4490da89cc318c54860a7",
                    "limit_num": limit_num,
                    "company": raw_name}
                if cookie is not None:
                    request_1_1["cookie"] = cookie
                params = json.dumps(request_1_1)
                httpClient.request(
                    "POST", "/v2.0/companies/background_info_add", params, headers)
                response = httpClient.getresponse()
                result = response.read().decode("utf-8")
                result = json.loads(result)
                if result["response_state"] != 1:
                    raise Exception("request err.param={}".format(params))
                response_content = result["response_content"]
                return response_content
            except Exception as exc:
                num += 1
                # print("#"*30 + "{}".format(num))
                err_msg = traceback.format_exc()
                err_msg = " ## ".join(err_msg.split("\n"))
                err_msg = " ## ".join([err_msg, str(exc)])
                if num > 3:
                    print(err_msg)
                    print("rep err !!" * 40)
                    break

        if httpClient is not None:
            httpClient.close()
        return result_list

    @staticmethod
    def query_category_info(raw_name):
        result_list = list()
        headers = {"Content-type": "application/x-www-form-urlencoded",
                   "Accept": "text/plain"}
        httpClient = http.client.HTTPConnection("10.10.128.185", 6226, timeout=30)
        # httpClient = http.client.HTTPConnection("60.205.212.21", 6226, timeout=30)
        cookie, limit_num = None, 20
        while True:
            try:
                request_1_1 = {
                    "user_id": "edcec33c8bdf48249cfc7985d39a39a3",
                    "cname": raw_name}
                if cookie is not None:
                    request_1_1["cookie"] = cookie
                params = json.dumps(request_1_1)
                httpClient.request(
                    "POST", "/v1/companys/category_info", params, headers)
                response = httpClient.getresponse()
                result = response.read().decode("utf-8")
                result = json.loads(result)
                if result["response_state"] != 1:
                    raise Exception("request err.param={}".format(params))
                response_content = result["response_content"]
                return response_content
            except Exception as exc:
                err_msg = traceback.format_exc()
                err_msg = " ## ".join(err_msg.split("\n"))
                err_msg = " ## ".join([err_msg, str(exc)])
                print(err_msg)
                break
        if httpClient is not None:
            httpClient.close()
        return result_list

    @staticmethod
    def query_gs_info(raw_name):
        result_list = list()
        headers = {"Content-type": "application/x-www-form-urlencoded",
                   "Accept": "text/plain"}
        httpClient = http.client.HTTPConnection("10.10.7.212", 9101, timeout=30)
        # httpClient = http.client.HTTPConnection("60.205.212.21", 9102, timeout=30)
        cookie, limit_num, num = None, 20, 0
        while True:
            try:
                request_1_1 = {
                    "user_id": "edcec33c8bdf48249cfc7985d39a39a3",
                    "limit_num": limit_num,
                    "company": raw_name}
                if cookie is not None:
                    request_1_1["cookie"] = cookie
                params = json.dumps(request_1_1)
                httpClient.request(
                    "POST", "/v2.0/companies/company_m_base_info", params, headers)
                response = httpClient.getresponse()
                result = response.read().decode("utf-8")
                result = json.loads(result)
                if result["response_state"] != 1:
                    raise Exception("request err.param={}".format(params))
                response_content = result["response_content"]
                return response_content
            except Exception as exc:
                num += 1
                err_msg = traceback.format_exc()
                err_msg = " ## ".join(err_msg.split("\n"))
                err_msg = " ## ".join([err_msg, str(exc)])
                if num > 3:
                    print(err_msg)
                    print("gs err !!" * 40)
                    break
                break
        if httpClient is not None:
            httpClient.close()
        return result_list


if __name__ == '__main__':
    p = BaseInfoMaster()
    p.process({})
