# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/25
from core.excel_base import ExcelBase


class MysqlStandar(ExcelBase):
    """
    输入：[name...]
    输出：{
        name:[
            {
                'draftOrg': ('起草单位', 0),
                'standardName': ('标准名称', 1),
                'standardType': ('标准类型', 2),
                'teamName': ('标准团体', 3),
                'relatedProducts': ('相关产品', 4),
                'publishDate': ('发布时间', 5),
                'details': ('详情', 6),
            }
        ]
    }
    """

    def __init__(self):
        super(MysqlStandar, self).__init__()

    def process(self, names):
        result = dict()
        for idx in range(0, len(names), 50):
            name_list = names[idx:idx + 50]
            result.update(self.query_mysql_data(name_list))
        return result

    def query_mysql_data(self, names):
        result = dict()
        name_str = "','".join(names)
        sql_statement = """SELECT * from dwd_me_buss_ip_stad where dataStatus !=3 and draftOrg in ('{}');"""
        query_schema = dict(db_key="db_seeyii", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result.setdefault(item["draftOrg"], list())
            result[item["draftOrg"]].append(item)
        return result
