# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/02/20
from core.excel_base import ExcelBase


class InvEvent(ExcelBase):
    """
    投资事件
    name:[
    {
        'subject': ('投资主体', 0),
        'compName': ('被投企业', 1),
        'chgType': ('投资类型', 2),
        'chgDate': ('投资日期', 3),
        'chgRatio': ('变更股比', 4),
        'chgAmount': ('投资数量', 5),
        'chgCash': ('投资金额', 6),
        'chgPrice': ('投资价格', 7),
    },
    ...]
    """

    def __init__(self):
        super(InvEvent, self).__init__()
        self.db_key = 'db_seeyii'
        self.inv_type = {
            101: "投资进入",
            201: "市场增持（A股）",
            301: "市场增持（三板）",
            401: "定增进入（A股）",
            501: "定增进入（三板）"}

    def process(self, names):
        result = dict()
        for idx in range(0, len(names), 100):
            name_list = names[idx:idx + 100]
            data_list = self.query_mysql_data(name_list)
            for item in data_list:
                name = item["subject"]
                inv_type = item.get("chgType")
                if inv_type not in {101, 201, 301, 401, 501}:
                    continue
                item["chgType"] = self.inv_type[inv_type]
                result.setdefault(name, list())
                result[name].append(item)
        return result

    def query_mysql_data(self, names):
        name_str = "','".join(names)
        sql_statement = """
        SELECT * from dws_me_trad_inv_event where subject in ('{}') and dataStatus!=3;
        """
        query_schema = dict(db_key=self.db_key, sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    p = InvEvent()
    p.process()
