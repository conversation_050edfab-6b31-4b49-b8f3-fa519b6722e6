# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/18
from core.excel_base import ExcelBase

"""
公司的地址信息
{
compName:{
      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
      `compName` varchar(255) NOT NULL COMMENT '公司名称',
      `address` varchar(128) DEFAULT NULL COMMENT '地址',
      `country` varchar(64) DEFAULT NULL COMMENT '所属国家',
      `provinceName` varchar(100) DEFAULT NULL COMMENT '省份名称',
      `provinceCode` varchar(20) DEFAULT NULL COMMENT '省份代码',
      `cityName` varchar(100) DEFAULT NULL COMMENT '城市名称',
      `cityCode` varchar(20) DEFAULT NULL COMMENT '城市代码',
      `district` varchar(100) DEFAULT NULL COMMENT '区县名称',
      `districtCode` varchar(20) DEFAULT NULL COMMENT '区县代码',
      `location` varchar(100) DEFAULT NULL COMMENT '坐标点',
      `sourceId` bigint(20) DEFAULT NULL COMMENT '同步标识',
      `dataStatus` int(4) DEFAULT NULL COMMENT '数据状态',
      `createTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `modifyTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
      }
}
"""


class CompanyArea(ExcelBase):
    def __init__(self):
        super(CompanyArea, self).__init__()

    def process(self, name_list):
        result = dict()
        for idx in range(0, len(name_list), 50):
            names = name_list[idx: idx + 50]
            data = self.query_cmp_area(names)
            result.update(data)
        return result

    def query_cmp_area(self, names):
        result = dict()
        name_str = "','".join(names)
        sql_statement = """SELECT b.compName,provinceName, provinceCode, cityName, cityCode, district, districtCode
            FROM `sy_cd_ms_base_comp_geo_new` a LEFT JOIN sy_cd_ms_base_gs_comp_info_new b
             ON a.compCode=b.compCode and a.dataStatus!=3
            WHERE b.compName in ('{}') and  b.dataStatus!=3"""
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result.setdefault(item["compName"], item)
        return result
