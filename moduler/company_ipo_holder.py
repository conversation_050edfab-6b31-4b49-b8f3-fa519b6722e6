# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/04/21
from collections import defaultdict

from core.excel_base import ExcelBase


class CompanyIPOHolder(ExcelBase):
    """
    IPO股东数据(证监会渠道披露的股东)
    {
        company:[
            {
            shHolderName:股东名称
            holderRTO:持股比例
            compName:公司名称
            holderamt:持股数(万股)
            limitHolderamt:有限售持股数(万股)
            unlimHolderamt:无限售持股数(万股)
            curchg:本期持股变动数(万股)
            endDate:日期
            }
        ...
        ]
    }
    """

    def __init__(self):
        super(CompanyIPOHolder, self).__init__()

    def process(self, name):
        last_item = self.query_last_data(name)
        if not last_item:
            return {}
        data_dict = self.query_mysql_data(last_item, name)
        return data_dict

    def query_mysql_data(self, last_item, name):
        result = defaultdict(list)
        sql = """SELECT f.shHolderName AS shHolderName, CAST(f.holderRTO AS DECIMAL(18,2))AS holderRTO, 
            CAST(f.holderamt/10000 AS DECIMAL(18,2)) AS holderamt, CAST(f.limitHolderamt/10000 AS DECIMAL(18,2)) AS limitHolderamt,
            CAST(f.unlimHolderamt/10000 AS DECIMAL(18,2)) AS unlimHolderamt, CAST(IFNULL(f.curchg/10000, 0.00) AS DECIMAL(18,2))AS curchg,
            DATE_FORMAT(f.endDate, '%Y-%m-%d') AS endDate 
            FROM sq_sk_shareholder f WHERE f.innerCode='{innerCode}' AND DATE_FORMAT(f.endDate, '%Y-%m-%d')='{endDate}' ORDER BY f.rank ASC
            """
        query_schema = dict(db_key="db_seeyii_128", sql_statement=sql.format(**last_item))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result[name].append(item)
        return result

    def query_last_data(self, name):
        sql = """
        SELECT max(DATE_FORMAT(a.endDate, '%Y-%m-%d')) as endDate, a.innerCode as innerCode, b.compName
        from sq_sk_shareholder as a JOIN  `sq_comp_info`as b where a.innerCode=b.innerCode and 
        b.compName in ({}) GROUP BY a.innerCode"""
        names = [name]
        if "（" in name:
            names += [name.replace("（", "(").replace("）", ")").strip()]
        name_str = ",".join(['{!r}'.format(i) for i in names])
        query_schema = dict(db_key="db_seeyii_128", sql_statement=sql.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            return item


if __name__ == '__main__':
    p = CompanyIPOHolder()
    print(p.process("青岛云路先进材料技术股份有限公司"))
