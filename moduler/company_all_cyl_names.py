# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/09
from collections import defaultdict
from core.excel_base import ExcelBase


class AllCyl(ExcelBase):
    """
    视野产业链划分的企业名单
    [
    {
        compName:公司名称，
        IndustryName：视野二级行业，
        cyl:产业链
    }
    。。。
    ]
    """

    def __init__(self):
        super(AllCyl, self).__init__()

    def process(self, *args, **kwargs):
        result = list()
        ind_dict = self.query_cyl_data()
        for ind_id, items in self.query_all_industry().items():
            cyl = ind_dict.get(ind_id)
            if not cyl:
                continue
            for item in items:
                item["cyl"] = ','.join(cyl)
                result.append(item)
        return result

    def query_all_industry(self):
        result = defaultdict(list)
        sql = """
         SELECT id, compName, industryId, IndustryName FROM sq_comp_all_industry
          WHERE id>'{}' order by id ASC limit 1000;"""
        for items in self._query_sql_iter_by_id(sql, "db_seeyii_128"):
            for item in items:
                ind_id = item["industryId"]
                result[ind_id].append(item)
        return result

    def query_cyl_data(self):
        result = dict()
        sql_statement = """
        SELECT b.industryId as second_id, a.name AS cyl_name 
        from sq_oa_chain a LEFT JOIN sq_oa_chain_industry b on a.id=b.chainId"""
        query_schema = dict(db_key="db_seeyii_128", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            second_id = item["second_id"]
            cyl_name = item["cyl_name"]
            result.setdefault(second_id, set())
            result[second_id].add(cyl_name)
        return result


if __name__ == '__main__':
    p = AreaCyl()
    p.run()
