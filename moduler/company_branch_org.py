# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/02/19
from core.excel_base import ExcelBase


class CompanyBranch(ExcelBase):
    """
    分支机构
    {name:{
        companyName：公司名称，
        branchName：分支机构，
        branchStatus：机构状态，
    }
    ...
    }
    """

    def __init__(self):
        super(CompanyBranch, self).__init__()

    def process(self, name_list):
        result = dict()
        for idx in range(0, len(name_list), 10):
            names = name_list[idx:idx + 10]
            data_list = self.branch_info(names)
            for item in data_list:
                is_valid = item["isValid"]
                if is_valid == 0:
                    continue
                name = item["companyName"]
                result.setdefault(name, list())
                result[name].append(item)
        return result

    def branch_info(self, company_list):
        name_str = "','".join(company_list)
        sql_statement = """SELECT companyName, branchName, branchStatus, isValid
         from branch where companyName in ('{}');"""
        query_schema = dict(db_key="relation_data", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    p = CompanyBranch()
    print(p.process(['西昌三峰环保发电有限公司']))
