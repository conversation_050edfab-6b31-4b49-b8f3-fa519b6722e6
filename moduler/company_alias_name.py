# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/05/12
from core.excel_base import ExcelBase


class CompanyAlias(ExcelBase):
    """查询公司别名
    输入：[name,...]
    输出：{
        name:cur_name,
        ...
    }
    """

    def __init__(self):
        super(CompanyAlias, self).__init__()

    def process(self, names):
        return self.query_cur_name(names)

    def query_cur_name(self, names):
        result = dict()
        for idx in range(0, len(names), 50):
            name_list = names[idx:idx + 50]
            query_schema = {
                "db_name": "raw_data",
                "collection_name": "company_alias_name",
                "query_condition": {'alias_name': {"$in": name_list}},
                "query_field": {"_id": 0, "alias_name": 1, "cur_name": 1}}
            query_result = self._data_server.call("query_item", query_schema) or list()
            for item in query_result:
                alias_name = item["alias_name"]
                cur_name = item["cur_name"]
                result.setdefault(alias_name, cur_name)
        return result


if __name__ == '__main__':
    p = CompanyAlias()
    p.process([])
