# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/02/19
from core.excel_base import ExcelBase


class CompanyMajorProject(ExcelBase):
    """
    重大在建项目
    {name:[
            {
             'ownerCompany': ('业主单位名称', 0),
                'projectName': ('项目名称', 1),
                'provinceArea': ('地区', 2),
                'publishDate': ('发布日期', 3),
                'industry': ('行业', 4),
                'period': ('建设周期', 5),
                'progress': ('进展阶段', 6),
                'amount': ('总投资金额（万元）', 7),
                'location': ('所在地', 8),
                'scale': ('建设内容及规模', 9),
                'content': ('项目简介', 10),
              },
      ...
      ]}
    """

    def __init__(self):
        super(CompanyMajorProject, self).__init__()

    def process(self, name_list):
        result = dict()
        for idx in range(0, len(name_list), 50):
            names = name_list[idx:idx + 50]
            data_list = self.ext_build_project(names)
            for item in data_list:
                name = item["ownerCompany"].replace("(", "（").replace(")", "）")
                item["ownerCompany"] = name
                result.setdefault(name, list())
                result[name].append(item)
        return result

    def ext_build_project(self, company_list):
        """
        重大在建项目
        :param company_list:
        :return:
        """
        name_str = ','.join(['{!r}'.format(name) for name in company_list] +
                            ['{!r}'.format(name.replace("（", "(").replace("）", ")"))
                             for name in company_list if '（' in name or '）' in name])
        sql_statement = """SELECT *  from ext_build_project where ownerCompany in ({});"""
        query_schema = dict(db_key="seeyii", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    p = CompanyMajorProject()
    print(p.process(['西昌三峰环保发电有限公司']))
