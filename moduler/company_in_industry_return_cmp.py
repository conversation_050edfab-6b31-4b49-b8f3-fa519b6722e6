# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/11
from core.excel_base import ExcelBase


class InIndustryReturnCMP(ExcelBase):
    """
    产业链数据
    输入：行业名list
    输出：[
        {
            'IndustryName': ('二级行业', 1),
            'compName': ('公司名称', 2)}
        },
        ...
        ]
    """
    def __init__(self):
        super(InIndustryReturnCMP, self).__init__()

    def process(self, ind_list):
        result = list()
        for idx in range(0, len(ind_list), 10):
            inds = ind_list[idx: idx + 10]
            result_list = self.query_cmp_industry(inds)
            result.extend(result_list)
        return result

    def query_cmp_industry(self, names):
        sql_statement = """
        SELECT compName, industryId, IndustryName FROM `db_seeyii`.`sq_comp_all_industry` f WHERE f.IndustryName in ({})
        UNION
        SELECT companyname as compName, industryId, IndustryName FROM 
        `sq_sk_basicinfo` a, sq_comp_industry b  WHERE  a.secucode=b.secucode and  b.IndustryName in ({});
        """
        name_str = ','.join(['{!r}'.format(name) for name in names])
        query_schema = dict(db_key="db_seeyii_128", sql_statement=sql_statement.format(name_str, name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    p = InIndustryReturnCMP()
    p.process()
