# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/11
from collections import defaultdict
from core.excel_base import ExcelBase

"""
输入：[names...]
输出：{
    name:[
        {
        name:公司名称，
        inv_name:被投公司
        holdRatio：持股比例
        }
    ]

}
"""


class CompanyInvest(ExcelBase):
    def __init__(self):
        super(CompanyInvest, self).__init__()

    def process(self, name_list):
        result = defaultdict(list)
        for idx in range(0, len(name_list), 100):
            names = name_list[idx:idx + 100]
            items = self.query_invest_data(names)
            for item in items:
                name = item["cname"]
                result[name].append(item)
        return result

    def query_invest_data(self, names):
        name_str = "','".join(names)
        sql_statement = """
        SELECT  IF(b.compName IS NOT NULL, b.compN<PERSON>, a.shName) AS cname, 
        IF(c.compName IS NOT NULL,c.compName, a.compName) AS inv_name, holdRatio
        FROM sy_cd_ms_sh_gs_shlist_new a 
        LEFT JOIN  sy_cd_ms_base_gs_comp_info_new b ON  a.shId=b.compCode AND b.dataStatus!=3
        LEFT JOIN sy_cd_ms_base_gs_comp_info_new c ON  a.compCode=c.compCode AND c.dataStatus!=3
        WHERE  a.isValid =1 AND a.dataStatus != 3 AND b.compName IN ('{}') 
        and a.infoSource=2 and (c.compName!='' or a.compName!='')"""
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    p = CompanyInvest()
    p.process()
