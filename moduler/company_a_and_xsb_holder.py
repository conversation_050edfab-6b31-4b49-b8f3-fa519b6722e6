# -*- encoding:utf-8 -*-
# <AUTHOR> <EMAIL>
# Date: 2021-04-12 09:56
from collections import defaultdict
from core.excel_base import ExcelBase

"""
A股三板十大股东
输入：[company_name...]
输出：{
    name:[
        {
        shareholder_name:股东，
        cname：公司名称，
        holdRatio：持股比例
        },
        ...
    ]

}
"""


class CompanyAandXsbShareholder(ExcelBase):
    def __init__(self):
        super(CompanyAandXsbShareholder, self).__init__()

    def process(self, name_list):
        result = defaultdict(list)
        for idx in range(0, len(name_list), 50):
            names = name_list[idx:idx + 50]
            items = self.a_and_xsb_shareholder(names)
            for item in items:
                name = item["cname"]
                result[name].append(item)
        return result

    def a_and_xsb_shareholder2(self, company_list):
        """
        A股三板十大股东
        :param company_list:
        :return:
        """
        query_schema = {
            "db_name": "base_data",
            "collection_name": "a_and_xsb_company_top10",
            "query_condition": {"cname": {"$in": company_list}},
            "query_field": {"cname": 1, "shareholder_name": 1, "_id": 0}}
        query_result = self._data_server.call("query_item", query_schema) or list()
        return query_result

    def a_and_xsb_shareholder(self, company_list):
        """
        工商股东
        :param company_list:
        :return:
        """
        name_str = ','.join(['{!r}'.format(name) for name in company_list])
        sql_statement = """
        SELECT IF(mddc.`changeField` IS NOT NULL,
                mddc.`changeField`,
                REPLACE(REPLACE(a.`SHHOLDERNAME`, '(', '（'), ')', '）'))
                    AS shareholder_name
            , REPLACE(REPLACE(b.companyName, '(', '（'), ')', '）')
                AS cname,
                a.holderRTO/100 as holdRatio
        FROM sq_sk_shareholder_new a
            LEFT JOIN sq_sk_basicinfo b ON a.innerCode = b.`innerCode`
            LEFT JOIN my_db_data_cleaning mddc
            ON mddc.`sourceField` = REPLACE(REPLACE(a.`SHHOLDERNAME`, '(',
                '（'), ')', '）')
        WHERE b.SETYPE = '101'
            AND b.LISTSTATUS = '1'
            AND b.ISVALID = '1'
            AND REPLACE(REPLACE(b.companyName, '(', '（'), ')', '）') in ({name_str})

        union

        SELECT * FROM (
            SELECT IF(mddc.`sourceField` IS NOT NULL ,
            mddc.`changeField`,REPLACE(
            REPLACE(nqt.SHName,'(','（'),')','）')) AS shareholder_name,
            REPLACE(REPLACE(nq.ChiName, '(', '（'), ')', '）') as cname,
            nqt.HoldingRatioEnd as holdRatio
            FROM `my_db_temp_new_top10sh` nqt 
            LEFT JOIN nq_secumain nq ON nq.companyCode = nqt.`CompanyCode` 
            LEFT JOIN my_db_data_cleaning mddc 
            ON mddc.`sourceField` = REPLACE(
            REPLACE(nqt.SHName,'(','（'),')','）') 
            WHERE  nq.`SecuCode` IS NOT NULL 
            AND nq.ListedState = '1' and nq.ListedSector ='3' and nq.SecuMarket='81'
            and REPLACE(REPLACE(nq.ChiName, '(', '（'), ')', '）') in ({name_str}) 
            ) t 
        WHERE shareholder_name IS NOT NULL
        """
        query_schema = dict(db_key="db_seeyii_128", sql_statement=sql_statement.format(name_str=name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    p = CompanyAandXsbShareholder()
    print(p.process(["中国铁建重工集团股份有限公司"]))
