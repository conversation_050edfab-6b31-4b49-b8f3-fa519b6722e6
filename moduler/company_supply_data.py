# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/25
from core.excel_base import ExcelBase


class CompanySupply(ExcelBase):
    """
    供应商数据
    输入：[name...]
    输出：{
            name:[
                    {
                   'cname': ('公司名称', 0),
                    'company_name': ('供应商名称', 1),
                    'relation': ('关系类型', 2),
                    'mark': ('供应商排名', 3),
                    'period': ('年度', 4),
                }
            ]
        }
    """

    def __init__(self):
        super(CompanySupply, self).__init__()

    def process(self, names):
        return self.supply_customer(names)

    # 供应商、销售客户
    def supply_customer(self, name_list):
        result, finger_id_set = dict(), set()
        for idx in range(0, len(name_list), 100):
            names = name_list[idx:idx + 100]
            for col in ["company_relation_supply_flow_supply",
                        "company_relation_supply_flow_customer"]:
                query_schema = {
                    "db_name": "relation_data_v2",
                    "collection_name": col,
                    "query_condition": {"cname": {"$in": names}},
                    "query_field": {"_id": 0, "company_name": 1, "cname": 1, "period": 1, "mark": 1}}
                query_result = self._data_server.call("query_item", query_schema) or list()
                for item in query_result:
                    mark = item.get("mark")
                    if col == "company_relation_supply_flow_supply":
                        rela = "供应商"
                        if mark:
                            item["mark"] = "第{}大供应商".format(mark)
                    else:
                        rela = "销售客户"
                        if mark:
                            item["mark"] = "第{}大销售客户".format(mark)

                    item["relation"] = rela
                    finger_id = self._gen_graph_id(item)
                    item["fingerId"] = finger_id
                    if finger_id in finger_id_set:
                        continue
                    finger_id_set.add(finger_id)
                    result.setdefault(item["cname"], list())
                    result[item["cname"]].append(item)
        return result
