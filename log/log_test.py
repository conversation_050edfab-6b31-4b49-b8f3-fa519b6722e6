# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2023/2/27

from logger import get_logger
import time


if __name__ == "__main__":
    logger = get_logger()
    print(id(logger))
    logger.info('我是info级别的log')
    logger.warning('我是warning级别的log')
    logger.error('我是error级别的log')
    time.sleep(2)
    logger = get_logger()
    logger.info('我是info级别的log')
    logger.warning('我是warning级别的log')
    logger.error('我是error级别的log')
    print(id(logger))
