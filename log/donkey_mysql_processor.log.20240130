MySQLDBMana<PERSON>, 30 Jan 2024 14:16:24 ERROR    mysql_engine.py (76)	####	(1370, "execute command denied to user '<PERSON><PERSON><PERSON><PERSON><PERSON>'@'39.155.212.%' for routine 'seeyii_assets_database.str'")

        select compCode, endDate, str(mark) as mark, operatingIncome,netProfit from sy_cd_ms_fin_nq_maindata 
        where dataStatus!=3 and mark in (1,2)  and endDate >"2020"
        
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1370, "execute command denied to user 'LiuQingPeng'@'39.155.212.%' for routine 'seeyii_assets_database.str'")
