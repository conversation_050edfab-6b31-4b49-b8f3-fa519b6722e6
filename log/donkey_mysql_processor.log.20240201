MySQLDBManager Thu, 01 Feb 2024 10:54:20 ERROR    mysql_engine.py (76)	####	(1142, "SELECT command denied to user 'LiuQing<PERSON><PERSON>'@'39.155.212.28' for table 'sy_cd_ms_base_sk_stock'")

        SELECT id, compCode,compName,if(listStatus=1,"上市", "非上市") as listStatus, dataStatus, "A股" as ty,listDate from sy_cd_ms_base_sk_stock where  
        id > 0 ORDER BY id ASC limit 1000;
        
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1142, "SELECT command denied to user 'LiuQingPeng'@'39.155.212.28' for table 'sy_cd_ms_base_sk_stock'")
MySQLDBManager Thu, 01 Feb 2024 10:54:22 ERROR    mysql_engine.py (76)	####	(1142, "SELECT command denied to user 'LiuQingPeng'@'39.155.212.28' for table 'sy_cd_ms_fin_sk_mainindex'")

        select compCode, endDate, reportType, mainBusiIncome, sgpmargin, netProfit * 10000 as netProfit from sy_cd_ms_fin_sk_mainindex 
        where dataStatus!=3 and reportType in (1,3)  and endDate >"2020" and endDate like "%1231%"
        
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1142, "SELECT command denied to user 'LiuQingPeng'@'39.155.212.28' for table 'sy_cd_ms_fin_sk_mainindex'")
MySQLDBManager Thu, 01 Feb 2024 10:54:22 ERROR    mysql_engine.py (76)	####	(1142, "SELECT command denied to user 'LiuQingPeng'@'39.155.212.28' for table 'sy_cd_ms_fin_nq_maindata'")

        select compCode, endDate, mark, operatingIncome,netProfit from sy_cd_ms_fin_nq_maindata 
        where dataStatus!=3 and mark in (1,2)  and endDate >"2020" and endDate like "%12-31%"
        
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1142, "SELECT command denied to user 'LiuQingPeng'@'39.155.212.28' for table 'sy_cd_ms_fin_nq_maindata'")
MySQLDBManager Thu, 01 Feb 2024 10:54:23 ERROR    mysql_engine.py (76)	####	(1142, "SELECT command denied to user 'LiuQingPeng'@'39.155.212.28' for table 'sy_cd_ms_fin_nq_mainindex'")

        select compCode, endDateProfit as endDate, ifAdjustProfit as mark, round(grossRatio *100, 4) as grossRatio from sy_cd_ms_fin_nq_mainindex 
        where dataStatus!=3 and ifAdjustProfit in (1,2)  and endDateProfit >"2020" and endDateProfit like "%12-31%"
        
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1142, "SELECT command denied to user 'LiuQingPeng'@'39.155.212.28' for table 'sy_cd_ms_fin_nq_mainindex'")
MySQLDBManager Thu, 01 Feb 2024 11:00:11 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'totalAssets' in 'field list'")

        select compCode, endDate, totalAssets,infoPublDate from sy_cd_ms_fin_sk_balsheet    
        where dataStatus!=3  and endDate >"2020" and endDate like "%12-31%" and ifMerged=1
        
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'totalAssets' in 'field list'")
