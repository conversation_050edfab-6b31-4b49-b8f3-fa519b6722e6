# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2023/2/27
import logging
import os

# 定义三种日志输出格式 开始
standard_format = '[%(asctime)s][%(pathname)s:%(lineno)d]' \
                  '[%(levelname)s][%(message)s]'

simple_format = '[%(levelname)s][%(asctime)s][%(filename)s:%(lineno)d]%(message)s'

# 定义日志输出格式 结束
"""
下面的两个变量对应的值 需要你手动修改
"""
logfile_dir = os.path.join(os.path.dirname(__file__), 'log')  # log文件的目录
logfile_name = 'test.log'  # log文件名

# 如果不存在定义的日志目录就创建一个
if not os.path.isdir(logfile_dir):
    os.mkdir(logfile_dir)

# log文件的全路径
logfile_path = os.path.join(logfile_dir, logfile_name)
# log配置字典
LOGGING_DIC = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': standard_format
        },
        'simple': {
            'format': simple_format
        },
    },
    'filters': {},  # 过滤日志
    'handlers': {
        # 打印到终端的日志
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',  # 打印到屏幕
            'formatter': 'simple'
        },
        # 打印到文件的日志,收集info及以上的日志
        'file': {
            'level': 'DEBUG',
            'class': 'logging.handlers.TimedRotatingFileHandler',  # 保存到文件
            'formatter': 'standard',
            'filename': logfile_path,  # 日志文件
            'backupCount': 3650,  # 指定保留的log文件数， backupCount是备份文件的个数，如果超过这个个数，就会自动删除
            'encoding': 'utf-8',  # 日志文件的编码，再也不用担心中文log乱码了
            'when': 'D'  # when是间隔的时间单位，以天为单位，一天一个log文件，单位有以下几种：S 秒，M 分 、H 小时、D 天、W 每星期（interval==0时代表星期一、midnight 每天凌晨
        },
    },
    'loggers': {
        # logging.getLogger(__name__)拿到的logger配置
        '': {
            'handlers': ['file', 'console'],  # 这里把上面定义的两个handler都加上，即log数据既写入文件又打印到屏幕
            'level': 'DEBUG',
            'propagate': True,  # 向上（更高level的logger）传递
        },  # 当键不存在的情况下 默认都会使用该k:v配置
    },
}


class Logger(object):
    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = object.__new__(cls, *args, **kwargs)
        return cls._instance

    def __init__(self):
        import logging.config
        logging.config.dictConfig(LOGGING_DIC)  # 自动加载字典中的配置
        self.logger = logging.getLogger('scorpio')


def get_logger():
    return Logger().logger



