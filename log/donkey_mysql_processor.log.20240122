MySQLDBManager Mon, 22 Jan 2024 11:44:38 ERROR    mysql_engine.py (76)	####	(1044, "Access denied for user 'YangXin'@'39.155.212.%' to database 'seeyii_assets_database'")

        SELECT id, compCode,compName,if(listStatus=1,"上市"， "非上市") as listStatus, dataStatus, "A股" as ty from sy_cd_ms_base_sk_stock where  
        id > 0 ORDER BY id ASC limit 1000;
        
Traceback (most recent call last):
  File "/Library/Python/3.9/site-packages/DBUtils/PooledDB.py", line 325, in connection
    con = self._idle_cache.pop(0)
IndexError: pop from empty list

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 68, in query_data
    mysql_db_conn = MySQLDBManager.create_mysql_connection(db_key)
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 42, in create_mysql_connection
    conn_pool = PooledDB(
  File "/Library/Python/3.9/site-packages/DBUtils/PooledDB.py", line 267, in __init__
    idle = [self.dedicated_connection() for i in range(mincached)]
  File "/Library/Python/3.9/site-packages/DBUtils/PooledDB.py", line 267, in <listcomp>
    idle = [self.dedicated_connection() for i in range(mincached)]
  File "/Library/Python/3.9/site-packages/DBUtils/PooledDB.py", line 338, in dedicated_connection
    return self.connection(False)
  File "/Library/Python/3.9/site-packages/DBUtils/PooledDB.py", line 327, in connection
    con = self.steady_connection()
  File "/Library/Python/3.9/site-packages/DBUtils/PooledDB.py", line 273, in steady_connection
    return connect(
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 137, in connect
    return SteadyDBConnection(
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 192, in __init__
    self._store(self._create())
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 211, in _create
    con = self._creator(*self._args, **self._kwargs)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 358, in __init__
    self.connect()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 679, in connect
    self.set_character_set(self.charset, self.collation)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 621, in set_character_set
    self._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1044, "Access denied for user 'YangXin'@'39.155.212.%' to database 'seeyii_assets_database'")
MySQLDBManager Mon, 22 Jan 2024 11:44:38 ERROR    mysql_engine.py (76)	####	(1044, "Access denied for user 'YangXin'@'39.155.212.%' to database 'seeyii_assets_database'")

        SELECT id, compCode,compName,if(listStatus=1,"上市"， "非上市") as listStatus, dataStatus, "三板" ty from sy_cd_ms_base_nq_stock where
        id > 0 ORDER BY id ASC limit 1000;
        
Traceback (most recent call last):
  File "/Library/Python/3.9/site-packages/DBUtils/PooledDB.py", line 325, in connection
    con = self._idle_cache.pop(0)
IndexError: pop from empty list

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 68, in query_data
    mysql_db_conn = MySQLDBManager.create_mysql_connection(db_key)
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 42, in create_mysql_connection
    conn_pool = PooledDB(
  File "/Library/Python/3.9/site-packages/DBUtils/PooledDB.py", line 267, in __init__
    idle = [self.dedicated_connection() for i in range(mincached)]
  File "/Library/Python/3.9/site-packages/DBUtils/PooledDB.py", line 267, in <listcomp>
    idle = [self.dedicated_connection() for i in range(mincached)]
  File "/Library/Python/3.9/site-packages/DBUtils/PooledDB.py", line 338, in dedicated_connection
    return self.connection(False)
  File "/Library/Python/3.9/site-packages/DBUtils/PooledDB.py", line 327, in connection
    con = self.steady_connection()
  File "/Library/Python/3.9/site-packages/DBUtils/PooledDB.py", line 273, in steady_connection
    return connect(
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 137, in connect
    return SteadyDBConnection(
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 192, in __init__
    self._store(self._create())
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 211, in _create
    con = self._creator(*self._args, **self._kwargs)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 358, in __init__
    self.connect()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 679, in connect
    self.set_character_set(self.charset, self.collation)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 621, in set_character_set
    self._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1044, "Access denied for user 'YangXin'@'39.155.212.%' to database 'seeyii_assets_database'")
MySQLDBManager Mon, 22 Jan 2024 11:47:25 ERROR    mysql_engine.py (76)	####	(1064, 'You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near \'， "非上市") as listStatus, dataStatus, "A股" as ty from sy_cd_ms_base_sk_s\' at line 1')

        SELECT id, compCode,compName,if(listStatus=1,"上市"， "非上市") as listStatus, dataStatus, "A股" as ty from sy_cd_ms_base_sk_stock where  
        id > 0 ORDER BY id ASC limit 1000;
        
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1064, 'You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near \'， "非上市") as listStatus, dataStatus, "A股" as ty from sy_cd_ms_base_sk_s\' at line 1')
MySQLDBManager Mon, 22 Jan 2024 11:47:25 ERROR    mysql_engine.py (76)	####	(1064, 'You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near \'， "非上市") as listStatus, dataStatus, "三板" ty from sy_cd_ms_base_nq_st\' at line 1')

        SELECT id, compCode,compName,if(listStatus=1,"上市"， "非上市") as listStatus, dataStatus, "三板" ty from sy_cd_ms_base_nq_stock where
        id > 0 ORDER BY id ASC limit 1000;
        
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.ProgrammingError: (1064, 'You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near \'， "非上市") as listStatus, dataStatus, "三板" ty from sy_cd_ms_base_nq_st\' at line 1')
MySQLDBManager Mon, 22 Jan 2024 12:05:55 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=2944532492;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:55 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=25952273;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:55 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=79822867;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:55 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=2349989919;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:55 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=2310406179;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:55 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=856162345;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:55 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=20578351;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:56 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=19005495;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:56 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=815398967;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:56 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=398458939;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:56 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=26476609;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:56 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=486539330;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:56 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=4096655430;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:56 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=164233292;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:56 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=403439709;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:56 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=2949775455;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:56 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=2320498784;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:56 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=510525538;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:56 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=2353660017;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:57 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=3219521663;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:57 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=677118083;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:57 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=26214540;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:57 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=2344485008;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:57 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=2320367780;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:57 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=167248045;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:57 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=2312765627;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:57 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=79823036;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:57 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=2314076349;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:57 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=266207426;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:57 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=2349465817;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:58 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=30802144;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:58 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=489816296;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:58 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=398459114;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:58 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=11172970738;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:58 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=945553655;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:58 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=3361341688;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:05:58 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=2341339397;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:07:01 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=2944532492;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:07:02 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=25952273;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:07:02 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=79822867;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:07:02 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=2349989919;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:07:02 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=2310406179;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:07:02 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=856162345;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:07:02 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=20578351;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:07:02 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=19005495;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:07:02 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=815398967;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:07:02 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=398458939;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:07:02 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=26476609;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:07:02 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=486539330;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
MySQLDBManager Mon, 22 Jan 2024 12:07:03 ERROR    mysql_engine.py (76)	####	(1054, "Unknown column 'dataStatus！' in 'where clause'")

                SELECT compCode,compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus！=3 and compCode=4096655430;
                
Traceback (most recent call last):
  File "/Users/<USER>/Downloads/git/panda/db/engine/mysql_engine.py", line 71, in query_data
    result_n = query_cursor.execute(sql_statement)
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 686, in tough_method
    raise error  # re-raise the original error again
  File "/Library/Python/3.9/site-packages/DBUtils/SteadyDB.py", line 605, in tough_method
    result = method(*args, **kwargs)  # try to execute
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/Library/Python/3.9/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 558, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 822, in _read_query_result
    result.read()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 1200, in read
    first_packet = self.connection._read_packet()
  File "/Library/Python/3.9/site-packages/pymysql/connections.py", line 772, in _read_packet
    packet.raise_for_error()
  File "/Library/Python/3.9/site-packages/pymysql/protocol.py", line 221, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "/Library/Python/3.9/site-packages/pymysql/err.py", line 143, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'dataStatus！' in 'where clause'")
