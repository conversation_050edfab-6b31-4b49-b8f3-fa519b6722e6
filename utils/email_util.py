# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
#
# <AUTHOR> <EMAIL>
# Date:   2018-11-13

import smtplib
import paramiko
from email.mime.text import MIMEText

from cfg.config import mail_info as m, ssh_cfg


class EmailUtil(object):
    def __init__(self):
        self.__mail_host = m["mail_host"]
        self.__mail_user = m["mail_user"]
        self.__mail_pass = m["mail_pass"]
        self.__mail_to = m["mail_to"]

    def send_email(self, email_title, email_content):
        if email_content is None or len(email_content) == 0:
            return
        email_struct = MIMEText(email_content, _subtype="plain", _charset="gb2312")
        email_struct["Subject"] = email_title
        email_struct["From"] = "".join(["WenYe", "<", self.__mail_user, ">"])
        email_struct["To"] = ";".join(self.__mail_to)
        server = smtplib.SMTP()
        server.connect(self.__mail_host)
        server.login(self.__mail_user, self.__mail_pass)
        server.sendmail(
            email_struct["From"], self.__mail_to, email_struct.as_string())
        server.close()


def send_email(err_info, email_title=None):
    """
    调度任务错误邮件发送入口
    :param err_info: 错误信息
    """
    if email_title is None:
        email_title = "donkey occurs err."
    email_content = err_info

    ssh = paramiko.SSHClient()
    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    ssh.connect("*************",
                username=ssh_cfg["user"],
                password=ssh_cfg["pwd"],
                allow_agent=True)

    for from_to in m["mail_to"]:
        sh_cmd = "echo '%s' | mail -s '%s' %s" % (
            email_content, email_title, from_to)
        _, stdout, _ = ssh.exec_command(sh_cmd)
        stdout.read()
    ssh.close()

# def send_email(err_info):
#     """
#     调度任务错误邮件发送入口
#     :param err_info: 错误信息
#     """
#     email_title = "donkey occurs err."
#     email_content = err_info
#
#     email_util = EmailUtil()
#     email_util.send_email(email_title, email_content)
