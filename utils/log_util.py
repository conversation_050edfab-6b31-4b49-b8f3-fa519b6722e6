# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
#
# <AUTHOR> <EMAIL>
# Date:   17-2-8

import logging
import logging.handlers

from cfg.config import log_path_base


def create_logger(log_name, log_file):
    LOGGER = logging.getLogger(log_name)
    formatter = logging.Formatter(
        "%(name)-12s %(asctime)s %(levelname)-8s "
        "%(filename)s (%(lineno)d)\t####\t"
        "%(message)s", "%a, %d %b %Y %H:%M:%S", )
    log_path = log_path_base + log_file
    file_handler = logging.handlers.TimedRotatingFileHandler(
        log_path, "midnight", 1)
    file_handler.suffix = "%Y%m%d"
    file_handler.setFormatter(formatter)
    LOGGER.addHandler(file_handler)
    LOGGER.setLevel(logging.INFO)
    return LOGGER
