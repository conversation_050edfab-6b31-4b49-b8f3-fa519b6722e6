# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
#
# <AUTHOR> <EMAIL>
# Date:   2018-02-13

import traceback

from datetime import datetime


def handler_err_msg(exc):
    err_msg = traceback.format_exc()
    err_msg = " ## ".join(err_msg.split("\n"))
    err_msg = " ## ".join([err_msg, str(exc)])
    return err_msg


def wrapper(func):
    def inner(*args, **kwargs):
        start = datetime.now()
        print("start", start)
        func(*args, **kwargs)
        end = datetime.now()
        print("end", start)
        print("finish", end - start)

    return inner
