# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
#
# <AUTHOR> <EMAIL>
# Date:   17-2-8

import time
from datetime import datetime
from datetime import timedelta


class DatetimeUtil(object):
    __datetime_format_list = [
        {"format_str": u"%Y%m%d", "is_date": False},
        {"format_str": u"%Y-%m-%d%H:%M", "is_date": False},
        {"format_str": u"%Y年%m月%d日%H:%M", "is_date": False},
        {"format_str": u"%Y-%m-%d%H:%M:%S", "is_date": False},
        {"format_str": u"%Y-%m-%d%H:%M:%S.%f", "is_date": False},
        {"format_str": u"%Y年%m月%d日%H:%M:%S", "is_date": False},
        {"format_str": u"%Y-%m-%dT%H:%M:%S.%fZ", "is_date": False},
        {"format_str": u"%Y-%m-%d", "is_date": True},
        {"format_str": u"%Y/%m/%d", "is_date": True},
        {"format_str": u"日期：%Y-%m-%d", "is_date": True},
        {"format_str": u"%Y年%m月%d日", "is_date": True},
        {"format_str": u"%Y.%m.%d", "is_date": True}, ]

    @staticmethod
    def get_datetime_now():
        return datetime.now()

    @staticmethod
    def create_date(year, month, day):
        return datetime(year, month, day)

    @staticmethod
    def get_today_base():
        return DatetimeUtil.get_date_base(datetime.now())

    @staticmethod
    def cal_date_delta(start_date, end_date):
        return (end_date - start_date).days

    @staticmethod
    def get_date_by_delta(date_base, delta):
        return date_base + timedelta(days=delta)

    @staticmethod
    def str_to_date(date_str, str_format="%Y-%m-%d"):
        return datetime.strptime(date_str, str_format)

    @staticmethod
    def date_to_str(target_date, str_format="%Y-%m-%d"):
        if not isinstance(target_date, datetime):
            return target_date
        if target_date.year >= 1900:
            return target_date.strftime(str_format)
        return datetime(1900, 1, 1).strftime(str_format)

    @staticmethod
    def get_date_base(date_base):
        return date_base.replace(hour=0, minute=0, second=0, microsecond=0)

    @staticmethod
    def get_last_year_same_date(date_base):
        return date_base.replace(year=date_base.year - 1)

    @staticmethod
    def diff_date_days(first_date, second_date):
        diff_days = (first_date - second_date).days
        return abs(diff_days)

    @staticmethod
    def timestamp_to_date(timestamp, str_format="%Y-%m-%d"):
        if len(str(timestamp)) == 13:
            timestamp = int(timestamp) / 1000
        timestamp = float(timestamp)
        time_array = time.localtime(timestamp)
        date_str = time.strftime(str_format, time_array)
        return datetime.strptime(date_str, str_format)

    @staticmethod
    # range: [date_begin, date_end]
    def extend_date_range(date_begin, date_end):
        return [date_begin + timedelta(days=i)
                for i in range((date_end - date_begin).days + 1)]

    @staticmethod
    def datetime_to_quarter_str(raw_datetime):
        re_quarter = ((raw_datetime.month + 2) / 3 - 1) * 3 + 1
        re_datetime = DatetimeUtil.create_date(raw_datetime.year, re_quarter, 1)
        return DatetimeUtil.date_to_str(re_datetime)

    @staticmethod
    def date_to_timestamp(datetime_object):
        # gen 13 timestamp
        now_timetuple = datetime_object.timetuple()
        now_second = time.mktime(now_timetuple)
        mow_millisecond = now_second * 1000 + datetime_object.microsecond / 1000
        return str(int(mow_millisecond))

    @staticmethod
    def convert_str_to_datetime(str_datetime):
        result_datetime = ""
        source_datetime_str = "".join(str_datetime.split())
        for datetime_format in DatetimeUtil.__datetime_format_list:
            try:
                result_datetime = datetime.strptime(
                    source_datetime_str, datetime_format["format_str"])

                # only has date, so set current time as the time.
                if datetime_format["is_date"] is True:
                    result_datetime = DatetimeUtil.merge_date_time(
                        result_datetime, datetime.now())
                break
            except:
                continue
        return result_datetime

    @staticmethod
    def merge_date_time(date_base, time_base):
        return datetime(year=date_base.year,
                        month=date_base.month,
                        day=date_base.day,
                        hour=time_base.hour,
                        minute=time_base.minute,
                        second=time_base.second,
                        microsecond=time_base.microsecond)

    @staticmethod
    def create_assist_date(datestart, dateend):
        # 转为日期格式
        datestart = datetime.strptime(datestart, '%Y-%m-%d')
        dateend = datetime.strptime(dateend, '%Y-%m-%d')
        date_list = []
        date_list.append(datestart.strftime('%Y-%m-%d'))
        while datestart < dateend:
            # 日期叠加一天
            datestart += timedelta(days=+1)
            # 日期转字符串存入列表
            date_list.append(datestart.strftime('%Y-%m-%d'))
        return date_list
