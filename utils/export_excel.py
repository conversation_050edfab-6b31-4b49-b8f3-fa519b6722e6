# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2020/05/18
import os
import json
import xlsxwriter
import decimal
from datetime import datetime
from cfg.config import out_file_path
from utils.datetime_util import DatetimeUtil


class ExportExcel(object):
    def __init__(self):
        super(ExportExcel, self).__init__()

    @staticmethod
    def save_to_excel(excel_name, field_dict, sheet_item_dict):
        """
        :param field_dict: {
                { "Sheet":
            eg:  "字段名": (u"字段含义", 插入的列号),
                 "cname": (u"公司名称", 0)
                 }
        }
        :param sheet_item_dict: {
            Sheet名称:[data]
        }
        :return:
        """
        output_file_path = os.path.join(out_file_path, excel_name)
        # output_file_path = "./" + self._excel_name
        xls_file = xlsxwriter.Workbook(output_file_path)
        for sheet_name, item_list in sheet_item_dict.items():
            xls_sheet = xls_file.add_worksheet(name=sheet_name)
            for title in field_dict[sheet_name].values():
                xls_sheet.write(0, title[1], title[0])
            row_no = 1
            for result_item in item_list:
                for field_name, title in field_dict[sheet_name].items():
                    field_value = result_item.get(field_name)
                    if isinstance(field_value, decimal.Decimal):
                        field_value = float(field_value)
                    if isinstance(field_value, datetime):
                        field_value = DatetimeUtil.date_to_str(field_value)
                    if not isinstance(field_value, str):
                        field_value = json.dumps(field_value, ensure_ascii=False)
                    xls_sheet.write(row_no, title[1], field_value)
                row_no += 1
        xls_file.close()
        print("finished.")
