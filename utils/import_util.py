# <AUTHOR> <EMAIL>
# Date:   2018-06-19

import importlib

from utils.exceptions import DefeatDataFill


def import_string(dotted_path):
    try:
        module_path, class_name = dotted_path.rsplit('.', 1)
    except ValueError:
        msg = "%s doesn't look like a module path" % dotted_path
        raise DefeatDataFill(msg)

    module = importlib.import_module(module_path)

    try:
        return getattr(module, class_name)
    except AttributeError:
        msg = 'Module "%s" does not define a "%s" attribute/class' % (
            module_path, class_name)
        raise DefeatDataFill(msg)


def import_attr(dotted_module_path, dotted_attr_name):
    module = importlib.import_module(dotted_module_path)
    attr_names = dotted_attr_name.split(".")
    cur_obj = module
    for attr_name in attr_names:
        cur_obj = getattr(cur_obj, attr_name)
    return cur_obj
