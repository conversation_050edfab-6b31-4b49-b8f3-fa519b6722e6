# <AUTHOR> <EMAIL>
# Date:   2019-06-26

from datetime import datetime


def query_item(collection_name, query_condition=dict(), query_field=dict()):
    for item in collection_name.find(query_condition, query_field):
        yield item


def batch_insert_many(collection_name, item_list):
    for item in item_list:
        item["modify_time"] = datetime.now()
    number = 1000
    while item_list:
        collection_name.insert_many(item_list[:number])
        item_list = item_list[number:]


def rebuild_collection():
    pass


def rebuild_collection_list():
    pass
