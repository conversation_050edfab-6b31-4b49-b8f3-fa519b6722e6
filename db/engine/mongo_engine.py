# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# Author: j<PERSON><PERSON><PERSON> <<EMAIL>>
# Date:   2017-6-16

from bson import ObjectId
import json

import gridfs
import pymongo

from cfg.config import mongodb_db_cfg

from utils.datetime_util import DatetimeUtil


class MongodbManager(object):
    __ADMIN_USER = "db_server_root_admin"
    __ADMIN_PWD = "db_server_root_admin_shiye1805A"

    def __init__(self):
        self.__client_list = list()
        self.__db_admin_dict = dict()
        self.__db_dict = dict()
        self.__db_collection_dict = dict()
        for db_name, db_cfg in mongodb_db_cfg.items():
            client, db_admin, db, collection_dict = self.__init_db(
                db_cfg["db_config"], db_cfg["collection_list"])
            self.__client_list.append(client)
            self.__db_admin_dict[db_name] = db_admin
            self.__db_dict[db_name] = db
            self.__db_collection_dict[db_name] = collection_dict

    def __init_db(self, db_cfg, collection_name_list):
        client = pymongo.MongoClient(db_cfg["path"], db_cfg["port"])
        db_admin = client.get_database("admin")
        db = client.get_database(db_cfg["db_name"])
        if db_cfg["need_auth"] is True:
            db_admin.authenticate(self.__ADMIN_USER, self.__ADMIN_PWD)
            db.authenticate(db_cfg["user"], db_cfg["pwd"])

        collection_dict = dict()
        for collection_name in collection_name_list:
            collection = db.get_collection(collection_name)
            collection_dict[collection_name] = collection
        return client, db_admin, db, collection_dict

    def __del__(self):
        for client in self.__client_list:
            client.close()

    def query_count(self, db_name, collection_name, query_condition):
        if db_name not in self.__db_collection_dict:
            return None
        collection_dict = self.__db_collection_dict[db_name]
        if collection_name not in collection_dict:
            return None
        return collection_dict[collection_name].count(query_condition)

    def distinct_data(self,
                      db_name, collection_name, distinct_field,
                      query_condition):
        if db_name not in self.__db_collection_dict:
            return None
        collection_dict = self.__db_collection_dict[db_name]
        if collection_name not in collection_dict:
            return None
        query_result = collection_dict[collection_name].distinct(
            distinct_field, query_condition)
        return query_result

    def query_data(self, db_name, collection_name, query_condition,
                   query_field=None, sort_field=None, limit_n=None, skip_n=None,
                   hint=None):
        if db_name not in self.__db_collection_dict:
            return None
        collection_dict = self.__db_collection_dict[db_name]
        if collection_name not in collection_dict:
            return None
        query_result = collection_dict[collection_name].find(
            query_condition, query_field)
        if sort_field is not None:
            query_result = query_result.sort(sort_field)
        if skip_n is not None:
            query_result = query_result.skip(skip_n)
        if limit_n is not None:
            query_result = query_result.limit(limit_n)
        if hint is not None:
            query_result = query_result.hint(hint)
        result_data_list = list(query_result)
        return result_data_list

    def group_query_data(self,
                         db_name, collection_name, key, condition, initial,
                         reduce_js):
        if db_name not in self.__db_collection_dict:
            return None
        collection_dict = self.__db_collection_dict[db_name]
        if collection_name not in collection_dict:
            return None
        query_result = collection_dict[collection_name].group(
            key=key, condition=condition, initial=initial, reduce=reduce_js)
        return query_result

    def update_data(
            self, db_name, collection_name, update_condition,
            update_data, push_data=None, add_to_set_data=None,
            upsert=True, multi=False):
        if db_name not in self.__db_collection_dict:
            return
        collection_dict = self.__db_collection_dict[db_name]
        if collection_name not in collection_dict:
            return
        modify_time = DatetimeUtil.get_datetime_now()
        update_data["modify_time"] = modify_time
        update_item = {"$set": update_data}
        if push_data is not None:
            update_item["$push"] = push_data
        if add_to_set_data is not None:
            update_item["$addToSet"] = add_to_set_data
        collection_dict[collection_name].update(
            update_condition, update_item, upsert=upsert, multi=multi)

    def remove_data(self, db_name, collection_name, remove_condition):
        if db_name not in self.__db_collection_dict:
            return
        collection_dict = self.__db_collection_dict[db_name]
        if collection_name not in collection_dict:
            return
        collection_dict[collection_name].remove(remove_condition)

    def rebuild_collection(self,
                           db_name, collection_name, index_list,
                           is_gridfs=False,
                           shard_key=None):
        if db_name not in self.__db_collection_dict:
            return
        collection_dict = self.__db_collection_dict[db_name]
        if collection_name not in collection_dict:
            return

        db_admin, db = self.__db_admin_dict[db_name], self.__db_dict[db_name]
        if is_gridfs is True:
            db.drop_collection(collection_name + ".chunks")
            db.drop_collection(collection_name + ".files")
            coll_chunks = db.create_collection(collection_name + ".chunks")
            collection = db.create_collection(collection_name + ".files")
            for index_item in index_list:
                key = index_item["index_key"]
                name = index_item["index_name"]
                collection.ensure_index(key, name=name, background=True)
            if shard_key is not None:
                coll_chunks.ensure_index(
                    [("files_id", "hashed")],
                    name="files_id_hashed", background=True)
                self.__shard_collection(
                    db_admin, db_name, collection_name + ".chunks",
                    {"files_id": "hashed"})
                self.__shard_collection(
                    db_admin, db_name, collection_name + ".files", shard_key)
        else:
            db.drop_collection(collection_name)
            collection = db.create_collection(collection_name)
            for index_item in index_list:
                key = index_item["index_key"]
                name = index_item["index_name"]
                collection.ensure_index(key, name=name, background=True)
            if shard_key is not None:
                self.__shard_collection(
                    db_admin, db_name, collection_name, shard_key)
            collection_dict[collection_name] = collection

    def insert_data(self, db_name, collection_name, insert_data):
        if db_name not in self.__db_collection_dict:
            return
        collection_dict = self.__db_collection_dict[db_name]
        if collection_name not in collection_dict:
            return
        modify_time = DatetimeUtil.get_datetime_now()
        insert_data["modify_time"] = modify_time
        collection_dict[collection_name].insert(insert_data)

    def insert_many(self, db_name, collection_name, data_list):
        if db_name not in self.__db_collection_dict:
            return
        collection_dict = self.__db_collection_dict[db_name]
        if collection_name not in collection_dict:
            return
        modify_time = DatetimeUtil.get_datetime_now()
        for data_item in data_list:
            data_item["modify_time"] = modify_time
        collection_dict[collection_name].insert_many(data_list)

    def insert_many_v2(self, db_name, collection_name, data_list):
        if db_name not in self.__db_collection_dict:
            return
        collection_dict = self.__db_collection_dict[db_name]
        if collection_name not in collection_dict:
            return
        modify_time = DatetimeUtil.get_datetime_now()
        for data_item in data_list:
            data_item["modify_time"] = modify_time
            data_item["create_time"] = modify_time
        collection_dict[collection_name].insert_many(data_list)

    def change_collection(self, db_name, collection_name, is_gridfs=False):
        if db_name not in self.__db_collection_dict:
            return
        collection_dict = self.__db_collection_dict[db_name]
        if collection_name not in collection_dict:
            return
        if collection_name + "_bak" not in collection_dict:
            return
        db = self.__db_dict[db_name]
        if is_gridfs is True:
            self.create_collection(db, collection_name + ".chunks")
            self.create_collection(db, collection_name + ".files")
            db[collection_name + ".chunks"].rename(
                collection_name + "_tmp.chunks")
            db[collection_name + ".files"].rename(
                collection_name + "_tmp.files")

            db[collection_name + "_bak.chunks"].rename(
                collection_name + ".chunks")
            db[collection_name + "_bak.files"].rename(
                collection_name + ".files")

            db[collection_name + "_tmp.chunks"].rename(
                collection_name + "_bak.chunks")
            db[collection_name + "_tmp.files"].rename(
                collection_name + "_bak.files")
        else:
            self.create_collection(db, collection_name)
            db[collection_name].rename(collection_name + "_tmp")
            db[collection_name + "_bak"].rename(collection_name)
            db[collection_name + "_tmp"].rename(collection_name + "_bak")

    @staticmethod
    def create_collection(db, collection_name):
        try:
            db.create_collection(collection_name)
        except:
            pass

    def aggregate_data(self, db_name, collection_name, pipeline):
        if db_name not in self.__db_collection_dict:
            return None
        collection_dict = self.__db_collection_dict[db_name]
        if collection_name not in collection_dict:
            return None
        query_result = collection_dict[collection_name].aggregate(pipeline)
        return query_result

    def gridfs_put(self, db_name, collection_name, data_item, attrs):
        fs = self.__create_gridfs(db_name, collection_name)
        attrs = {attr: data_item[attr] for attr in attrs}
        data_item = json.dumps(data_item)
        if len(attrs) == 0:
            fs.put(data_item, encoding='utf-8')
        else:
            fs.put(data_item, encoding='utf-8', **attrs)

    def gridfs_find(self, db_name, collection_name, query_condition,
                    sort_field=None, limit_n=None, skip_n=None,
                    need_file_info=False):
        fs = self.__create_gridfs(db_name, collection_name)
        query_result = fs.find(query_condition, no_cursor_timeout=False)
        if sort_field is not None:
            query_result = query_result.sort(sort_field)
        if limit_n is not None:
            query_result = query_result.limit(limit_n)
        if skip_n is not None:
            query_result = query_result.skip(skip_n)
        data_item_list = list()
        for result in query_result:
            data_item = result.read()
            data_item = json.loads(data_item.decode("utf-8"))
            if need_file_info is True:
                data_item["file_info"] = result._file
            data_item_list.append(data_item)
        return data_item_list

    def gridfs_exists(self, db_name, collection_name, query_condition):
        fs = self.__create_gridfs(db_name, collection_name)
        return fs.exists(query_condition)

    def gridfs_delete_by_id(self, db_name, collection_name, data_id):
        fs = self.__create_gridfs(db_name, collection_name)
        if isinstance(data_id, ObjectId) is False:
            data_id = ObjectId(data_id)
        return fs.delete(data_id)

    def __create_gridfs(self, db_name, collection_name):
        if db_name not in self.__db_collection_dict:
            return None
        collection_dict = self.__db_collection_dict[db_name]
        if collection_name not in collection_dict:
            return None
        db = self.__db_dict[db_name]
        return gridfs.GridFS(db, collection=collection_name)

    @staticmethod
    def __shard_collection(db_admin, db_name, collection_name, shard_key):
        db_admin.command("shardcollection",
                         ".".join([db_name, collection_name]),
                         key=shard_key)
