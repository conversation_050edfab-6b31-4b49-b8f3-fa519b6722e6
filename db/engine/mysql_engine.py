# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
#
# Author: j<PERSON><PERSON><PERSON> <<EMAIL>>
# Date:   2017-6-27
import os
from functools import wraps
import time
import logging
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool
from sqlalchemy_utils import database_exists, create_database
from sqlalchemy.ext.declarative import declarative_base

import threading
from traceback import format_exc

from DBUtils.PooledDB import PooledDB
import pymysql
pymysql.install_as_MySQLdb()

from cfg.config import mysql_db_cfg
from utils.email_util import send_email
from utils.log_util import create_logger

LOGGER = create_logger("MySQLDBManager", "donkey_mysql_processor.log")


class MySQLDBManager(object):
    _conn_dict = {
        db_key: {"conn_pool": None, "mutex": threading.RLock()}
        for db_key in mysql_db_cfg}

    @staticmethod
    def create_mysql_connection(db_key):
        db_cfg = mysql_db_cfg[db_key]
        conn = MySQLDBManager._conn_dict[db_key]
        if conn["conn_pool"] is None:
            conn["mutex"].acquire()
            if conn["conn_pool"] is None:
                conn_pool = PooledDB(
                    creator=pymysql,
                    mincached=db_cfg.get("min_cached", 1),
                    maxcached=db_cfg.get("max_cached", 10),
                    maxconnections=db_cfg.get("max_connections", 10),
                    host=db_cfg["path"],
                    port=db_cfg["port"],
                    user=db_cfg["user"],
                    passwd=db_cfg["pwd"],
                    db=db_cfg["db_name"],
                    autocommit=True,
                    charset=db_cfg.get("charset", "utf8"))
                conn["conn_pool"] = conn_pool
            conn["mutex"].release()
        return conn["conn_pool"].connection()

    @staticmethod
    def query_data(sql_statement, db_key) -> (list or None):
        """
        返回的结果包括两种：
            list: 查询到结果。
            None: 未查询到内容，程序执行报错。
        """
        mysql_db_conn = None
        query_cursor = None
        try:
            mysql_db_conn = MySQLDBManager.create_mysql_connection(db_key)
            query_cursor = mysql_db_conn.cursor(
                cursor=pymysql.cursors.DictCursor)
            result_n = query_cursor.execute(sql_statement)
            if result_n == 0:
                return list()
            return list(query_cursor.fetchall())
        except Exception as e:
            LOGGER.exception(str(e) + "\n" + sql_statement)
            print(e)
            print(sql_statement)
            return list()
        finally:
            if query_cursor is not None:
                query_cursor.close()
            if mysql_db_conn is not None:
                mysql_db_conn.close()

    @staticmethod
    def execute_sql(sql_statement, db_key):
        mysql_db_conn = None
        insert_cursor = None
        try:
            mysql_db_conn = MySQLDBManager.create_mysql_connection(db_key)
            insert_cursor = mysql_db_conn.cursor(
                cursor=pymysql.cursors.DictCursor)
            insert_cursor.execute(sql_statement)
            mysql_db_conn.commit()
        except Exception as e:
            LOGGER.exception(str(e))
            raise e
        finally:
            if insert_cursor is not None:
                insert_cursor.close()
            if mysql_db_conn is not None:
                mysql_db_conn.close()

    @staticmethod
    def save_data(db_key, insert_template, result_data, field_list):
        mysql_db_conn = None
        cursor = None
        insert_sql = None
        try:
            columns = field_list
            field_str = "`{}`".format("`,`".join(columns))
            mysql_db_conn = MySQLDBManager.create_mysql_connection(db_key)
            cursor = mysql_db_conn.cursor(
                cursor=pymysql.cursors.DictCursor)
            for idx in range(0, len(result_data), 100):
                value_str_list = list()
                for item in result_data[idx: idx + 100]:
                    values, value_template_list = list(), list()
                    for field in columns:
                        value = item.get(field, None)
                        if value is None:
                            value_template_list.append("{}")
                            values.append("NULL")
                        else:
                            if isinstance(value, str):
                                if ('"' in value and "'" in value) or '\n' in value \
                                        or '\r' in value or '\t' in value or '\\' in value:
                                    value = pymysql.escape_string(value)
                                if '"' in value:
                                    value_template_list.append("'{}'")
                                else:
                                    value_template_list.append('"{}"')
                            else:
                                value_template_list.append("'{}'")
                            values.append(value)
                    value_template = ",".join(value_template_list)
                    value_str_item = value_template.format(*values)
                    value_str_list.append(value_str_item.join(["(", ")"]))
                insert_sql = insert_template % (
                    field_str, ",".join(value_str_list))
                cursor.execute(insert_sql)
        except Exception as e:
            print(e)
            LOGGER.exception("sql={}\nerr={}\n".format(insert_sql, str(e)))
            send_email("err={}".format(format_exc()))
        finally:
            if cursor is not None:
                cursor.close()
            if mysql_db_conn is not None:
                mysql_db_conn.close()


class DB(object):
    def __init__(self, cfg=None, conn_uri=None, is_pool=False):
        """
        :param cfg:
        :param conn_uri:
        """
        # For compatibility with older code
        if cfg is None:
            cfg = dict()
        if conn_uri and is_pool:
            self.engine = create_engine(conn_uri)
        elif conn_uri and not is_pool:
            self.engine = create_engine(conn_uri, poolclass=NullPool)
        elif is_pool:
            self.engine = create_engine(
                f"mysql+pymysql://"
                f"{cfg['user']}:"
                f"{cfg['pwd']}@"
                f"{cfg['path']}:"
                f"{cfg['port']}/"
                f"{cfg['db_name']}?"
                f"charset=utf8"
            )
        else:
            self.engine = create_engine(
                f"mysql+pymysql://"
                f"{cfg['user']}:"
                f"{cfg['pwd']}@"
                f"{cfg['path']}:"
                f"{cfg['port']}/"
                f"{cfg['db_name']}?"
                f"charset=utf8",
                poolclass=NullPool
            )
        # if not database_exists(self.engine.url):
        #     create_database(self.engine.url)
        self.session = None

    def __enter__(self):
        Session = sessionmaker(bind=self.engine)
        self.session = Session()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.session.close()