# Copyright (c) 2015 <PERSON>ye Inc.
# All rights reserved.
# Author: j<PERSON><PERSON>e <<EMAIL>>
# Date:   2017-6-20

import threading

from db.engine.mongo_engine import MongodbManager
from .item_base import ItemBase


class DataServer(object):
    _server = None
    _mutex = threading.Lock()

    @staticmethod
    def get_instance():
        if DataServer._server is None:
            DataServer._mutex.acquire()
            if DataServer._server is None:
                DataServer._server = DataServer()
            DataServer._mutex.release()
        return DataServer._server

    def __init__(self):
        mongodb_manager = MongodbManager()
        self.__item_base = ItemBase(mongodb_manager)

        self.__processor_mapping = {
            "change_table": self.__item_base.change_table,
            "update_item": self.__item_base.update_item,
            "query_item": self.__item_base.query_item,
            "distinct_query_item": self.__item_base.distinct_query_item,
            "query_item_count": self.__item_base.query_item_count,
            "remove_item": self.__item_base.remove_item,
            "rebuild_item_collection": self.__item_base.rebuild_item_collection,
            "insert_item": self.__item_base.insert_item,
            "batch_insert_item": self.__item_base.batch_insert_item,
            "batch_insert_item_v2": self.__item_base.batch_insert_item_v2,
            "query_sql_item": self.__item_base.query_sql_item,
            "execute_sql_item": self.__item_base.execute_sql_item,
            "insert_sql_list": self.__item_base.insert_sql_list,
        }

    def call(self, processor_name, schema=None):
        if schema is None:
            return self.__processor_mapping[processor_name]()
        return self.__processor_mapping[processor_name](schema)
