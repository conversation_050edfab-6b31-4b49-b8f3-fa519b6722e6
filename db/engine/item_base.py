# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
#
# Author: j<PERSON><PERSON><PERSON> <<EMAIL>>
# Date:   2017-6-20
from db.engine.mysql_engine import MySQLDBManager


class ItemBase(object):
    def __init__(self, db_manager=None):
        self._db_manager = db_manager

    def query_item(self, query_schema):
        db_name = query_schema["db_name"]
        collection_name = query_schema["collection_name"]
        query_condition = query_schema["query_condition"]
        query_field = query_schema.get("query_field", None)
        sort_field = query_schema.get("sort_field", None)
        limit_n = query_schema.get("limit_n", None)
        skip_n = query_schema.get("skip_n", None)
        hint = query_schema.get("hint", None)
        return self._db_manager.query_data(
            db_name, collection_name, query_condition, query_field,
            sort_field, limit_n, skip_n, hint)

    def group_query_item(self, query_schema):
        db_name = query_schema["db_name"]
        collection_name = query_schema["collection_name"]
        key = query_schema["key"]
        condition = query_schema.get("condition", None)
        initial = query_schema.get("initial", None)
        reduce_js = query_schema.get("reduce_js", None)
        return self._db_manager.group_query_data(
            db_name, collection_name, key, condition,
            initial, reduce_js)

    def distinct_query_item(self, query_schema):
        db_name = query_schema["db_name"]
        collection_name = query_schema["collection_name"]
        distinct_field = query_schema["distinct_field"]
        query_condition = query_schema["query_condition"]
        return self._db_manager.distinct_data(
            db_name, collection_name, distinct_field, query_condition)

    def query_item_count(self, query_schema):
        db_name = query_schema["db_name"]
        collection_name = query_schema["collection_name"]
        query_condition = query_schema["query_condition"]
        return self._db_manager.query_count(
            db_name, collection_name, query_condition)

    def insert_item(self, insert_schema):
        db_name = insert_schema["db_name"]
        collection_name = insert_schema["collection_name"]
        insert_data = insert_schema["insert_data"]
        self._db_manager.insert_data(db_name, collection_name, insert_data)

    def remove_item(self, remove_schema):
        db_name = remove_schema["db_name"]
        collection_name = remove_schema["collection_name"]
        remove_condition = remove_schema["remove_condition"]
        self._db_manager.remove_data(db_name, collection_name, remove_condition)

    def rebuild_item_collection(self, rebuild_schema):
        db_name = rebuild_schema["db_name"]
        collection_name = rebuild_schema["collection_name"]
        index_list = rebuild_schema.get("index_list", list())
        is_gridfs = rebuild_schema.get("is_gridfs", False)
        shard_key = rebuild_schema.get("shard_key", None)
        self._db_manager.rebuild_collection(
            db_name, collection_name, index_list, is_gridfs, shard_key)

    def update_item(self, update_schema):
        db_name = update_schema["db_name"]
        collection_name = update_schema["collection_name"]
        update_condition = update_schema["update_condition"]
        update_data = update_schema["update_data"]
        push_data = update_schema.get("push_data", None)
        add_to_set_data = update_schema.get("add_to_set_data", None)
        upsert = update_schema.get("upsert", True)
        multi = update_schema.get("multi", False)
        self._db_manager.update_data(
            db_name, collection_name, update_condition,
            update_data, push_data=push_data, add_to_set_data=add_to_set_data,
            upsert=upsert, multi=multi)

    def change_table(self, change_schema):
        collection_name = change_schema["collection_name"] + "_bak"
        is_gridfs = change_schema.get("is_gridfs", False)
        collection_name_list = list()
        if is_gridfs is True:
            collection_name_list.append(".".join([collection_name, "chunks"]))
            collection_name_list.append(".".join([collection_name, "files"]))
        else:
            collection_name_list.append(collection_name)
        for collection_name in collection_name_list:
            query_schema = {
                "db_name": change_schema["db_name"],
                "collection_name": collection_name,
                "query_condition": {}}
            result_count = self.query_item_count(query_schema)
            if result_count == 0:
                return
        db_name = change_schema["db_name"]
        collection_name = change_schema["collection_name"]
        self._db_manager.change_collection(db_name, collection_name, is_gridfs)

    def batch_insert_item(self, insert_schema):
        db_name = insert_schema["db_name"]
        collection_name = insert_schema["collection_name"]
        data_list = insert_schema["data_list"]
        self._db_manager.insert_many(db_name, collection_name, data_list)

    def batch_insert_item_v2(self, insert_schema):
        db_name = insert_schema["db_name"]
        collection_name = insert_schema["collection_name"]
        data_list = insert_schema["data_list"]
        self._db_manager.insert_many_v2(db_name, collection_name, data_list)

    def aggregate_data(self, aggregate_schema):
        db_name = aggregate_schema["db_name"]
        collection_name = aggregate_schema["collection_name"]
        pipeline = aggregate_schema["pipeline"]
        return self._db_manager.aggregate_data(
            db_name, collection_name, pipeline)

    @staticmethod
    def query_sql_item(query_schema):
        db_key = query_schema["db_key"]
        sql_statement = query_schema["sql_statement"]
        return MySQLDBManager.query_data(sql_statement, db_key)

    @staticmethod
    def execute_sql_item(query_schema):
        db_key = query_schema["db_key"]
        sql_statement = query_schema["sql_statement"]
        return MySQLDBManager.query_data(sql_statement, db_key)

    @staticmethod
    def insert_sql_list(save_schema):
        insert_template = save_schema["insert_template"]
        result_data = save_schema["result_list"]
        field_list = save_schema["field_list"]
        db_key = save_schema["db_key"]
        MySQLDBManager.save_data(db_key, insert_template,
                                 result_data, field_list)
