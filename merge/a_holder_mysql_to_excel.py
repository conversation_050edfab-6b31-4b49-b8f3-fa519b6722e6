# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022-10-17
from core.excel_base import ExcelBase


class Holder(ExcelBase):
    def __init__(self):
        super(Holder, self).__init__()
        pass

    def process(self, *args, **kwargs):
        name_list = list(set(self.query_ipo_data()))
        sql_1, sql_2 = list(), list()
        for idx in range(0, len(name_list), 50):
            names = name_list[idx:idx + 50]
            sql_1.extend(self.query_sql_data(names))
            sql_2.extend(self.query_sql_data_1(names))
        self.save_data_excel({"tq_sk_shareholder": sql_1, "LC_MainSHListNew": sql_2})
        print(f"ipo={len(name_list)}")
        print("tq_sk_shareholder={}".format(len({i["company_name"] for i in sql_1})))
        print("LC_MainSHListNew={}".format(len({i["company_name"] for i in sql_2})))

    def save_data_excel(self, result):
        """
        公司名称、公司类型【已上市、已申报】统一社会信用代码、股东名称、股东排名、持股数量（HoldSum）、
        持股数量占总股本比例 （PCTOfTotalShares）、 其中:有限售股份数量（RestrainedTShare） 、
         其中:无限售股份数量（UnstintedTShare）、 股东关联关系（ConnectionRelation）、
        与其他股东关联关系说明（ConnectionStatement）、 与其他股东同属一致行动人说明（ActInConcertStatement）
        :param result:
        :return:
        """
        field_cfg = {
            'company_name': ('公司名称', 0),
            'holder_name': ('股东名称', 1),
            'rank': ('股东排名', 2),
            'num': ('持股数量', 3),
            'rto': ('持股数量占总股本比例', 4),
            'limit_rto': ("其中:有限售股份数量", 5),
            'un_rto': ("其中:无限售股份数量", 6),
            'rel': ("股东关联关系", 7),
            'sm': ("与其他股东关联关系说明", 8),
            'a_sm': ("与其他股东同属一致行动人说明", 9),
            'end_date': ("截止日期", 10),
        }
        self._excel_name = self.name_add_date("IPO股东数据.xlsx")
        self.save_to_excel(field_cfg, result)

    def query_ipo_data(self, ):
        sql_statement = """SELECT compName from dws_me_trad_ipo_base where 
        dataStatus !=3 and currStat not in (206, 207, 208, 210, 213, 219,220, 301,302);"""
        query_schema = dict(db_key="seeyii_db", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return [i["compName"] for i in result_list]

    def query_sql_data(self, names):
        sql_statement = """
        SELECT
            REPLACE(REPLACE(SHHOLDERNAME, '(', '（'), ')', '）')  AS holder_name,
            REPLACE(REPLACE(b.COMPNAME, '(', '（'), ')', '）') AS company_name,	
            RANK as rank,
            HOLDERAMT as num,
            HOLDERRTO as rto,
            LIMITHOLDERAMT as limit_rto,
            UNLIMHOLDERAMT as un_rto,
            SHHOLDRELEGROUP as rel,
            SHHOLDERRELEMEMO as sm,
            ACTCONCERTGROUP as a_sm,
            ENDDATE as end_date
        FROM
            tq_sk_shareholder as  a JOIN tq_comp_info  as b on a.COMPCODE = b.COMPCODE  
            and a.isvalid = 1 and b.isvalid = 1
        WHERE
            b.COMPNAME in ('{}')
        """
        # a.DATASOURCE in (07, 08) and
        name_str = "','".join([i.replace('（', '(').replace('）', ')') for i in names])
        query_schema = dict(db_key="finchina_143", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list

    def query_sql_data_1(self, names):
        sql_statement = """
        SELECT
            REPLACE(REPLACE(b.ChiName, '(', '（'), ')', '）') as company_name,
            REPLACE(REPLACE(SHList, '(', '（'), ')', '）') as holder_name,
            SHNo as rank,
            HoldSum as num,
            PCTOfTotalShares as rto,
            RestrainedTShare as limit_rto,
            UnstintedTShare as un_rto,
            ConnectionRelation as rel,
            ConnectionStatement as sm,
            ActInConcertStatement as a_sm,
            EndDate as end_date
        FROM
           LC_MainSHListNew as a JOIN SecuMain as b on a.CompanyCode=b.CompanyCode
        WHERE
            a.InfoSource like "%招股%" and
            b.ChiName in ('{}')
        """
        # a.InfoSource = '招股说明书' and
        name_str = "','".join([i.replace('（', '(').replace('）', ')') for i in names])
        query_schema = dict(db_key="db_seeyii_143", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    p = Holder()
    p.process()
