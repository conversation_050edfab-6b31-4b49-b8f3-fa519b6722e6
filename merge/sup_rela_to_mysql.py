# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/05/25
from core.excel_base import ExcelBase


class RelMysql(ExcelBase):
    def __init__(self):
        super(RelMysql, self).__init__()
        self.gy_fetch = [
            ("company_name", "compName"),
            ("cname", "objName"),
            ("company_type", "objCat"),
            "period", "mark"]
        self.zb_fetch = [
            ("projectID", 'projectId'), ('bidderNO', 'bidderNo'),
            'projectName', 'projectInitiator', 'projectBidder', 'bidderTime', 'bidderProxy',
            'amount', 'websiteName', 'projectWebsite', 'contentUrl', 'projectInitiatorName', 'projectBidderName',
            'bidderTimeConfirm', 'proviceName', 'city', 'district', 'source', 'contractName']
        self.gy_field = ["compName", "objName", "objCat", "mark", "period"]
        self.zb_field = [
            'projectId', 'bidderNo', 'projectName', 'projectInitiator', 'projectBidder', 'bidderTime', 'bidderProxy',
            'amount', 'websiteName', 'projectWebsite', 'contentUrl', 'projectInitiatorName', 'projectBidderName',
            'bidderTimeConfirm', 'proviceName', 'city', 'district', 'source', 'contractName']

    def process(self, *args, **kwargs):
        names = ['神州数码集团股份有限公司', '深圳市长亮科技股份有限公司', '上海恒生聚源数据服务有限公司']
        # supply = self.supply(names)
        # self.save_to_mysql(self.gy_field, supply, "sy_sa_zxyh_ms_rela_supply")

        customer = self.customer(names)
        self.save_to_mysql(self.gy_field, customer, "sy_sa_zxyh_ms_rela_customer")

        zb = self.zhaobiao(names)
        zb_result = self.zhaobiao_detail(zb)

        self.save_to_mysql(self.zb_field, zb_result, "merge_bidder_info")

    def save_to_mysql(self, valid_fields, result_list, table):
        save_schema = {
            "insert_template": "insert into {} (%s) values %s".format(table),
            "result_list": result_list,
            "field_list": valid_fields,
            "db_key": "section_data_63306"}
        self._data_server.call("insert_sql_list", save_schema)

    # 供应商
    def supply(self, name_list):
        result = list()
        for idx in range(0, len(name_list), 10):
            names = name_list[idx:idx + 10]
            query_schema = {
                "db_name": "relation_data_v2",
                "collection_name": "company_relation_supply_flow_supply",
                "query_condition": {"cname": {"$in": names}},
                "query_field": {"_id": 0, "company_name": 1, "cname": 1, "period": 1, "mark": 1, "company_type": 1}}
            query_result = self._data_server.call("query_item", query_schema) or list()
            for item in query_result:
                item = self.fetch_dict(item, self.gy_fetch)
                result.append(item)
        return result

    # 销售客户
    def customer(self, name_list):
        result = list()
        for idx in range(0, len(name_list), 10):
            names = name_list[idx:idx + 10]
            query_schema = {
                "db_name": "relation_data_v2",
                "collection_name": "company_relation_supply_flow_customer",
                "query_condition": {"cname": {"$in": names}},
                "query_field": {"_id": 0, "company_name": 1, "cname": 1, "period": 1, "mark": 1, "company_type": 1}}
            query_result = self._data_server.call("query_item", query_schema) or list()
            for item in query_result:
                item = self.fetch_dict(item, self.gy_fetch)
                result.append(item)
        return result

    def zhaobiao(self, company_list):
        result = list()
        sql = """
             SELECT id, projectInitiatorName, projectBidderName FROM 
             merge_bidder_info WHERE id>'{}' order by id ASC limit 1000;"""
        for items in self._query_sql_iter_by_id(sql, "db_seeyii_128"):
            for item in items:
                # # 供应商
                # tb_name = item.get("projectBidderName")
                # if tb_name and tb_name in company_list:
                #     result.append(item)
                # 销售客户
                zz_name = item.get("projectInitiatorName")
                if zz_name and zz_name in company_list:
                    result.append(item)
        return result

    def zhaobiao_detail(self, item_list):
        result = list()
        for idx in range(0, len(item_list), 10):
            _ids = [i["id"] for i in item_list[idx: idx + 10]]
            id_str = ','.join(['{!r}'.format(name) for name in _ids])
            sql = """SELECT * from merge_bidder_info where id in ({});"""
            query_schema = dict(db_key="db_seeyii_128", sql_statement=sql.format(id_str))
            result_list = self._data_server.call("query_sql_item", query_schema) or list()
            for item in result_list:
                item = self.fetch_dict(item, self.zb_fetch)
                result.append(item)
        return result


if __name__ == '__main__':
    p = RelMysql()
    p.process()
