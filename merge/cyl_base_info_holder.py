# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/11

from core.excel_base import ExcelBase
from moduler.base_info_api_thread import BaseInfoMaster
from moduler.company_cyl_and_industry import ChinaToExcel
from moduler.company_holder import CompanyHolder


class CYLBaseInfo(ExcelBase):
    def __init__(self):
        super(CYLBaseInfo, self).__init__()
        self.cyl = ChinaToExcel()
        self.base = BaseInfoMaster()
        self.holder = CompanyHolder()

    def process(self, *args, **kwargs):
        result = list()
        name_items = self.cyl.run(["5G产业链"])
        name_list = list({i["company_name"] for i in name_items})
        base_info_dict = self.base.run(name_list)
        holder_dict = self.holder.run(name_list)
        print(len(name_list))
        for cyl_items in name_items:
            name = cyl_items["company_name"]
            base_info = base_info_dict[name]
            holder_info = holder_dict[name]
            cyl_items.update(base_info)
            cyl_items.update({"holder": ",".join([i.get("shareholder_name", "") for i in holder_info])})
            result.append(cyl_items)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'cyl_name': ('产业链名称', 0),
            'second_industry_name': ('视野行业名称', 1),
            'companyName': ('公司名称', 2),
            'market': ('资本市场', 3),
            'reg_province': ('所在地省', 4),
            'reg_city': ('所在地市', 5),
            'reg_district': ('所在地县区', 6),
            'reg_location': ('注册地址', 7),
            'estiblish_time': ('注册时间', 8),
            'reg_capital': ('注册资本', 9),
            'legal_person_name': ('法定代表人', 10),
            'reg_status': ('公司状态', 11),
            'holder': ('股东', 12)
        }
        self._excel_name = self.name_add_date("建设银行POC.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})


if __name__ == '__main__':
    p = CYLBaseInfo()
    p.process()
