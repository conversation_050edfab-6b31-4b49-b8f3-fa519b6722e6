# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/8/31
from collections import defaultdict

from core.excel_base import ExcelBase
from moduler.company_area_info import CompanyArea


class PX(ExcelBase):
    def __init__(self):
        super(PX, self).__init__()
        self.area = CompanyArea()

    def process(self):
        result, fig_set = list(), set()
        items = self.read_excel_data(self._in_file_path + "/谱系名单.xlsx")
        names = list({i["name"].replace("(", "（").replace(")", "）").strip() for i in items})
        print(f"names={len(names)}")
        px_dict, px_map = self.query_px_data(names)
        px_id_list = list(px_dict.keys())
        px_gm_dict = self.query_px_base(px_id_list)
        px_inv_stat_dict = self.query_px_inv_data(px_id_list)
        px_inv_cmp = self.query_px_inv(px_id_list)
        for px_id, org_list in px_dict.items():
            px_name = px_map[px_id]
            fig_set |= {i["name"] for i in org_list}
            px_gm = px_gm_dict.get(px_id) or dict()
            px_inv_stat = px_inv_stat_dict.get(px_id) or dict()
            inv_list = px_inv_cmp.get(px_id) or list()
            area_dict = self.area.process([i["cName"] for i in inv_list])
            inv_len = len(inv_list)
            org_len = len(org_list)
            if org_len > inv_len:
                for idx, org_item in enumerate(org_list):
                    new_dict = dict()
                    new_dict.update(org_item)
                    if idx < inv_len:
                        i_item = inv_list[idx]
                        new_dict.update(i_item)
                        n = i_item["cName"]
                        new_dict.update(area_dict.get(n) or dict())
                    new_dict.update(px_gm)
                    new_dict.update(px_inv_stat)
                    result.append(new_dict)
            else:
                for idx, inv_item in enumerate(inv_list):
                    new_dict = dict()
                    new_dict["spectrumName"] = px_name
                    new_dict.update(inv_item)
                    new_dict.update(area_dict.get(inv_item["cName"]) or dict())
                    if idx < org_len:
                        new_dict.update(org_list[idx])
                    new_dict.update(px_gm)
                    new_dict.update(px_inv_stat)
                    result.append(new_dict)
        other = set(names) - fig_set
        for i in other:
            result.append({"name": i})
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'spectrumName': ('谱系名称', 0),
            'name': ('机构名称', 1),
            'capScale': ('资本规模', 2),
            'invNum': ('历史投资数量', 3),
            'lat1invNum': ('近一年来投资数量', 4),
            'extNum': ('退出总数', 5),
            'lat1extNum': ('近一年来退出总数', 6),
            'invTurn': ('投资轮次', 7),
            'invField': ('投资领域', 8),
            'cName': ('企业名称', 9),
            'provinceName': ('省份名称', 10),
            'cityName': ('城市名称', 11),
            'firInd': ('所属一级行业', 12),
            'secInd': ('所属二级行业', 13),
            'legalRepresent': ('法定代表人', 14),
            'invDate': ('投资日期', 15),
        }
        self._excel_name = self.name_add_date("谱系投资企业及投融资事件.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def read_excel_data(self, file_name, sheet="Sheet1"):
        data_list = self._extract_data(file_name, sheet)
        return data_list

    def query_px_data(self, names):
        result, px_dict = defaultdict(list), dict()
        for idx in range(0, len(names), 100):
            name_list = names[idx:idx + 100]
            name_str = "','".join(name_list)
            sql_statement = """
            SELECT cName as name, spectrumName, b.spectrumId as spectrumId from dwd_ms_cn_px_pdt_info as a 
            right JOIN dwd_ms_cn_px_list as b 
            on a.spectrumId=b.spectrumId and a.dataStatus!=3 and b.dataStatus!=3
            where a.cName in ('{}') and nameType=1
            UNION
            SELECT cName as name, spectrumName, b.spectrumId as spectrumId from dwd_ms_cn_px_invo_info as a 
            right JOIN dwd_ms_cn_px_list as b 
            on a.spectrumId=b.spectrumId and a.dataStatus!=3 and b.dataStatus!=3
            where a.cName in ('{}') and nameType=1
            """
            query_schema = dict(db_key="seeyii_db", sql_statement=sql_statement.format(name_str, name_str))
            result_list = self._data_server.call("query_sql_item", query_schema) or list()
            for item in result_list:
                result[item["spectrumId"]].append(item)
                px_dict[item["spectrumId"]] = item["spectrumName"]
        return result, px_dict

    def query_px_inv_data(self, names):
        result = defaultdict(list)
        for idx in range(0, len(names), 100):
            name_list = names[idx:idx + 100]
            name_str = "','".join(name_list)
            sql_statement = """
            SELECT * from dwd_ms_cn_px_inv_stat where spectrumId in ('{}') and dataStatus!=3;
            """
            query_schema = dict(db_key="seeyii_db", sql_statement=sql_statement.format(name_str, name_str))
            result_list = self._data_server.call("query_sql_item", query_schema) or list()
            for item in result_list:
                result[item["spectrumId"]] = item
        return result

    def query_px_inv(self, names):
        result = defaultdict(list)
        for idx in range(0, len(names), 100):
            name_list = names[idx:idx + 100]
            name_str = "','".join(name_list)
            sql_statement = """
            SELECT * from dwd_me_trad_cn_px_inv_comp where spectrumId in ('{}') and dataStatus!=3;
            """
            query_schema = dict(db_key="seeyii_db", sql_statement=sql_statement.format(name_str, name_str))
            result_list = self._data_server.call("query_sql_item", query_schema) or list()
            for item in result_list:
                result[item["spectrumId"]].append(item)
        return result

    def query_px_base(self, names):
        result = dict()
        for idx in range(0, len(names), 100):
            name_list = names[idx:idx + 100]
            name_str = "','".join(name_list)
            sql_statement = """
               SELECT spectrumId, capScale from dwd_ms_cn_px_info where spectrumId in ('{}') and dataStatus!=3;
               """
            query_schema = dict(db_key="seeyii_db", sql_statement=sql_statement.format(name_str, name_str))
            result_list = self._data_server.call("query_sql_item", query_schema) or list()
            for item in result_list:
                result[item["spectrumId"]] = item
        return result


if __name__ == '__main__':
    p = PX()
    p.process()
