# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/04/21
from collections import defaultdict

from core.excel_base import ExcelBase
from moduler.base_info_api_thread import BaseInfoMaster
from moduler.company_ipo_holder import CompanyIPOHolder
from moduler.company_ipo_three_holder import CompanySBIPOHolder

alias_map = {
    "书香门地（上海）美学家居股份有限公司": "书香门地集团股份有限公司"
}


class IPOHolderData(ExcelBase):
    def __init__(self):
        super(IPOHolderData, self).__init__()
        self.market = {
            "101": "主板",
            "201": "中小板",
            "301": "创业板",
            "302": "创业板",
            "401": "科创板"}
        self.stat = {
            "101": "启动上市辅导",
            "102": "完成辅导验收",
            "103": "终止上市辅导",
            "201": "已受理",
            "202": "已问询",
            "203": "已反馈",
            "204": "预披露",
            "205": "更新预披露",
            "206": "中止",
            "207": "终止（审核不通过）",
            "208": "终止（撤回）",
            "209": "发审会通过",
            "210": "发审会未通过",
            "211": "暂缓表决",
            "212": "上市委会议通过",
            "213": "上市委会议未通过",
            "214": "暂缓审议",
            "215": "复审委会议通过",
            "216": "复审委会议未通过",
            "217": "提交注册",
            "218": "注册生效",
            "219": "不予注册",
            "220": "终止注册",
            "221": "证监会核准",
            "222": "证监会不予核准",
            "223": "补充审核",
            "301": "已发行上市",
            "302": "发行失败",
            "303": "发行暂缓"}
        self.exchange_map = {
            "101": "上交所",
            "201": "深交所"}
        self.list_fetch = [
            "id",
            "projId",
            "compName",
            "shortName",
            "finaAmount",
            "reguInstitution",
            "preName",
            "brokName",
            "acctName",
            "lawName",
            ("exchange", "exchange", lambda x: self.exchange_map[str(x)]),
            ("ipo_market", "ipo_market", lambda x: self.market[str(x)]),
            "acceDate",
            ("currStat", "currStat", lambda x: self.stat[str(x)]),
            "statDate",
            "fingerId",
            "dataStatus",
            "createTime",
            "modifyTime"]
        self.stop_status = {
            "终止上市辅导",
            "终止（审核不通过）",
            "终止（撤回）",
            "发审会未通过",
            "上市委会议未通过",
            "复审委会议未通过",
            "不予注册",
            "终止注册",
            "发行失败",
        }
        self.base = BaseInfoMaster()
        self.holder = CompanyIPOHolder()
        self.sb_holder = CompanySBIPOHolder()

    def process(self):
        result = list()
        a_names = self.query_a_name()
        ipo_dict = self.get_ipo_info(a_names, alias_map)
        names = list(ipo_dict.keys())
        base_dict = self.base.run(names)
        for name, ipo_item in ipo_dict.items():
            base_info = base_dict.get(name, dict())
            mark = str(base_info.get("market", ""))
            if "三板公司" in mark:
                holder_dict = self.sb_holder.run(name)
            else:
                holder_dict = self.holder.run(name)
            if holder_dict:
                for raw_name, holder_list in holder_dict.items():
                    for holder in holder_list:
                        ipo = ipo_dict.get(raw_name, dict())
                        holder.update(ipo)
                        holder.update(base_info)
                        result.append(holder)
            else:
                ipo_item.update(base_info)
                result.append(ipo_item)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'compName': ('公司名称', 0),
            'credit_code': ('统一信用代码', 1),
            'currStat': ('IPO最新状态', 2),
            'status_type': ('IPO状态类别', 3),
            'statDate': ('IPO最新状态时间', 4),
            'ipo_market': ('板块', 5),
            'finaAmount': ('计划募集资金金额', 6),
            'reg_province': ('注册省', 7),
            'reg_city': ('注册市', 8),
            'legal_person_name': ('法定代表人', 9),
            'shHolderName': ('股东姓名', 10),
            'holderRTO': ('持股比例', 11),
            'holderamt': ('持股数（万股）', 12),
            'curchg': ('本期持股变动数（万股）', 13),
            'limitHolderamt': ('有限售持股数（万股）', 14),
            'unlimHolderamt': ('无限售持股数（万股）', 15),
            'endDate': ('日期', 16)}

        self._excel_name = self.name_add_date("IPO股东信息.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def query_ipo_data(self):
        result = defaultdict(list)
        sql_statement = """SELECT compName, market as ipo_market, currStat, statDate, preName,
         brokName, acctName, lawName, finaAmount
         from dws_me_trad_ipo_base where dataStatus !=3 and currStat!=301"""
        query_schema = dict(db_key="seeyii_db", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            item_result = self.fetch_dict(item, self.list_fetch)
            result[item_result["compName"]].append(item_result)
        return result

    def query_a_name(self):
        query_schema = {
            "db_name": "category_data",
            "collection_name": "company_category",
            "query_condition": {"is_valid": True,
                                "category": {"$in": ["ACompany"]}},
            "query_field": {"_id": 0, "cname": 1}}
        query_result = self._data_server.call("query_item", query_schema)
        return [i["cname"] for i in query_result]

    def get_ipo_info(self, a_name_list, alias_map):
        result = dict()
        item_dict = self.query_ipo_data()
        for name, item_list in item_dict.items():
            raw_item = self.get_ipo_item(item_list)
            if raw_item:
                result_item = raw_item
            else:
                result_item = max(item_list, key=lambda x: x["statDate"])
            stat = result_item["currStat"]
            name = result_item["compName"]
            result_item["compName"] = alias_map.get(name, name)
            if name in a_name_list:
                continue
            if stat in self.stop_status:
                result_item["status_type"] = "IPO已停止"
            else:
                result_item["status_type"] = "IPO进行中"
            result.setdefault(alias_map.get(name, name), result_item)
        return result

    @staticmethod
    def get_ipo_item(item_list):
        last_stat = set()
        new_item = max(item_list, key=lambda x: x["statDate"])
        max_date = str(new_item["statDate"])
        for item in item_list:
            stat = item["currStat"]
            stat_date = str(item["statDate"])
            if str(max_date) == stat_date:
                last_stat.add(stat)
        if last_stat == {'启动上市辅导', '终止上市辅导'}:
            for raw in item_list:
                r_stat = raw["currStat"]
                if r_stat == "启动上市辅导":
                    return raw


if __name__ == '__main__':
    p = IPOHolderData()
    p.process()
