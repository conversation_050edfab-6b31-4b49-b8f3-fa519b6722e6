# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2023/9/13
import re

import paramiko

from db.engine.data_services import DataServer

regex_str = u"年年度报告$"
zip_path = "/data/upload"


class AGG():
    def __init__(self):
        super(AGG, self).__init__()
        self._data_server = DataServer.get_instance()

    def process(self):
        ssh = self.connect_ssh("*************")
        self.gen_gg_info(ssh)

    def copy_pdf(self, d_url, ssh):
        # copy
        old_file_path = zip_path + d_url
        new_file_path = "/shiye_data/lys/"
        cp_command = "cp {} {}".format(old_file_path, new_file_path)
        stdin, stdout, stderr = ssh.exec_command(cp_command)
        print(stdout.read())

    @staticmethod
    def connect_ssh(host):
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(host, username="root", password="shiye1805A", allow_agent=True)
        return ssh

    def gen_gg_info(self, ssh):
        result, code_set = list(), set()
        query_condition = dict()
        offset = None
        while True:
            if offset:
                query_condition["_id"] = {"$gt": offset}
            query_item = {
                "db_name": "ggdb",
                "collection_name": "a_gg_down_status",
                "query_condition": query_condition,
                "query_field": {
                    '_id': 1, "ggsj": 1, "url": 1, "d_url": 1, "title": 1, "stock_code": 1, "d_status": 1},
                "sort_field": [("_id", 1)],
                "limit_n": 100}
            query_result = self._data_server.call("query_item", query_item)
            if not query_result:
                break
            for item in query_result:
                if re.search(regex_str, item["title"]) is not None and item["ggsj"] >= "2023" and len(result) < 100 and \
                        item["stock_code"] not in code_set and item["d_status"]==300:
                    print(item)
                    result.append(item)
                    code_set.add(item["stock_code"])
                    self.copy_pdf(item["d_url"], ssh)
            offset = query_result[-1]["_id"]
        print(len(result))
        return result


if __name__ == '__main__':
    p = AGG()
    p.process()
