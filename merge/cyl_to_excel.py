# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/11
import json
import requests
import time
from core.excel_base import ExcelBase
from moduler.company_cyl_and_industry import ChinaToExcel
from moduler.company_gb_industry import CompanyGBIndustry


class CYLData(ExcelBase):
    def __init__(self):
        super(CYLData, self).__init__()
        self.__area_field_name_list = ["province", "city", "district", "adcode"]

    def process(self, *args, **kwargs):
        result = list()
        cyl = ["汽车制造产业链", "新能源汽车产业链", "锂电池产业链"]
        cyl_info = ChinaToExcel().run(cyl)
        num = 0
        for idx in range(0, len(cyl_info), 50):
            items = cyl_info[idx: idx + 50]
            num += 50
            print(f"num={num} all={len(cyl_info)}")
            names = [item['company_name'] for item in items]
            base_info = self.query_mysql_base_info(names)  # 基本信息
            base_dict = dict()
            for base in base_info:
                name = base['name']
                reg_location = base.get("reg_location")
                if reg_location:
                    area_info = self.query_gaode_api(reg_location) or dict()
                    base.update(area_info)
                base_dict.setdefault(name, base)
            ind_map = CompanyGBIndustry.run(names)  # 国标行业
            for item in items:
                name = item["company_name"]
                ind = ind_map.get(name, dict())
                item.update(ind)
                base = base_dict.get(name, dict())
                item.update(base)
                result.append(item)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'company_name': ('公司名称', 0),
            'second_industry_name': ('视野行业', 1),
            'ind_m': ('国民经济行业（门类）', 2),
            'ind_d': ('国民经济行业（大类）', 3),
            'ind_z': ('国民经济行业（中类）', 4),
            'reg_location': ('注册地址', 5),
            'property1': ('统一社会信用码', 6),
            'reg_status': ('公司状态', 7),
            'reg_province': ('所属省', 8),
            'reg_city': ('所属市', 9),
            'c_name': ('产业链名称', 10),
        }
        self._excel_name = self.name_add_date("浦发银行POC数据.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def query_mysql_base_info(self, names):
        sql = """SELECT reg_location, property1, reg_status, name FROM `company`  WHERE name in ({}) """
        name_str = ','.join(['{!r}'.format(name) for name in names])
        query_schema = dict(db_key="company", sql_statement=sql.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list

    def query_gaode_api(self, address_str, retry_count=3):
        try:
            tem_url = "http://restapi.amap.com/v3/geocode/geo?address=%s&output=" \
                      "JSON&key=bcca9ff29f3f34473cf9129eeec173a4"
            headers = {
                "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) "
                              "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3944.0 Safari/537.36"}
            interface_url = tem_url % address_str
            response = requests.get(interface_url, headers=headers).text
            response_content = json.loads(response)
            if self.is_valid_area_response(response_content) is False:
                return dict()
            addr_info = self.transform_area(response_content)
            return addr_info
        except:
            if retry_count > 0:
                time.sleep(2)
                return self.query_gaode_api(address_str, retry_count - 1)
            return dict()

    def transform_area(self, raw_area):
        province_name = raw_area["geocodes"][0]["province"]
        city_name = raw_area["geocodes"][0]["city"]
        district_name = raw_area["geocodes"][0]["district"]
        district_code = raw_area["geocodes"][0]["adcode"]

        reg_province_code = district_code[:2] + "0000"
        reg_province = province_name
        reg_city_code = district_code[:4] + "00"
        reg_city = city_name
        if isinstance(city_name, list) is True or province_name == city_name:
            reg_city_code = district_code
            reg_city = district_name

        result_item = dict()
        if isinstance(reg_province, list) is False:
            result_item["reg_province_code"] = reg_province_code
            result_item["reg_province"] = reg_province
        if isinstance(reg_city, list) is False:
            result_item["reg_city_code"] = reg_city_code
            result_item["reg_city"] = reg_city
        if isinstance(district_name, list) is False:
            result_item["reg_district_code"] = district_code
            result_item["reg_district"] = district_name
        return result_item

    def is_valid_area_response(self, response_content):
        status_code = response_content.get("status", "0")
        if status_code != "1":
            return False
        geocodes = response_content.get("geocodes", None)
        if geocodes is None or len(geocodes) == 0:
            return False
        for field_name in self.__area_field_name_list:
            if field_name not in geocodes[0]:
                return False
        adcode = geocodes[0]["adcode"]
        if isinstance(adcode, list) is True:
            return False
        return True


if __name__ == '__main__':
    CYLData().run()
