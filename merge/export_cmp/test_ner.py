#-*-coding:utf-8-*-


import time
from merge.export_cmp.ner_bc import BertClient

def model3():
    '''
    调用模型3
    '''
    return BertClient(show_server_config=False,
                      check_version=False,
                      check_length=False,
                      ip='*************',
                      port=6666,
                      port_out=6667,
                      mode='NER',
                      timeout=60000)

def model4():
    '''
    调用模型4
    '''
    return BertClient(show_server_config=False,
                      check_version=False,
                      check_length=False,
                      ip='*************',
                      port=6668,
                      port_out=6669,
                      mode='NER',
                      timeout=60000)


bclient3 = model3()
bclient4 = model4()


text = '2020年9月28日上午10点18分,北京人寿保险股份有限公司北京市顺义支公司(以下简称“北京人寿顺义支公司”)在顺义区后沙峪镇国门一号举行了开业庆典仪式,标志着北京人寿顺义支公司正式揭牌成立。北京人寿保险股份有限公司党委书记、董事长郭光磊;北京顺鑫控股集团有限公司党委书记、董事长王泽;顺义区金融服务办公室党组书记、主任王卿;顺义区各委办局、临空经济核心区、顺义区各乡镇辖区企业以及北京人寿保险股份有限公司员工出席了本次庆典活动,共同见证了这一重要时刻!北京人寿本着“诚信、敬业、专业、创新”的核心价值观,以“北京人寿 首善人生”为愿景目标,将“保险业姓保”更好的付诸于实践,以良好的品牌形象参与保险市场建设和发展,努力实现客户价值最大化和股东价值持续增长,争创首善卓越、受人尊敬的优秀保险企业。北京分公司副总经理兼顺义支公司经理张欣宇在致辞中表示:“顺义支公司已经完成了初步的战略布局,以监管部门提出的保险姓保、保险扶贫、服务民生为己任,以为百姓解难、替政府分忧为努力目标,真正让顺义人民感受到北京人寿身边亲人的服务理念,实现北京人寿首善人生的美好愿景。顺义支公司未来的发展目标是:顺义市场保费占有率三年前五,五年第一!依法合规做业务,实事求是谋发展,顺义支公司努力为公司、为股东创造更多的价值,在保险服务上让顺义人民顺心顺意,使北京人寿品牌在顺义落地生根,开花结果。”北京人寿保险股份有限公司董事长郭光磊先生首先代表北京人寿党委和董事会对顺义支公司的成立表示祝贺,并指出:“一切经营以客户的需求为导向,使顺义百姓得到最优、最精良的专业化保险服务;保险业要讲诚信,践行北京人寿走稳、走好、走远的发展目标;作为一家有远大理想和目标的金融机构,合规发展是长远发展的根本,要守住合规底线,将合规经营作为北京人寿的标杆。”北京顺鑫控股集团有限公司党委书记、董事长王泽代表股东做了重要讲话,他指出:“北京人寿顺义支公司正式成立并开业标志着北京人寿进入更快的发展轨道、走上更大的发展平台,要继续践行初心使命,依法合规经营,提升业务发展质量,追求经济效益与社会效益的完美集合,尽属地责任,担政治任务,为顺义区百姓提供更优质的保险服务,为北京建设国际一流的和谐宜居之都首善之区发挥更大作用,为保险行业规范经营做出更大贡献。”顺义区金融服务办公室党组书记、主任王卿代表顺义区金融办做了重要讲话。北京人寿作为以“北京”命名的人身保险公司,于2018年2月14日成功落户后沙峪金融商务区,成为了顺义首家实质意义上的寿险总部,是区内保险公司的翘楚,为顺义金融产业发展注入了强大活力。今天,北京人寿顺义支公司的开业,无疑又给顺义经济社会的发展增添了血液和动力。“雄关漫道真如铁,而今迈步从头越”,顺义支公司将以百倍的信心与激情、专业合规的销售行为,把保险的大爱播撒到顺义的每一个家庭,实现“北京人寿 首善人生”的伟大愿景!'

text = text.replace('＊','*').replace('\xa0','').replace('\r','').replace('\n','').replace('\u3000','')


# 模型3提取
print('模型3提取')
try:
    time_start=time.time()
    result_list = bclient3.process_seq(text)
    print('模型3',result_list,len(result_list))
    time_end=time.time()
    print('totally cost',time_end-time_start)
except:
    print('预测服务中断')


# 模型4提取
print('模型4提取')
try:
    time_start=time.time()
    result_list = bclient4.process_seq(text)
    print('模型4',result_list,len(result_list))
    time_end=time.time()
    print('totally cost',time_end-time_start)
except:
    print('预测服务中断')

