# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2024/3/12
import re

def sj_bach(sql):
    b = sql.replace('"', "'").replace("\\", "\\\\").replace("'|", '\\"|').replace("", '')
    a = re.sub("[\n\t\s]+", " ", b)
    d = re.search("'SJ.*?'", sql).group(0)

    e = """
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  {},
        "{}",
        map("tempview1", ""),
        "7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       """.format(d, a)
    # if "|。" in e:
    #     raise
    result = replace_last_occurrence(e)
    with open("./sj_out_bc1.txt", 'a', encoding='utf-8') as f:
        f.write(result)

def replace_last_occurrence(string):
    target = "eigenvalue"
    replacement = "eigenvalue,null as retrovalue"
    last_occurrence_index = string.rfind(target)
    if last_occurrence_index != -1:
        return string[:last_occurrence_index] + replacement + string[last_occurrence_index + len(target):].replace("", '')
    else:
        return string.replace("", '')


if __name__ == '__main__':
    sql = """
with subsist_list as (
  select 
    id, 
    relieveDate as eventdate, 
    ipType, 
    tsSubType, 
    ipName, 
    invenName 
  from 
    seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc 
  where 
    filedate in (
      select 
        max(filedate) 
      from 
        seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc
    ) 
    AND datastatus != 3
), 
sk_stock as (
  select 
    id, 
    compcode, 
    compname 
  from 
    seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc_se 
  where 
    filedate in (
      select 
        max(filedate) 
      from 
        seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc_se
    ) 
    AND datastatus != 3 
    and typecode = 1
), 
join_tb as (
  select 
    a.compcode as subjectcode, 
    a.compname as eventsubject, 
    b.eventdate, 
    ipType, 
    tsSubType, 
    ipName, 
    invenName, 
    concat(
      '100162', '&#&', ipType, '&#&', '3'
    ) as eigenvalue 
  from 
    sk_stock as a 
    join subsist_list as b on a.id = b.id
), 
next_df as (
  select 
    *, 
    CAST(null AS STRING) as url, 
    CAST(null AS STRING) as expiredate, 
    concat(
      eventdate, '，公司专利权质押合同解除登记'
    ) as a, 
    if(
      (ipType is null) 
      or (ipType = ''), 
      '', 
      concat('，专利类型为', ipType)
    ) as b, 
    if(
      (tsSubType is null) 
      or (tsSubType = ''), 
      '', 
      concat(
        '，细分事务分类为', tsSubType
      )
    ) as c, 
    if(
      (ipName is null) 
      or (ipName = ''), 
      '', 
      concat('，专利名称为', ipName)
    ) as d, 
    if(
      (eventsubject is null) 
      or (eventsubject = ''), 
      '', 
      concat('，质权人为', eventsubject)
    ) as e, 
    if(
      (invenName is null) 
      or (invenName = ''), 
      '', 
      concat('，发明名称为', invenName)
    ) as f, 
    '。' g 
  from 
    join_tb 
  where 
    eventdate is not NULL
), 
final_df as (
  select 
    *, 
    concat_ws('', a, b, c, d, e, f, g) as desc1 
  from 
    next_df
)  insert into {ku}.{tb} partition (filedate = '{fileDate}') 
select 
  fingerId_row(
    concat_ws(
      ',', 
      if(
        eventsubject is NULL, '#', eventsubject
      ), 
      if(
        subjectcode is NULL, '#', subjectcode
      ), 
      'SJ000208003', 
      if(desc1 is NULL, '#', desc1), 
      if(url is NULL, '#', url), 
      if(eventdate is NULL, '#', eventdate), 
      if(
        expiredate is NULL, '#', expiredate
      )
    )
  ) as eventid, 
  subjectcode, 
  eventsubject, 
  'SJ000208003' as eventtype, 
  desc1 as desc, 
  url, 
  eventdate, 
  expiredate, 
  null as property1, 
  null as property2, 
  null as property3, 
  null as property4, 
  eigenvalue, 
  1 as datastatus, 
  date_format(
    current_timestamp(), 
    'yyyy-MM-dd HH:mm:ss'
  ) as modifytime 
from 
  final_df
     """
    sj_bach(sql)
