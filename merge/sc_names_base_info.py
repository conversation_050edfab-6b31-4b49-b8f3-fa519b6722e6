# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/24
import regex
from copy import deepcopy
from collections import defaultdict

from core.excel_base import ExcelBase
from moduler.base_info_api_thread import BaseInfoMaster
from moduler.company_area_info import CompanyArea
from moduler.company_cur_name import CurName
from moduler.company_cyl_and_industry import ChinaToExcel
from moduler.company_gb_industry import CompanyGBIndustry
from moduler.company_law_data import CompanyCPWS, CompanyFYGG, CompanyKTGG
from moduler.company_mysql_reward import MysqlReward
from moduler.company_repoet_shebao import She<PERSON><PERSON>
from moduler.company_supply_data import CompanySupply


class SCCYL(ExcelBase):
    """
    中行四川分行价值企业V2.0
    https://docs.qq.com/sheet/DSG9HTGVyQ3lndGRI?tab=BB08J2
    """

    def __init__(self):
        super(SCCYL, self).__init__()
        self.cyl = ChinaToExcel()
        self.base = BaseInfoMaster()
        self.gb_ind = CompanyGBIndustry()
        self.gys = CompanySupply()
        self.shebao = SheBao()
        self.jiangli = MysqlReward()
        self.area = CompanyArea()
        self.cpws = CompanyCPWS()
        self.fygg = CompanyFYGG()
        self.ktgg = CompanyKTGG()
        self.alias = CurName()
        self.pattern = regex.compile(r"注销|取消|撤销|吊销|不予换发|过期|作废|变更|收缴|退出|停业")
        self.market = {
            "101": "主板",
            "201": "中小板",
            "301": "创业板",
            "302": "创业板",
            "401": "科创板"}
        self.stat = {
            "101": "启动上市辅导",
            "102": "完成辅导验收",
            "103": "终止上市辅导",
            "201": "已受理",
            "202": "已问询",
            "203": "已反馈",
            "204": "预披露",
            "205": "更新预披露",
            "206": "中止",
            "207": "终止（审核不通过）",
            "208": "终止（撤回）",
            "209": "发审会通过",
            "210": "发审会未通过",
            "211": "暂缓表决",
            "212": "上市委会议通过",
            "213": "上市委会议未通过",
            "214": "暂缓审议",
            "215": "复审委会议通过",
            "216": "复审委会议未通过",
            "217": "提交注册",
            "218": "注册生效",
            "219": "不予注册",
            "220": "终止注册",
            "221": "证监会核准",
            "222": "证监会不予核准",
            "223": "补充审核",
            "301": "已发行上市",
            "302": "发行失败",
            "303": "发行暂缓"}
        self.exchange_map = {
            "101": "上交所",
            "201": "深交所"}
        self.list_fetch = [
            "id",
            "projId",
            "compName",
            "shortName",
            "finaAmount",
            "reguInstitution",
            "preName",
            "brokName",
            "acctName",
            "lawName",
            ("exchange", "exchange", lambda x: self.exchange_map[str(x)]),
            ("market", "ss_market", lambda x: self.market[str(x)]),
            "acceDate",
            ("currStat", "currStat", lambda x: self.stat[str(x)]),
            "statDate",
            "fingerId",
            "dataStatus",
            "createTime",
            "modifyTime"]

    def process(self, *args, **kwargs):
        result = list()
        # ipo公司名单
        ipo_data = self.get_ipo_info()
        names = [
            '中科院成都信息技术股份有限公司', '乐山大佛旅游投资开发（集团）有限公司', '乐山川天燃气输配设备有限公司', '乐山电力股份有限公司', '乐山高新投资发展（集团）有限公司',
            '仁寿县鑫城建设开发有限责任公司', '创信工程咨询股份有限公司', '利尔化学股份有限公司', '剑门关华侨城旅游开发股份有限公司', '北川九皇山生态旅游股份有限公司', '北川禹珍实业有限公司',
            '北方化学工业股份有限公司', '华融化学股份有限公司', '厚普清洁能源股份有限公司', '台沃科技集团股份有限公司', '吉峰三农科技服务股份有限公司', '四川三元环境治理股份有限公司',
            '四川三星新材料科技股份有限公司', '四川三松医疗管理集团有限公司', '四川东方水利智能装备工程股份有限公司', '四川东材科技集团股份有限公司', '四川东立科技股份有限公司',
            '四川东钢新材料股份有限公司', '四川中物技术股份有限公司', '四川中物材料股份有限公司', '四川九州光电子技术有限公司', '四川九洲光电科技股份有限公司', '四川九洲电器股份有限公司',
            '四川代代为本农业科技有限公司', '四川健佰氏医药股份有限公司', '四川光辉好口碑农业发展有限公司', '四川六合特种金属材料股份有限公司', '四川准达信息技术股份有限公司',
            '四川凤生纸业科技股份有限公司', '四川利尔作物科学有限公司', '四川千行你我科技股份有限公司', '四川华丰企业集团有限公司', '四川华拓光通信股份有限公司', '四川华泰电气股份有限公司',
            '四川南联环资科技股份有限公司', '四川双马水泥股份有限公司', '四川合纵药易购医药股份有限公司', '四川君和环保股份有限公司', '四川君逸数码科技股份有限公司', '四川唯怡饮料食品有限公司',
            '四川唯鸿生物科技股份公司', '四川国为制药有限公司', '四川圣迪乐村生态食品股份有限公司', '四川圣锦高新科技股份有限公司', '四川大宇信息系统股份有限公司', '四川天味食品集团股份有限公司',
            '四川天微电子股份有限公司', '四川天邑康和通信股份有限公司', '四川奇石缘科技股份有限公司', '四川安宁铁钛股份有限公司', '四川安泰茧丝绸集团有限公司', '四川安达农森科技股份有限公司',
            '四川宏达股份有限公司', '四川川投能源股份有限公司', '四川广安爱众股份有限公司', '四川德博尔生物科技股份有限公司', '四川德尔博睿科技股份有限公司', '四川德康农牧食品集团股份有限公司',
            '四川德恩精工科技股份有限公司', '四川意龙科纺集团有限公司', '四川戎春酒业集团有限公司', '四川成渝高速公路股份有限公司', '四川成飞集成科技股份有限公司', '四川我要去哪科技股份有限公司',
            '四川新一美生物科技有限公司', '四川新威环境服务股份有限公司', '四川新翔智联科技有限公司', '四川新迎顺信息技术股份有限公司', '四川新金路集团股份有限公司',
            '四川新闻网传媒(集团)股份有限公司', '四川普什醋酸纤维素有限责任公司', '四川景云祥通信股份公司', '四川梓橦宫药业股份有限公司', '四川永强机械施工股份有限公司',
            '四川汇宇制药股份有限公司', '四川汇安融信息技术服务有限公司', '四川泸天化股份有限公司', '四川润和催化新材料股份有限公司', '四川港通医疗设备集团股份有限公司',
            '四川点石能源股份有限公司', '四川爱创科技有限公司', '四川爱客信生物科技股份有限公司', '四川爱联科技有限公司', '四川特飞科技股份有限公司', '四川白家食品产业有限公司',
            '四川白家食品产业股份有限公司', '四川百夫长清真饮品股份有限公司', '四川省中明环境治理有限公司', '四川省吉香居食品有限公司', '四川省宜宾圣山服装家纺有限公司',
            '四川省宜宾惠美线业有限责任公司', '四川省宜宾普什智能科技有限公司', '四川省宜宾普什模具有限公司', '四川省宜宾普什驱动有限责任公司', '四川省燃气集团有限公司', '四川省玻纤集团有限公司',
            '四川省绵阳市华意达化工有限公司', '四川省羌山农牧科技股份有限公司', '四川省银河化学股份有限公司', '四川科新机电股份有限公司', '四川科瑞德制药股份有限公司', '四川科瑞软件有限责任公司',
            '四川科莱电梯股份有限公司', '四川纵横六合科技股份有限公司', '四川羽玺新材料股份有限公司', '四川自立机械有限公司', '四川苏格通讯技术有限公司', '四川英创力电子科技股份有限公司',
            '四川英杰电气股份有限公司', '四川观堂建筑工程设计股份有限公司', '四川豪特精工装备股份有限公司', '四川赛狄信息技术股份公司', '四川远丰森泰能源集团股份有限公司', '四川郎酒股份有限公司',
            '四川金星清洁能源装备股份有限公司', '四川金石租赁股份有限公司', '四川金象赛瑞化工股份有限公司', '四川长仪油气集输设备股份有限公司', '四川长虹教育科技有限公司',
            '四川长虹新能源科技股份有限公司', '四川长虹电器股份有限公司', '四川长虹电源有限责任公司', '四川隧唐科技股份有限公司', '四川雅化实业集团股份有限公司', '四川飞亚动力科技股份有限公司',
            '四川鱼鳞图信息技术股份有限公司', '国久大数据有限公司', '国机重型装备集团股份有限公司', '国药集团宜宾制药有限责任公司', '国金证券股份有限公司', '威特龙消防安全集团股份公司',
            '宜宾丰源盐业有限公司', '宜宾五尺道集团有限公司', '宜宾凯翼汽车有限公司', '宜宾天亿新材料科技有限公司', '宜宾天原集团股份有限公司', '宜宾市四方射钉制造有限公司',
            '宜宾市申酉辰明威农业发展有限公司', '宜宾海丰和锐有限公司', '宜宾纸业股份有限公司', '宜宾锂宝新材料有限公司', '尚纬股份有限公司', '峨眉山旅游股份有限公司',
            '巴中市巴山牧业股份有限公司', '幺麻子食品股份有限公司', '广元蜀塔电缆有限公司', '康泰塑胶科技集团有限公司', '德阳天元重工股份有限公司', '德阳市中嘉实业股份有限公司',
            '成都一通密封股份有限公司', '成都三泰控股集团股份有限公司', '成都中医大银海眼科医院股份有限公司', '成都九鼎瑞信科技股份有限公司', '成都云图控股股份有限公司',
            '成都依能科技股份有限公司', '成都倍特药业股份有限公司', '成都光大灵曦科技发展股份有限公司', '成都共同管业集团股份有限公司', '成都利君实业股份有限公司',
            '成都华美牙科连锁管理股份有限公司', '成都卡乐福高分子材料股份有限公司', '成都卫士通信息产业股份有限公司', '成都可为科技股份有限公司', '成都圣诺生物制药有限公司',
            '成都圣诺生物科技股份有限公司', '成都坤恒顺维科技股份有限公司', '成都大宏立机器股份有限公司', '成都安好精工机械股份有限公司', '成都安美科燃气技术股份有限公司',
            '成都宏基建材股份有限公司', '成都巨龙生物科技股份有限公司', '成都市兴蓉环境股份有限公司', '成都市大数据股份有限公司', '成都市新筑路桥机械股份有限公司', '成都广达新网科技股份有限公司',
            '成都康弘药业集团股份有限公司', '成都彩虹电器(集团)股份有限公司', '成都拜欧药业股份有限公司', '成都振芯科技股份有限公司', '成都智明达电子股份有限公司', '成都极米科技股份有限公司',
            '成都欧康医药股份有限公司', '成都欧林生物科技股份有限公司', '成都正恒动力股份有限公司', '成都燃气集团股份有限公司', '成都百裕制药股份有限公司', '成都索贝数码科技股份有限公司',
            '成都纵横自动化技术股份有限公司', '成都联星技术股份有限公司', '成都至诚华天复合材料股份有限公司', '成都西部泰力智能设备股份有限公司', '成都豪能科技股份有限公司',
            '成都赛肯思创享生活景观设计股份有限公司', '成都趣睡科技股份有限公司', '成都达智咨询股份有限公司', '成都邦普切削刀具股份有限公司', '成都锐思环保技术股份有限公司',
            '成都锦欣康养医院管理有限公司', '成都雷电微力科技股份有限公司', '成都高新发展股份有限公司', '成都高赛尔股份有限公司', '攀枝花卓越钒业科技股份有限公司', '攀枝花秉扬科技股份有限公司',
            '攀钢集团钒钛资源股份有限公司', '新华文轩出版传媒股份有限公司', '新希望乳业股份有限公司', '新希望六和股份有限公司', '正源控股股份有限公司', '泸州老窖股份有限公司',
            '海天水务集团股份公司', '海诺尔环保产业股份有限公司', '眉山环天水务有限公司', '眉山车辆工业股份有限公司', '眉山顺应动力电池材料有限公司', '绵阳麦思威尔科技有限公司',
            '老肯医疗科技股份有限公司', '自贡市川力科技股份有限公司', '舍得酒业股份有限公司', '超凡知识产权服务股份有限公司', '迈克生物股份有限公司', '迈普通信技术股份有限公司',
            '通威股份有限公司', '锦泰财产保险股份有限公司', '领地集团有限公司']
        name_cur_dict = self.query_cur_name(names)
        other_name_dict = {name_cur_dict.get(i, i): dict() for i in names if not i in ipo_data}
        other_name_dict.update(ipo_data)
        print(len(other_name_dict))
        self.query_cmp_info(other_name_dict)

    def query_cmp_info(self, raw_names_dict):
        names = list(raw_names_dict.keys())
        base_info = self.base.run(names)
        print(len(base_info))
        base_list = self.data_process(base_info, raw_names_dict)
        # 国标行业
        gb_dict = self.gb_ind.run(list(names))
        # 社保数据
        sb_dict = self.shebao.run(list(names))
        # 奖励数据
        jl_dict = self.jiangli.run(list(names))
        # 供应商
        raw_gys_dict = self.gys.run(list(names))
        gys_dict = self.gys_data_process(raw_gys_dict)
        # 财务数据
        yl_dict = self.query_yl_data()
        fz_dict = self.query_fz_data()
        self.get_data(gb_dict, sb_dict, jl_dict, base_list, gys_dict, yl_dict, fz_dict)
        # 法律数据处理
        self.law_process(list(names), base_list)
        self.save_data_excel(base_list)

    def query_ipo_data(self):
        result = defaultdict(list)
        sql_statement = """SELECT compName, market, currStat, statDate, 
        preName, brokName, acctName, lawName, finaAmount from 
        dws_me_trad_ipo_base where dataStatus !=3 and statDate >= '2020-10-01'"""
        query_schema = dict(db_key="seeyii_db", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            item_result = self.fetch_dict(item, self.list_fetch)
            result[item_result["compName"]].append(item_result)
        return result

    def get_ipo_info(self):
        result = dict()
        item_dict = self.query_ipo_data()
        for raw_name, item_list in item_dict.items():
            raw_item = self.get_ipo_item(item_list)
            if raw_item:
                result_item = raw_item
            else:
                result_item = max(item_list, key=lambda x: x["statDate"])
            name = result_item["compName"]
            result.setdefault(name, result_item)
        return result

    @staticmethod
    def get_ipo_item(item_list):
        last_stat = set()
        new_item = max(item_list, key=lambda x: x["statDate"])
        max_date = str(new_item["statDate"])
        for item in item_list:
            stat = item["currStat"]
            stat_date = str(item["statDate"])
            if str(max_date) == stat_date:
                last_stat.add(stat)
        if last_stat == {'启动上市辅导', '终止上市辅导'}:
            for raw in item_list:
                r_stat = raw["currStat"]
                if r_stat == "启动上市辅导":
                    return raw

    def law_process(self, names, base_list):
        cpws_dict = self.cpws.run(names)
        fygg_dict = self.fygg.run(names)
        ktgg_dict = self.ktgg.run(names)
        law_dict = {
            "judgeTime": cpws_dict,
            "ctimeDate": fygg_dict,
            "eventTime": ktgg_dict}
        for item in base_list:
            name = item["companyName"]
            is_ss = False
            for field, dic in law_dict.items():
                ss_list = dic.get(name)
                if ss_list:
                    for ss in ss_list:
                        date = ss.get(field)
                        if date:
                            if str(date) > "2020-01-01":
                                item.update({"law": "是"})
                                is_ss = True
                                break

            if not is_ss:
                item.update({"law": "否"})

    def read_excel_data(self, file_name, sheet="Sheet"):
        data_list = self._extract_data(file_name, sheet)
        return data_list

    def gys_data_process(self, gys_dict):
        result = dict()
        for name, items in gys_dict.items():
            gys, kh = set(), set()
            for item in items:
                gys_name = item["company_name"]
                rel = item["relation"]
                if rel == "供应商":
                    gys.add(gys_name)
                elif rel == "销售客户":
                    kh.add(gys_name)

            r_gys, r_kh = list(), list()
            area_dict = self.area.run(list(gys | kh))
            for n, raw_item in area_dict.items():
                province = raw_item.get("provinceName")
                if not province or province != '四川省':
                    continue
                if n in gys:
                    r_gys.append(n)
                if n in kh:
                    r_kh.append(n)
            if r_gys or r_kh:
                result.setdefault(name, {"gys": ",".join(r_gys), "kh": ",".join(r_kh)})
        return result

    def save_data_excel(self, result):
        field_cfg = {
            'companyName': ('企业名称', 0),
            'reg_city': ('市', 1),
            'reg_district': ('区', 2),
            'reg_location': ('注册地址', 3),
            'postal_address': ('办公地址', 4),
            'phone_number': ('联系电话', 5),
            'first_industry': ('一级行业', 6),
            'second_industry': ('二级行业', 7),
            'ind_m': ('国标行业（门）', 8),
            'ind_d': ('国标行业（大）', 9),
            'ind_z': ('国标行业（中）', 10),
            'business_scope': ('经营范围', 11),
            'market': ('上市情况', 12),
            'ss_market': ('(拟)申请市场', 13),
            'preName': ('辅导机构', 14),
            'currStat': ('申报进程', 15),
            'statDate': ('更新日期', 16),
            'endowment_insurance': ('社保缴纳人数', 17),
            'law': ('是否涉诉/涉案', 18),
            'bizInco': ('营业收入（万元）', 19),
            'netProfit': ('净利润（万元）', 20),
            'accoRece': ('应收账款（万元）', 21),
            'accoPaya': ('应付账款（万元）', 22),
            'gys': ('上游客户', 23),
            'kh': ('下游客户', 24)}
        self._excel_name = self.name_add_date("中行四川分行价值企业.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    @staticmethod
    def get_data(gb_dict, sb_dict, jl_dict, base_list, gys_dict, yl_dict, fz_dict):
        for item in base_list:
            name = item["companyName"]
            gb = gb_dict.get(name)
            if gb:
                item.update(gb)
            sb = sb_dict.get(name)
            if sb:
                item.update(sb)
            jl = jl_dict.get(name)
            if jl:
                item.update({"jl": "是"})
            else:
                item.update({"jl": "否"})
            gys = gys_dict.get(name)
            if gys:
                item.update(gys)
            yl = yl_dict.get(name)
            if yl:
                item.update(yl)
            fz = fz_dict.get(name)
            if fz:
                item.update(fz)

    def data_process(self, cyl_base, raw_names_dict):
        result = list()
        for name, base_info in cyl_base.items():
            reg_province = base_info.get("reg_province")
            reg_status = base_info.get("reg_status") or ""
            if name not in {"华融化学股份有限公司"}:
                if reg_province != "四川省" or self.pattern.search(reg_status):
                    continue
            # 一级行业
            first_industry = base_info.get("first_industry")
            if first_industry:
                first = first_industry[0]["industry_name"]
                base_info["first_industry"] = first
            # 二级行业
            second_industry = base_info.get("second_industry")
            if second_industry:
                second = second_industry[0]["industry_name"]
                base_info["second_industry"] = second
            base_info.update(raw_names_dict.get(name, dict()))
            mark_list = set()
            market = base_info.get("market", "")
            curr_stat = base_info.get("currStat", "")
            if "A股公司" in market:
                mark_list.add("A股公司")
            if "四板公司" in market:
                mark_list.add("四板公司")
            if "三板公司" in market:
                mark_list.add("三板公司")
            if "非挂牌上市公司" in market:
                mark_list.add("非挂牌上市公司")
            if "拟IPO上市" in market:
                mark_list.add("拟IPO上市")
            if curr_stat in {"发审会通过", "暂缓表决", "上市委会议通过", "暂缓审议", "补充审核", "发行暂缓",
                             "复审委会议通过", "提交注册", "注册生效", "证监会核准"}:
                mark_list.add("拟IPO上市")
            base_info["market"] = ",".join(list(mark_list))
            result.append(base_info)
        return result

    def query_yl_data(self):
        result = dict()
        sql_statement = """
        SELECT companyName, bizInco /10000 as bizInco, netProfit/10000 as netProfit from (
        SELECT  companyName, bizInco, netProfit, endDate, reportType, publishDate from   sy_cd_ms_fin_sk_inc as a JOIN 
        (SELECT max(endDate) as end_date, innerCode as cmp_code from sy_cd_ms_fin_sk_inc where
         reportType=3 and endDate in ("********", "********") and dataStatus != 3 GROUP BY innerCode ) as b on a.innerCode=b.cmp_code and a.endDate=b.end_date and a.reportType=3  and a.dataStatus != 3 
        JOIN sy_cd_ms_base_cn_stock c on a.innerCode=c.innerCode  and c.dataStatus != 3 ) as x GROUP BY companyName, bizInco, netProfit, endDate, reportType, publishDate
        UNION all
        SELECT  companyName, operatRevenue/10000 as bizInco, netProfit/10000 as netProfit from  sy_cd_ms_fin_nq_inc as a JOIN 
        (SELECT max(endDate) as end_date, companyCode as cmp_code from sy_cd_ms_fin_nq_inc where ifMerged=1 and endDate in ("2020-12-31", "2019-12-31") and dataStatus != 3 GROUP BY companyCode ) as b 
        on a.companyCode=b.cmp_code and a.endDate=b.end_date and a.ifMerged=1  and a.dataStatus != 3 
        JOIN sy_cd_ms_base_cn_stock c on a.companyCode=c.companyCode  and c.dataStatus != 3 
        """
        query_schema = dict(db_key="tidb_152", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result.setdefault(item["companyName"], item)
        return result

    def query_fz_data(self):
        result = dict()
        sql_statement = """
         SELECT companyName, accoPaya/10000 as accoPaya, accoRece/10000 as accoRece from (
        SELECT  companyName, accoPaya, accoRece, endDate, reportType, publishDate from  sy_cd_ms_fin_sk_balsheet as a JOIN 
        (SELECT max(endDate) as end_date, innerCode as cmp_code from sy_cd_ms_fin_sk_balsheet where
         reportType=3 and endDate in ("********", "********") and dataStatus != 3 GROUP BY innerCode ) as b on a.innerCode=b.cmp_code and a.endDate=b.end_date and a.reportType=3  and a.dataStatus != 3 
        JOIN sy_cd_ms_base_cn_stock c on a.innerCode=c.innerCode  and c.dataStatus != 3 ) as x GROUP BY companyName, accoPaya, accoRece, endDate, reportType, publishDate
        UNION
        SELECT  companyName, accountsPayable/10000 as accoPaya, AR/10000 as accoRece from  sy_cd_ms_fin_nq_balsheet as a JOIN 
        (SELECT max(endDate) as end_date, companyCode as cmp_code from sy_cd_ms_fin_nq_balsheet where ifMerged=1 and endDate in ("2020-12-31", "2019-12-31") and dataStatus != 3 GROUP BY companyCode ) as b 
        on a.companyCode=b.cmp_code and a.endDate=b.end_date and a.ifMerged=1  and a.dataStatus != 3 
        JOIN sy_cd_ms_base_cn_stock c on a.companyCode=c.companyCode and c.dataStatus != 3 GROUP BY companyName, AR, accountsPayable
        """
        query_schema = dict(db_key="tidb_152", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result.setdefault(item["companyName"], item)
        return result

    def query_cur_name(self, names):
        result = dict()
        for idx in range(0, len(names), 50):
            name_list = names[idx:idx + 50]
            query_schema = {
                "db_name": "raw_data",
                "collection_name": "company_alias_name",
                "query_condition": {'alias_name': {"$in": name_list}},
                "query_field": {"_id": 0, "alias_name": 1, "cur_name": 1}}
            query_result = self._data_server.call("query_item", query_schema) or list()
            for item in query_result:
                alias_name = item["alias_name"]
                cur_name = item["cur_name"]
                result.setdefault(alias_name, cur_name)
        return result


if __name__ == '__main__':
    p = SCCYL()
    p.process()
