# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2022/1/27
from elasticsearch import Elasticsearch
from elasticsearch.helpers import scan
from collections import defaultdict

es_user, es_pwd = "shiye_es", "shiye1805A"
# online
NODE_CFGS = [
    {"host": "*************",
     "port": 9200},
    {"host": "*************",
     "port": 9200}]


class Cat():
    def __init__(self):
        super(Cat, self).__init__()

    def process(self):
        stand_list = self.query_one_standard_company("地方标准")
        cname_dict = self.transform_company_standard1(stand_list)
        print(len(cname_dict.keys()))

    @staticmethod
    def transform_company_standard(company_info):
        company_dict = defaultdict(int)
        for company in company_info:
            company_name = company.get("_source", dict()).get("draft_org")
            company_dict[company_name] += 1
        return company_dict

    @staticmethod
    def transform_company_standard1(company_info):
        company_dict = defaultdict(int)
        for company in company_info["hits"]["hits"]:
            company_name = company.get("_source", dict()).get("draft_org")
            company_dict[company_name] += 1
        return company_dict

    @staticmethod
    def query_one_standard_company(standard):
        es = Elasticsearch(
            hosts=NODE_CFGS, http_auth=(es_user, es_pwd), timeout=120)
        # search_body = {
        #     "query": {"term": {"standard_type": {"value": standard}}},
        #     "_source": ["draft_org"]}
        search_body = {"query": {"term": {
            "standard_type": {
                "value": "地方标准"
            }
        }},
            "aggs": {
                "all_cname": {
                    "cardinality": {
                        "field": "draft_org.raw"
                    }
                }
            },
            "size": 100000,
        }
        raw_data = es.search(
            index="company_standard_v2",
            doc_type="company_standard",
            body=search_body)
        # raw_data = scan(
        #     es, search_body, scroll="10m", index="company_standard_v2",
        #     doc_type="company_standard", timeout="10m")
        return raw_data


if __name__ == '__main__':
    p = Cat()
    p.process()
