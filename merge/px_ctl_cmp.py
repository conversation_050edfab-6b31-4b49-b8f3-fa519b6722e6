# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022-9-21
from core.excel_base import ExcelBase


class PXCtl(ExcelBase):
    def __init__(self):
        super(PXCtl, self).__init__()

    def process(self):
        result = list()
        for spt_list in self.__gen_spt_info():
            for inv_item in spt_list:
                inv_list = inv_item["investor_list"]
                investor = [data["investor"].replace("(", "（").replace(")", "）") for data in inv_list]
                spectrum_name = inv_item["spectrum_name"]
                ctl_list = self.query_ctl_cmp(investor)
                for c in ctl_list:
                    c["spectrum_name"] = spectrum_name
                    result.append(c)
        self.save_data_excel({"sheet": result})

    def save_data_excel(self, result):
        field_cfg = {
            'spectrum_name': ('谱系名称', 0),
            'ctlerName': ('起始名称', 1),
            'compName': ('被控制公司名称', 2),
        }
        self._excel_name = self.name_add_date("谱系测试数据.xlsx")
        self.save_to_excel(field_cfg, result)

    def query_ctl_cmp(self, cname_list):
        sql = """
                select ctlerName, compName from sy_cd_ms_rela_cta_info
                where ctlRatio>=0.25 and dataStatus!=3 and ctlerName in ({})"""

        query_schema = dict(db_key="polardb_seeyii_assets_database",
                            sql_statement=sql.format(','.join([f'{item!r}' for item in cname_list])))
        result_list1 = self._data_server.call("query_sql_item", query_schema) or list()
        r1 = [item["compName"] for item in result_list1]
        if r1:
            query_schema1 = dict(db_key="polardb_seeyii_assets_database",
                                 sql_statement=sql.format(','.join([f'{item!r}' for item in r1])))
            result_list = self._data_server.call("query_sql_item", query_schema1) or list()
            return result_list1 + result_list
        return result_list1

    def __gen_spt_info(self):
        query_condition = {"spectrum_name": {"$in": ["宁德时代", "BAI资本", "华侨城(亚洲)控股", "DCM"]}}
        offset = None
        while True:
            if offset:
                query_condition["_id"] = {"$gt": offset}
            query_item = {
                "db_name": "raw_data",
                "collection_name": "investor_spectrum_info_v2",
                "query_condition": query_condition,
                "query_field": {
                    '_id': 1, "feature_id": 1, "investor_list": 1, "spectrum_name": 1},
                "sort_field": [("_id", 1)],
                "limit_n": 100}
            query_result = self._data_server.call("query_item", query_item)
            if not query_result:
                break
            yield query_result
            offset = query_result[-1]["_id"]


if __name__ == '__main__':
    p = PXCtl()
    p.process()
