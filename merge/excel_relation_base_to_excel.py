# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/25
from core.excel_base import ExcelBase
from moduler.base_info_api_thread import BaseInfoMaster
from moduler.company_industry_and_cyl import CompanyIndustryAndCYL


class AddBaseInfo(ExcelBase):
    def __init__(self):
        super(AddBaseInfo, self).__init__()
        self.base_info = BaseInfoMaster()
        self.industry_info = CompanyIndustryAndCYL()

    def process(self, *args, **kwargs):
        result = list()
        relation_data = self.read_excel_data(self._in_file_path + "/股权关联关系_20210121.xlsx")
        rel_names = {item["关联企业名称"] for item in relation_data}
        base_info_dict = self.base_info.run(list(rel_names))
        ind_info_dict = self.industry_info.run(list(rel_names))
        for item in relation_data:
            name = item["关联企业名称"]
            item.update(base_info_dict.get(name, dict()))
            ind_set = set()
            for ind in ind_info_dict.get(name, list()):
                ind_set.add(ind["IndustryName"])
            item["IndustryName"] = ",".join(list(ind_set))
            result.append(item)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            '关联企业名称': ('关联企业名称', 0),
            'credit_code': ('统一信用代码', 1),
            'legal_person_name': ('法定代表人', 2),
            'reg_capital': ('注册资本', 3),
            'establish_date': ('成立时间', 4),
            'reg_status': ('注册状态', 5),
            'company_org_type': ('公司组织类型', 6),
            'reg_province': ('省', 7),
            'reg_city': ('市', 8),
            'reg_district': ('区', 9),
            'reg_location': ('注册地址', 10),
            'postal_address': ('办公地址', 11),
            'phone_number': ('联系电话', 12),
            'business_scope': ('经营范围', 13),
            'company_nature': ('性质标签', 14),
            'company_business': ('业务亮点', 15),
            'IndustryName': ('视野行业', 16),
            '关系类型': ('关系类型', 17),
            '链路': ('链路', 18),
            '集团名称': ('集团名称', 19)}
        self._excel_name = self.name_add_date("农业银行集团客户POC数据.xlsx")
        self.save_to_excel(field_cfg, {"Sheet": result})

    def read_excel_data(self, file_name, sheet="Sheet"):
        data_list = self._extract_data(file_name, sheet)
        return data_list


if __name__ == '__main__':
    p = AddBaseInfo()
    p.process()
