# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/05/26
from core.excel_base import ExcelBase
import operator
from functools import reduce

from moduler.company_alias_name import CompanyAlias
from moduler.company_base_info import CompanyGS
from moduler.company_build_project import CompanyMajorProject


class BuildProject(ExcelBase):
    def __init__(self):
        super(BuildProject, self).__init__()
        self.build = CompanyMajorProject()
        self.alias = CompanyAlias()

    def process(self, *args, **kwargs):
        file_name = self._in_file_path + "/农行V3POC_20210604.xlsx"
        names_result = self._extract_data(file_name, "sheet1")
        raw_names = list({i["集团名称"].replace("(", "（").replace(")", "）").strip() for i in names_result} |
                         {i["成员企业名称"].replace("(", "（").replace(")", "）").strip() for i in names_result})
        alias_dict = self.alias.run(raw_names)
        names = list({alias_dict.get(i, i) for i in raw_names})
        result = self.build.run(names)
        self.save_data_excel(reduce(operator.add, result.values()))

    def save_data_excel(self, result):
        field_cfg = {
            'ownerCompany': ('业主单位名称', 0),
            'projectName': ('项目名称', 1),
            'provinceArea': ('地区', 2),
            'publishDate': ('发布日期', 3),
            'industry': ('行业', 4),
            'period': ('建设周期', 5),
            'progress': ('进展阶段', 6),
            'amount': ('总投资金额（万元）', 7),
            'location': ('所在地', 8),
            'scale': ('建设内容及规模', 9),
            'content': ('项目简介', 10),
        }
        self._excel_name = self.name_add_date("农行POCV3.xlsx")
        self.save_to_excel(field_cfg, {"Sheet": result})


if __name__ == '__main__':
    p = BuildProject()
    p.process()
