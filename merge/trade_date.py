# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2024/4/19
from db.engine.data_services import DataServer
from datetime import datetime, timedelta

# data_server = DataServer.get_instance()


# def _insert_data(item):
#     update_schema = {
#         "db_name": "xsb_data",
#         "collection_name": "trade_date",
#         "insert_data": item}
#     data_server.call("insert_item", update_schema)


# 假期日期列表，包括休市日期
holiday_dates = [
    # 元旦
    datetime(2023, 12, 30),
    datetime(2023, 12, 31),
    datetime(2024, 1, 1),
    # 春节
    datetime(2024, 2, 9),
    datetime(2024, 2, 10),
    datetime(2024, 2, 11),
    datetime(2024, 2, 12),
    datetime(2024, 2, 13),
    datetime(2024, 2, 14),
    datetime(2024, 2, 15),
    datetime(2024, 2, 16),
    datetime(2024, 2, 17),
    # 清明节
    datetime(2024, 4, 4),
    datetime(2024, 4, 5),
    datetime(2024, 4, 6),
    # 劳动节
    datetime(2024, 5, 1),
    datetime(2024, 5, 2),
    datetime(2024, 5, 3),
    datetime(2024, 5, 4),
    datetime(2024, 5, 5),
    # 端午节
    datetime(2024, 6, 10),
    # 中秋节
    datetime(2024, 9, 15),
    datetime(2024, 9, 16),
    datetime(2024, 9, 17),
    # 国庆节
    datetime(2024, 10, 1),
    datetime(2024, 10, 2),
    datetime(2024, 10, 3),
    datetime(2024, 10, 4),
    datetime(2024, 10, 5),
    datetime(2024, 10, 6),
    datetime(2024, 10, 7)
]

# 获取2024年的开始日期和结束日期
start_date = datetime(2024, 1, 1)
end_date = datetime(2024, 12, 31)

# 循环遍历日期并插入数据库
current_date = start_date
while current_date <= end_date:
    # 判断是否为股票交易日，这里使用简单的规则，周一到周五为交易日，周六和周日为非交易日，以及假期日期为非交易日
    is_trade_date = 1 if current_date.weekday() < 5 and current_date not in holiday_dates else 0

    # 构造要插入的文档
    document = {
        'date': current_date,
        'is_trade_date': is_trade_date
    }
    print(document)
    # _insert_data(document)

    # 增加一天
    current_date += timedelta(days=1)
