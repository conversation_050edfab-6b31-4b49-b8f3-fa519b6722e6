# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2024/6/7
from core.excel_base import ExcelBase
import pandas as pd
import numpy as np


class ZL(ExcelBase):
    def __int__(self):
        super(ZL, self).__int__()

    def process(self, *args, **kwargs):
        industry_list = ["农业", "林业", "畜牧业", "渔业", "农、林、牧、渔专业及辅助性活动", "煤炭开采和洗选业",
                         "石油和天然气开采业", "黑色金属矿采选业", "有色金属矿采选业", "非金属矿采选业",
                         "开采专业及辅助性活动", "其他采矿业", "农副食品加工业", "食品制造业", "酒、饮料和精制茶制造业",
                         "烟草制品业", "纺织业", "纺织服装、服饰业", "皮革、毛皮、羽毛及其制品和制鞋业",
                         "木材加工和木、竹、藤、棕、草制品业", "家具制造业", "造纸和纸制品业", "印刷和记录媒介复制业",
                         "文教、工美、体育和娱乐用品制造业", "石油、煤炭及其他燃料加工业", "化学原料和化学制品制造业",
                         "医药制造业", "化学纤维制造业", "橡胶和塑料制品业", "非金属矿物制品业",
                         "黑色金属冶炼和压延加工业", "有色金属冶炼和压延加工业", "金属制品业", "通用设备制造业",
                         "专用设备制造业", "汽车制造业", "铁路、船舶、航空航天和其他运输设备制造业",
                         "电气机械和器材制造业", "计算机、通信和其他电子设备制造业", "仪器仪表制造业", "其他制造业",
                         "废弃资源综合利用业", "金属制品、机械和设备修理业", "电力、热力生产和供应业", "燃气生产和供应业",
                         "水的生产和供应业", "房屋建筑业", "土木工程建筑业", "建筑安装业", "建筑装饰、装修和其他建筑业",
                         "电信、广播电视和卫星传输服务", "互联网和相关服务", "软件和信息技术服务业",
                         "机动车、电子产品和日用产品修理业"]

        # 定义专利类型映射
        patent_types = {
            1: "发明",
            2: "实用新型",
            3: "外观设计"
        }

        # 读取Excel文件
        sheets = pd.read_excel(self._in_file_path + "/专利统计_20240607.xlsx", sheet_name=None)
        # relation_data = self._extract_data(self._in_file_path + "/专利统计_20240607.xlsx", "Sheet")

        # 获取所有年份
        all_years = set()
        for sheet_name, df in sheets.items():
            if 'year' in df.columns:
                all_years.update(df['year'].dropna().astype(int).unique())
        print(sorted(list(all_years)))

        # 处理每个Sheet
        for sheet_name, df in sheets.items():
            print(sheet_name, df.shape)
            if sheet_name == '各行业专利转让的数量':
                # 确保每个行业都有记录
                for industry in industry_list:
                    if industry not in df['industry'].values:
                        new_row = pd.DataFrame({'industry': [industry], 'patent_count': [0]})
                        df = pd.concat([df, new_row], ignore_index=True)
                        # df = df.append({'industry': industry, 'patent_count': 0}, ignore_index=True)

            elif sheet_name == '各行业不同类型专利转让的数量':
                # 确保每个行业和专利类型组合都有记录
                for industry in industry_list:
                    for pattype, patname in patent_types.items():
                        if not ((df['industry'] == industry) & (df['pattype'] == pattype)).any():
                            new_row = pd.DataFrame({'industry': [industry], 'pattype': [pattype], '专利类型': [patname],
                                                    'patent_count': [0]})
                            df = pd.concat([df, new_row], ignore_index=True)
                            # df = df.append(
                            #     {'industry': industry, 'pattype': pattype, '专利类型': patname, 'patent_count': 0},
                            #     ignore_index=True)

            elif sheet_name == '各行业不同年份专利转让的数量':
                # 确保每个行业都有记录
                for industry in industry_list:
                    for year in all_years:
                        if not ((df['industry'] == industry) & (df['year'] == year)).any():
                            new_row = pd.DataFrame({'industry': [industry], 'year': [year], 'patent_count': [0]})
                            df = pd.concat([df, new_row], ignore_index=True)
                            # df = df.append({'industry': industry, 'year': year, 'patent_count': 0}, ignore_index=True)

            elif sheet_name == '各行业不同类型年份专利转让的数量':
                # 确保每个行业都有记录
                for industry in industry_list:
                    for pattype, patname in patent_types.items():
                        for year in all_years:
                            if not ((df['industry'] == industry) & (df['year'] == year) & (
                                    df['pattype'] == pattype)).any():
                                new_row = pd.DataFrame(
                                    {'industry': [industry], 'pattype': [pattype], "专利类型": [patname],
                                     'year': [year],
                                     'patent_count': [0]})
                                df = pd.concat([df, new_row], ignore_index=True)
                                # df = df.append(
                                #     {'industry': industry, 'pattype': pattype, '专利类型': patname, 'year': year,
                                #      'patent_count': 0},
                                #     ignore_index=True)

            # 更新Sheet中的数据
            print(df.shape)
            sheets[sheet_name] = df
        # 将读取到的内容保存到新的Excel文件中
        output_path = '专利统计.xlsx'
        with pd.ExcelWriter(output_path) as writer:
            for sheet_name, df in sheets.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)


if __name__ == '__main__':
    ZL().process()
