# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2023/4/21
import re


class DDL():
    def __init__(self):
        pass

    def process(self):
        pass

    def gen_source(self):
        pass

    def gen_title(self):
        pass

    def gen_end(self):
        pass

    def gen_desc(self):
        a = [
    {
        "修改后编码": "SJ000106100"
    },
    {
        "修改后编码": "SJ000106101"
    },
    {
        "修改后编码": "SJ000106102"
    },
    {
        "修改后编码": "SJ000106103"
    },
    {
        "修改后编码": "SJ000106104"
    },
    {
        "修改后编码": "SJ000106105"
    },
    {
        "修改后编码": "SJ000106106"
    },
    {
        "修改后编码": "SJ000106107"
    },
    {
        "修改后编码": "SJ000106108"
    },
    {
        "修改后编码": "SJ000106109"
    },
    {
        "修改后编码": "SJ000106110"
    },
    {
        "修改后编码": "SJ000106111"
    },
    {
        "修改后编码": "SJ000106112"
    },
    {
        "修改后编码": "SJ000106113"
    },
    {
        "修改后编码": "SJ000106114"
    },
    {
        "修改后编码": "SJ000106115"
    },
    {
        "修改后编码": "SJ000106116"
    },
    {
        "修改后编码": "SJ000106117"
    },
    {
        "修改后编码": "SJ000106118"
    }
]
        res = ''
        for i in a:
            code = i["修改后编码"]
            # eventName = i["实施规则"].replace("筛选emoLabel='正向' 且 ",'')
            # print(code, eventName)
            # sql = f"select subjectcode,eventsubject, '{code}' as eventtype,concat_ws('',a,b,c) as desc1,url,eventdate,expiredate from base_tb where {eventName}\nunion \n"
            sql =f"""INSERT INTO cmnoppo.dwd_mt_buss_event_op PARTITION(filedate="20230524") VALUES ("{code}","16",null,null,null,null,null,"1","2023-05-24 17:00:00"); \n"""
            res +=sql

        print(res)




if __name__ == '__main__':
    p = DDL()
    p.gen_desc()
