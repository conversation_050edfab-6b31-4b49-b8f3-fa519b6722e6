# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/25
from collections import defaultdict

from core.excel_base import ExcelBase
from moduler.base_info_api_thread import BaseInfoMaster
from moduler.company_area_info import CompanyArea


class AddBaseInfo(ExcelBase):
    def __init__(self):
        super(AddBaseInfo, self).__init__()
        self.base_info = BaseInfoMaster()
        self.area = CompanyArea()

    def process(self, *args, **kwargs):
        result = list()
        name_set = self.query_cmp_names()
        print(len(name_set))
        names = self.query_cmp_area(list(name_set))
        print(len(names))
        base_info_dict = self.base_info.run(list(names))
        result.extend(base_info_dict.values())
        self.save_data_excel(result)

    def query_cmp_area(self, name_list):
        result = set()
        for idx in range(0, len(name_list), 50):
            names = name_list[idx: idx + 50]
            name_str = "','".join(names)
            sql_statement = """SELECT compName from sy_cd_ms_base_comp_geo 
            where compName in ('{}') and dataStatus !=3 and districtCode ='120102';"""
            query_schema = dict(db_key="tidb_135", sql_statement=sql_statement.format(name_str))
            result_list = self._data_server.call("query_sql_item", query_schema) or list()
            for item in result_list:
                result.add(item["compName"])
        return result

    def query_cmp_names(self):
        result = set()
        sql = """
        SELECT id, compName, dataStatus, isValid from dwd_ms_cn_comp_hq_list where
        id > {} ORDER BY id ASC limit 1000;
        """
        for items in self._query_sql_iter_by_id(sql, "db_seeyii"):
            for item in items:
                compName = item["compName"]
                dataStatus = item["dataStatus"]
                isValid = item["isValid"]
                if dataStatus == 3 or isValid == 0:
                    continue
                result.add(compName)
        return result

    def save_data_excel(self, result):
        field_cfg = {
            'companyName': ('公司名称', 0),
            'credit_code': ('统一信用代码', 1),
            'legal_person_name': ('法定代表人', 2),
            'reg_capital': ('注册资本', 3),
            'establish_date': ('成立时间', 4),
            'reg_status': ('注册状态', 5),
            'company_org_type': ('公司组织类型', 6),
            'reg_province': ('省', 7),
            'reg_city': ('市', 8),
            'reg_district': ('区', 9),
            'reg_location': ('注册地址', 10),
            'postal_address': ('办公地址', 11),
            'phone_number': ('联系电话', 12),
            'business_scope': ('经营范围', 13),
            'company_business': ('业务亮点', 14)}
        self._excel_name = self.name_add_date("天津市河东区重点企业.xlsx")
        self.save_to_excel(field_cfg, {"Sheet": result})

    def read_excel_data(self, file_name, sheet="Sheet"):
        data_list = self._extract_data(file_name, sheet)
        return data_list


if __name__ == '__main__':
    p = AddBaseInfo()
    p.process()
