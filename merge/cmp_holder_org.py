# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/6/3
import operator
from functools import reduce
from copy import deepcopy
from core.excel_base import ExcelBase
from moduler.company_a_and_xsb_holder import CompanyAandXsbShareholder
from moduler.company_alias_name import CompanyAlias
from moduler.company_category import CompanyCategory
from moduler.company_holder import CompanyHolder

product = {
    "PrivateFundCompany": u"私募基金管理公司",
    "PublicFundCompany": u"公募基金管理公司",
    "SecurityCompany": u"证券公司",
    "FuturesCompany": u"期货公司",
    "InvestCompanyOfBroker": u"券商私募投资子公司",
    "Inv100003": u"券商另类投资子公司",
    "PublicChildCompany": u"基金子公司",
    "DevelopmentBank": u"开发性金融机构",
    "SYAU10000": u"集团公司投资平台",
    "PolicyBank": u"政策性银行",
    "AssetManagementCompany": u"金融资产管理公司",
    "FinancialLeaseCompany": u"金融租赁公司",
    "LargeCommercialBank": u"大型商业银行",
    "JointStockCommercialBank": u"股份制商业银行",
    "PrivateBank": u"民营银行",
    "RuralCommercialBank": u"农村商业银行",
    "ForeignBank": u"外资银行",
    "CityCommercialBank": u"城市商业银行",
    "NationalInvestmentPlatform": u"国家投资平台",
    "InvestmentConsultCompany": u"证券投资咨询公司",
    "TrustCompany": u"信托公司",
    "FinanceCompany": u"集团财务公司",
    "InsuranceCompany": u"保险公司",
    "InsuranceAssetCompany": u"保险资产管理公司",
    "GovGXInvestCompany": u"政府高新投资平台",
    "GovCYInvestCompany": u"政府产业引导基金",
    "FinanceHoldingsCompany": u"金融控股集团",
    "FundOfFund": u"母基金",
    "UnrecordedPrivateFundCompany": u"私募基金管理公司（未备案）",
    "is_lost_contact_mechanism": u"失联机构",
    "identification": u"异常机构",
    "sham_statement": u"虚假填报",
    "mater_rial_omission": u"重大遗漏",
    "eight_line": u"违反八条底线",
    "bad_credit": u"相关主体存在不良诚信记录",
    "manage_scala_zero": u"管理规模为零",
    "one_year_manage_scala_zero": u"登记一年以上管理规模为零",
    "hand_in_under_register_25_p": u"管理人实缴资本低于注册资本25%",
    "hand_in_le_100_w": u"管理人实缴资本低于100万",
    "not_normal_liquidation": u"非正常清盘",
    "independent_securities_scale_1": u"私募证券基金(自主发行)规模0-1亿",
    "independent_securities_scale_2": u"私募证券基金(自主发行)规模1-10亿",
    "independent_securities_scale_3": u"私募证券基金(自主发行)规模10-20亿",
    "independent_securities_scale_4": u"私募证券基金(自主发行)规模20-50亿",
    "independent_securities_scale_5": u"私募证券基金(自主发行)规模50亿以上",
    "consultant_securities_scale_1": u"私募证券基金(顾问管理)规模0-1亿",
    "consultant_securities_scale_2": u"私募证券基金(顾问管理)规模1-10亿",
    "consultant_securities_scale_3": u"私募证券基金(顾问管理)规模10-20亿",
    "consultant_securities_scale_4": u"私募证券基金(顾问管理)规模20-50亿",
    "consultant_securities_scale_5": u"私募证券基金(顾问管理)规模50亿以上",
    "private_equity_scale_1": u"私募股权基金规模0-20亿",
    "private_equity_scale_2": u"私募股权基金规模20-50亿",
    "private_equity_scale_3": u"私募股权基金规模50-100亿",
    "private_equity_scale_4": u"私募股权基金规模100亿以上",
    "venture_capital_scale_1": u"创业投资基金规模0-2亿",
    "venture_capital_scale_2": u"创业投资基金规模2-5亿",
    "venture_capital_scale_3": u"创业投资基金规模5-10亿",
    "venture_capital_scale_4": u"创业投资基金规模10亿以上",
    "other_fund_scale_1": u"其他私募基金规模0-2亿",
    "other_fund_scale_2": u"其他私募基金规模2-5亿",
    "other_fund_scale_3": u"其他私募基金规模5-10亿",
    "other_fund_scale_4": u"其他私募基金规模10亿以上",
    "PrivateFund": u"私募基金",
    "UnrecordedFund": u"未备案基金",
    "SpecialAccountFund": u"公募基金专户",
    "BrokerFund": u"券商直投基金",
    "SecurityAssetManagementPlan": u"券商资管计划",
    "FuturesAssetManagementPlan": u"期货资管计划",
    "PublicFund": u"公募基金",
    "UnrecordedOtherAsset": u"三类资产（未备案）",
    "RecordedFund": u"已备案基金",
    "EquityInvestFund": u"股权投资基金",
    "OtherPrivateFund": u"其他私募投资基金",
    "PrivateSecurityFund": u"私募证券投资基金",
    "VentureCapitalFund": u"创业投资基金",
    "TrustPlan": u"信托计划",
    "InsuranceAssetManagementPlan": u"保险资管计划",
    "BankFinancialProduct": u"银行理财产品",
    "OtherFund": u"其他基金类产品",
    "Operating": u"正在运作",
    "DelayClear": u"延期清算",
    "EarlyClear": u"提前清算",
    "NormalClear": u"正常清算",
    "AbnormalClear": u"非正常清算",
    "DelayClose": u"延期清盘",
    "EarlyClose": u"提前清盘",
    "NormalClose": u"正常清盘",
    "ThreeAsset": u"三类资产",
}


class CmpHolder(ExcelBase):
    def __init__(self):
        super(CmpHolder, self).__init__()
        self.a_xsb = CompanyAandXsbShareholder()
        self.gs_holder = CompanyHolder()
        self.cat = CompanyCategory()
        self.alias = CompanyAlias()

    def process(self, names):
        result = list()
        names = list({i.replace("(", "（").replace(")", "）").strip() for i in names})
        alias_dict = self.alias.run(names)
        names = [alias_dict.get(i, i) for i in names]
        cat_dict = self.cat.run(names)
        for name in names:
            cat = cat_dict.get(name, dict())
            category = cat.get("category", list())
            if "ACompany" in category or "ThirdCompany" in category:
                holder_list = self.a_xsb.run([name])
            else:
                holder_list = list(self.gs_holder.run([name]).values())
                if not holder_list:
                    print(name)
                    continue
                holder_list = reduce(operator.add, holder_list)

            holder_name_dict = {i["shareholder_name"]: i for i in holder_list if len(i["shareholder_name"]) >= 4}
            holder_cat_dict = self.cat.run(list(holder_name_dict.keys()))
            for h_name, h_item in holder_name_dict.items():
                h_cat = holder_cat_dict.get(h_name, dict())
                h_category = set(h_cat.get("category", list()))
                if h_category & set(product.keys()):
                    is_add = False
                    manager_item = self.query_product(h_name) or dict()
                    manager_list = manager_item.get("manager_name", list())
                    for man_item in manager_list:
                        if "manager_type" in man_item:
                            man_name = man_item.get("manager_name")
                            if man_name:
                                is_add = True
                                new_item = deepcopy(h_item)
                                new_item["manager_name"] = man_name
                                result.append(new_item)
                    if not is_add:
                        result.append(h_item)
        self.save_data_excel(result)

    def query_product(self, name):
        query_schema = {
            "db_name": "base_data",
            "collection_name": "investor_product_base_info_v2",
            "query_condition": {'cname': name},
            "query_field": {"_id": 0, "manager_name": 1}}
        query_result = self._data_server.call("query_item", query_schema) or list()
        for item in query_result:
            return item

    def save_data_excel(self, result):
        field_cfg = {
            'cname': ('公司名称', 0),
            'shareholder_name': ('机构名称', 1),
            'manager_name': ('管理人名称', 2),
        }
        self._excel_name = self.name_add_date("机构数据.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})


if __name__ == '__main__':
    names = ["中国旅游集团中免股份有限公司", "中国神华能源股份有限公司", "三一重工股份有限公司", "京沪高速铁路股份有限公司", "中国联合网络通信股份有限公司", "北京东方雨虹防水技术股份有限公司",
             "北新集团建材股份有限公司", "紫光股份有限公司", "中国铁路通信信号股份有限公司", "北京顺鑫农业股份有限公司", "中金黄金股份有限公司", "中航航空电子系统股份有限公司",
             "北京同仁堂股份有限公司", "国药集团药业股份有限公司", "北京千方科技股份有限公司", "蓝星安迪苏股份有限公司", "北京光环新网科技股份有限公司", "东华软件股份公司", "航天信息股份有限公司",
             "中海油能源发展股份有限公司", "北京燕京啤酒股份有限公司", "中国医药健康产业股份有限公司", "北京蓝色光标数据科技股份有限公司", "天地科技股份有限公司", "华润双鹤药业股份有限公司",
             "中工国际工程股份有限公司", "北方国际合作股份有限公司", "华电重工股份有限公司", "嘉事堂药业股份有限公司", "北京华联综合超市股份有限公司", "奥瑞金科技股份有限公司",
             "北京城市排水集团有限责任公司", "北京大北农科技集团股份有限公司", "北京电子控股有限责任公司", "北京二商集团有限责任公司", "北京供销社投资管理中心", "北京国有资本经营管理中心",
             "北京金隅集团股份有限公司", "北京京煤集团有限责任公司", "北京京能电力股份有限公司", "北京京能清洁能源电力股份有限公司", "北京控股集团有限公司", "北京能源集团有限责任公司",
             "北京汽车股份有限公司", "北京时尚控股有限责任公司", "北京市国有资产经营有限责任公司", "北京市基础设施投资有限公司", "北京市热力集团有限责任公司", "北京首创股份有限公司",
             "北京首农食品集团有限公司", "北控水务集团有限公司", "大唐国际发电股份有限公司", "国家电网有限公司", "国家开发投资集团有限公司", "国家能源集团新能源有限责任公司",
             "国家能源投资集团有限责任公司", "国投电力控股股份有限公司", "国网新源控股有限公司", "国网信息通信产业集团有限公司", "国网综合能源服务集团有限公司", "华能国际电力股份有限公司",
             "华润医药控股有限公司", "际华集团股份有限公司", "江河创建集团股份有限公司", "京东方科技集团股份有限公司", "凯盛科技集团有限公司", "乐普(北京)医疗器械股份有限公司",
             "龙源电力集团股份有限公司", "王府井集团股份有限公司", "五矿资本控股有限公司", "物美科技集团有限公司", "新奥(中国)燃气投资有限公司", "新兴际华集团有限公司", "招商局集团有限公司",
             "中广核风电有限公司", "中国北方工业有限公司", "中国兵器工业集团有限公司", "中国兵器装备集团有限公司", "中国诚通控股集团有限公司", "中国船舶重工集团有限公司",
             "中国大唐集团新能源股份有限公司", "中国电信股份有限公司", "中国电子科技集团有限公司", "中国航空工业集团有限公司", "中国航天科工集团有限公司", "中国航天科技集团有限公司",
             "中国华电集团有限公司", "中国华润有限公司", "中国化工油气股份有限公司", "中国化学工程股份有限公司", "中国化学工程集团有限公司", "中国黄金集团有限公司", "中国机械工业集团有限公司",
             "中国建材股份有限公司", "中国节能环保集团有限公司", "中国科学院控股有限公司", "中国联合网络通信有限公司", "中国铝业股份有限公司", "中国铝业集团有限公司", "中国轻工集团有限公司",
             "中国生物技术股份有限公司", "中国石油化工股份有限公司", "中国石油化工集团有限公司", "中国石油天然气股份有限公司", "中国石油天然气集团有限公司", "中国通用技术(集团)控股有限责任公司",
             "中国外运股份有限公司", "中国有色矿业集团有限公司", "中国长江电力股份有限公司", "中国长江三峡集团有限公司", "中国中材集团有限公司", "中国中车股份有限公司", "中国中车集团有限公司",
             "中国中煤能源股份有限公司", "中国中煤能源集团有限公司", "中航机载系统有限公司", "中交投资有限公司", "中石油昆仑燃气有限公司", "中铁高新工业股份有限公司",
             "北京市顺义区国有资本经营管理中心", "华能资本服务有限公司", "宜宾五粮液股份有限公司", "东方电气股份有限公司", "攀钢集团钒钛资源股份有限公司", "易见供应链管理股份有限公司",
             "新华文轩出版传媒股份有限公司", "成都红旗连锁股份有限公司", "创维数字股份有限公司", "成都云图控股股份有限公司", "成都高新投资集团有限公司", "成都轨道交通集团有限公司",
             "成都环境投资集团有限公司", "泸州老窖股份有限公司", "泸州老窖集团有限责任公司", "攀钢集团攀枝花钢钒有限公司", "攀钢集团有限公司", "四川科伦药业股份有限公司",
             "四川省铁路产业投资集团有限责任公司", "四川省投资集团有限责任公司", "通威股份有限公司", "新希望集团有限公司", "新希望六和股份有限公司", "雅砻江流域水电开发有限公司",
             "宜宾市国有资产经营有限公司", "宜宾天原集团股份有限公司", "中国东方电气集团有限公司", "圆通速递股份有限公司", "大连华锐重工集团股份有限公司", "大商股份有限公司", "大连港集团有限公司",
             "大连万达商业管理集团股份有限公司", "广汇汽车服务集团股份公司", "国电电力发展股份有限公司", "中铁铁龙集装箱物流股份有限公司", "漳州片仔癀药业股份有限公司", "永辉超市股份有限公司",
             "福建圣农发展股份有限公司", "福建三钢闽光股份有限公司", "福建星网锐捷通讯股份有限公司", "福建福能股份有限公司", "福建傲农生物科技集团股份有限公司", "福建省国有资产管理有限公司",
             "福建省交通运输集团有限责任公司", "福建省冶金(控股)有限责任公司", "福建漳龙集团有限公司", "福耀玻璃工业集团股份有限公司", "福州城市建设投资集团有限公司", "国家能源集团福建能源有限责任公司",
             "合力泰科技股份有限公司", "华电福新能源有限公司", "龙岩交通发展集团有限公司", "闽西兴杭国有资产投资经营有限公司", "宁德时代新能源科技股份有限公司", "泉州市国有资产投资经营有限责任公司",
             "泉州市金融控股集团有限公司", "漳州市九龙江集团有限公司", "紫金矿业集团股份有限公司", "天水华天科技股份有限公司", "甘肃祁连山水泥集团股份有限公司", "甘肃省公路交通建设集团有限公司",
             "甘肃省国有资产投资集团有限公司", "金川集团股份有限公司", "美的集团股份有限公司", "佛山市海天调味食品股份有限公司", "珠海格力电器股份有限公司", "惠州亿纬锂能股份有限公司",
             "分众传媒信息技术股份有限公司", "广东海大集团股份有限公司", "欧派家居集团股份有限公司", "广州视源电子科技股份有限公司", "广东生益科技股份有限公司", "广州金域医学检验集团股份有限公司",
             "大参林医药集团股份有限公司", "金发科技股份有限公司", "广州白云山医药集团股份有限公司", "广东新宝电器股份有限公司", "丽珠医药集团股份有限公司", "中顺洁柔纸业股份有限公司",
             "索菲亚家居股份有限公司", "海信家电集团股份有限公司", "广东韶钢松山股份有限公司", "众业达电气股份有限公司", "TCL科技集团股份有限公司", "佛燃能源集团股份有限公司",
             "佛山市公用事业控股有限公司", "佛山市建设开发投资有限公司", "广东宝丽华新能源股份有限公司", "广东电力发展股份有限公司", "广东东阳光科技控股股份有限公司", "广东恒健投资控股有限公司",
             "广东南海控股投资有限公司", "广东省广晟资产经营有限公司", "广东省广物控股集团有限公司", "广东省广新控股集团有限公司", "广东省广业集团有限公司", "广东省联泰集团有限公司",
             "广东省能源集团有限公司", "广东省粤垦投资有限公司", "广东粤海控股集团有限公司", "广汽商贸有限公司", "广州地铁集团有限公司", "广州发展集团股份有限公司", "广州港股份有限公司",
             "广州港集团有限公司", "广州国资发展控股有限公司", "广州汽车集团股份有限公司", "广州商贸投资控股集团有限公司", "广州市城市建设投资集团有限公司", "广州市公共交通集团有限公司",
             "广州市水务投资集团有限公司", "广州无线电集团有限公司", "广州医药集团有限公司", "广州智能装备产业集团有限公司", "瀚蓝环境股份有限公司", "华润电力投资有限公司", "木林森股份有限公司",
             "搜于特集团股份有限公司", "温氏食品集团股份有限公司", "中国南方电网有限责任公司", "珠海华发综合发展有限公司", "广东领益智造股份有限公司", "贵州茅台酒股份有限公司", "中航重机股份有限公司",
             "中国贵州茅台酒厂(集团)有限责任公司", "中钨高新材料股份有限公司", "海南京粮控股股份有限公司", "中国旅游集团有限公司", "杭州海康威视数字技术股份有限公司", "浙江华友钴业股份有限公司",
             "浙江三花智能控制股份有限公司", "浙江新和成股份有限公司", "浙江正泰电器股份有限公司", "浙江大华技术股份有限公司", "浙江苏泊尔股份有限公司", "杭州福斯特应用材料股份有限公司",
             "华峰化学股份有限公司", "天能电池集团股份有限公司", "桐昆集团股份有限公司", "完美世界股份有限公司", "浙江世纪华通集团股份有限公司", "浙江浙能电力股份有限公司", "顾家家居股份有限公司",
             "华东医药股份有限公司", "杭州老板电器股份有限公司", "杭州巨星科技股份有限公司", "天山铝业集团股份有限公司", "横店集团东磁股份有限公司", "杭州制氧机集团股份有限公司",
             "盈峰环境科技集团股份有限公司", "新凤鸣集团股份有限公司", "普洛药业股份有限公司", "浙江森马服饰股份有限公司", "迪安诊断技术集团股份有限公司", "浙富控股集团股份有限公司",
             "利欧集团股份有限公司", "卧龙电气驱动集团股份有限公司", "杭州钢铁股份有限公司", "万向钱潮股份有限公司", "杭叉集团股份有限公司", "振德医疗用品股份有限公司", "浙江海亮股份有限公司",
             "浙江医药股份有限公司", "浙江万丰奥威汽轮股份有限公司", "浙江亚厦装饰股份有限公司", "浙江南都电源动力股份有限公司", "浙江新安化工集团股份有限公司", "杭萧钢构股份有限公司",
             "浙江甬金金属科技股份有限公司", "浙江万马股份有限公司", "万邦德医药控股集团股份有限公司", "浙江东南网架股份有限公司", "浙江星星科技股份有限公司", "赞宇科技集团股份有限公司",
             "传化集团有限公司", "传化智联股份有限公司", "德力西集团有限公司", "富通集团有限公司", "顾家集团有限公司", "海亮集团有限公司", "海宁市资产经营公司", "杭州市城市建设投资集团有限公司",
             "杭州市国有资本投资运营有限公司", "杭州市金融投资集团有限公司", "杭州市商贸旅游集团有限公司", "杭州正才控股集团有限公司", "合盛硅业股份有限公司", "横店集团控股有限公司",
             "红狮控股集团有限公司", "湖州市城市投资发展集团有限公司", "湖州市交通投资集团有限公司", "花园集团有限公司", "华峰集团有限公司", "华立集团股份有限公司", "华数数字电视传媒集团有限公司",
             "巨化集团有限公司", "三花控股集团有限公司", "申通快递股份有限公司", "台州市椒江区国有资本运营集团有限公司", "祥源控股集团有限责任公司", "浙江东南网架集团有限公司", "浙江海正集团有限公司",
             "浙江海正药业股份有限公司", "浙江吉利控股集团有限公司", "浙江嘉兴国有资本投资运营有限公司", "浙江龙盛集团股份有限公司", "浙江省国际贸易集团有限公司", "浙江省交通投资集团有限公司",
             "浙江省金融控股有限公司", "浙江省能源集团有限公司", "浙江卫星石化股份有限公司", "正泰集团股份有限公司", "中电海康集团有限公司", "中国巨石股份有限公司", "浙江东方金融控股集团股份有限公司",
             "阳光电源股份有限公司", "安徽古井贡酒股份有限公司", "科大讯飞股份有限公司", "芜湖三七互娱网络科技集团股份有限公司", "安徽鸿路钢结构(集团)股份有限公司", "铜陵有色金属集团股份有限公司",
             "马鞍山钢铁股份有限公司", "中粮生物科技股份有限公司", "山鹰国际控股股份公司", "安徽中鼎密封件股份有限公司", "安徽楚江科技新材料股份有限公司", "长江精工钢结构(集团)股份有限公司",
             "安徽合力股份有限公司", "安徽新华传媒股份有限公司", "淮河能源(集团)股份有限公司", "安徽辉隆农资集团股份有限公司", "铜陵精达特种电磁线股份有限公司", "合肥百货大楼集团股份有限公司",
             "安徽出版集团有限责任公司", "安徽古井集团有限责任公司", "安徽海螺水泥股份有限公司", "安徽淮海实业发展集团有限公司", "安徽省能源集团有限公司", "安徽省投资集团控股有限公司",
             "安徽省皖能股份有限公司", "安徽新华发行(集团)控股有限公司", "合肥市建设投资控股(集团)有限公司", "淮北矿业(集团)有限责任公司", "淮北矿业控股股份有限公司", "淮北市建投控股集团有限公司",
             "江东控股集团有限责任公司", "同安控股有限责任公司", "铜陵化学工业集团有限公司", "安徽国元金融控股集团有限责任公司", "合肥兴泰金融控股(集团)有限公司", "中航直升机股份有限公司",
             "哈药集团人民同泰医药股份有限公司", "东方集团股份有限公司", "内蒙古君正能源化工集团股份有限公司", "内蒙古第一机械集团股份有限公司", "银泰黄金股份有限公司", "内蒙古霍林河露天煤业股份有限公司",
             "内蒙古鄂尔多斯资源股份有限公司", "中盐内蒙古化工股份有限公司", "内蒙古蒙电华能热电股份有限公司", "内蒙古伊泰集团有限公司", "内蒙古伊泰煤炭股份有限公司", "亿利资源集团有限公司",
             "中国北方稀土(集团)高科技股份有限公司", "万华化学集团股份有限公司", "歌尔股份有限公司", "中航沈飞股份有限公司", "英科医疗科技股份有限公司", "山东华鲁恒升化工股份有限公司",
             "山东玲珑轮胎股份有限公司", "山东太阳纸业股份有限公司", "烟台杰瑞石油服务集团股份有限公司", "中际旭创股份有限公司", "山东步长制药股份有限公司", "九阳股份有限公司",
             "淄博齐翔腾达化工股份有限公司", "山东钢铁股份有限公司", "金能科技股份有限公司", "三角轮胎股份有限公司", "家家悦集团股份有限公司", "山东龙大肉食品股份有限公司", "山东出版传媒股份有限公司",
             "山东华泰纸业股份有限公司", "山东新华医疗器械股份有限公司", "山推工程机械股份有限公司", "国家能源集团山东电力有限公司", "华电国际电力股份有限公司", "华鲁控股集团有限公司",
             "济宁矿业集团有限公司", "济宁市兖州区惠民城建投资有限公司", "浪潮电子信息产业股份有限公司", "临沂矿业集团有限责任公司", "鲁西化工集团股份有限公司", "南山集团有限公司",
             "日照港集团有限公司", "润华集团股份有限公司", "山东出版集团有限公司", "山东富宇化工有限公司", "山东高速股份有限公司", "山东高速集团有限公司", "山东国惠投资有限公司",
             "山东宏桥新型材料有限公司", "山东黄金集团有限公司", "山东黄金矿业股份有限公司", "山东岚桥集团有限公司", "山东南山铝业股份有限公司", "山东能源重型装备制造集团有限责任公司",
             "山东齐悦科技有限公司", "山东三星集团有限公司", "山东省鲁信投资控股集团有限公司", "山东魏桥铝电有限公司", "山东招金集团有限公司", "威高集团有限公司", "新凤祥控股集团有限责任公司",
             "烟台国丰投资控股集团有限公司", "兖矿集团有限公司", "兖州煤业股份有限公司", "阳谷祥光铜业有限公司", "招金矿业股份有限公司", "中国重型汽车集团有限公司", "淄博矿业集团有限责任公司",
             "山东恒邦冶炼股份有限公司", "方大特钢科技股份有限公司", "新余钢铁股份有限公司", "江西万年青水泥股份有限公司", "长虹华意压缩机股份有限公司", "诚志股份有限公司", "江铃汽车集团有限公司",
             "江西省交通投资集团有限责任公司", "江西省投资集团有限公司", "江西铜业股份有限公司", "江西铜业集团有限公司", "江西正邦科技股份有限公司", "景德镇市国资运营投资控股集团有限责任公司",
             "南昌工业控股集团有限公司", "上饶投资控股集团有限公司", "鹰潭市国有控股集团有限公司", "中文天地出版传媒集团股份有限公司", "江苏恒瑞医药股份有限公司", "无锡药明康德新药开发股份有限公司",
             "江苏洋河酒厂股份有限公司", "江苏恒立液压股份有限公司", "江苏扬农化工股份有限公司", "天合光能股份有限公司", "通富微电子股份有限公司", "江苏中天科技股份有限公司", "海澜之家股份有限公司",
             "江苏国信股份有限公司", "无锡威孚高科技集团股份有限公司", "无锡市太极实业股份有限公司", "江苏省农垦农业发展股份有限公司", "江苏凤凰出版传媒股份有限公司", "江苏立华牧业股份有限公司",
             "中国中材国际工程股份有限公司", "江苏三房巷股份有限公司", "亚普汽车部件股份有限公司", "无锡华东重型机械股份有限公司", "无锡商业大厦大东方股份有限公司", "南京公用发展股份有限公司",
             "国电南瑞科技股份有限公司", "红豆集团有限公司", "淮安市水利控股集团有限公司", "江苏方洋集团有限公司", "江苏凤凰出版传媒集团有限公司", "江苏瀚瑞投资控股有限公司", "江苏交通控股有限公司",
             "江苏康缘集团有限责任公司", "江苏南通三建集团股份有限公司", "江苏宁沪高速公路股份有限公司", "江苏省广电有线信息网络股份有限公司", "江苏省国信集团有限公司", "江苏省农垦集团有限公司",
             "江苏省苏豪控股集团有限公司", "江苏洋河集团有限公司", "江苏悦达集团有限公司", "江苏长电科技股份有限公司", "南京钢铁股份有限公司", "南京南钢钢铁联合有限公司",
             "南京市城市建设投资控股(集团)有限责任公司", "南京市国有资产投资管理控股(集团)有限责任公司", "南京新港开发总公司", "南京新工投资集团有限责任公司", "南通沿海开发集团有限公司",
             "无锡市国联发展(集团)有限公司", "无锡市交通产业集团有限公司", "无锡市市政公用产业集团有限公司", "宿迁产业发展集团有限公司", "徐工集团工程机械股份有限公司", "徐州矿务集团有限公司",
             "盐城市国有资产投资集团有限公司", "扬州龙川控股集团有限责任公司", "扬州市城建国有资产控股(集团)有限责任公司", "镇江城市建设产业集团有限公司", "镇江国有投资控股集团有限公司",
             "镇江交通产业集团有限公司", "中材科技股份有限公司", "中天钢铁集团有限公司", "广西柳工机械股份有限公司", "广西柳州医药股份有限公司", "广汇汽车服务有限责任公司",
             "广西北部湾国际港务集团有限公司", "广西北部湾投资集团有限公司", "广西广投能源集团有限公司", "广西桂冠电力股份有限公司", "广西宏桂资本运营集团有限公司", "广西金桂浆纸业有限公司",
             "广西金融投资集团有限公司", "广西柳工集团有限公司", "广西柳州钢铁集团有限公司", "广西柳州市东城投资开发集团有限公司", "广西农垦集团有限责任公司", "恒逸石化股份有限公司",
             "柳州钢铁股份有限公司", "公牛集团股份有限公司", "宁波舟山港股份有限公司", "雅戈尔集团股份有限公司", "东方日升新能源股份有限公司", "天邦食品股份有限公司", "宁波太平鸟时尚服饰股份有限公司",
             "宁波金田铜业(集团)股份有限公司", "宁波华翔电子股份有限公司", "宁波博威合金材料股份有限公司", "宁波三星医疗电气股份有限公司", "奥克斯集团有限公司", "宁波城建投资控股有限公司",
             "宁波开发投资集团有限公司", "宁波舟山港集团有限公司", "杉杉集团有限公司", "余姚市舜财投资控股有限公司", "韵达控股股份有限公司", "宁波杉杉股份有限公司", "宁夏宝丰能源集团股份有限公司",
             "海尔智家股份有限公司", "青岛啤酒股份有限公司", "海信视像科技股份有限公司", "青岛国恩科技股份有限公司", "澳柯玛股份有限公司", "利群商业集团股份有限公司",
             "青岛城市建设投资(集团)有限责任公司", "青岛港(集团)有限公司", "青岛港国际股份有限公司", "青岛海湾集团有限公司", "青岛军民融合发展集团有限公司", "青岛西海岸新区海洋控股集团有限公司",
             "青海省国有资产投资管理有限公司", "西部矿业股份有限公司", "盛屯矿业集团股份有限公司", "厦门合兴包装印刷股份有限公司", "恒安(中国)投资有限公司", "厦门港务发展股份有限公司",
             "厦门港务控股集团有限公司", "厦门国际港务股份有限公司", "厦门金圆投资集团有限公司", "厦门路桥建设集团有限公司", "厦门钨业股份有限公司", "厦门翔业集团有限公司",
             "益海嘉里金龙鱼粮油食品股份有限公司", "上海韦尔半导体股份有限公司", "华域汽车系统股份有限公司", "上海晨光文具股份有限公司", "上海宝信软件股份有限公司", "环旭电子股份有限公司",
             "招商局能源运输股份有限公司", "上海爱旭新能源股份有限公司", "上海百联集团股份有限公司", "东方明珠新媒体股份有限公司", "上海家化联合股份有限公司", "光明乳业股份有限公司",
             "欧普照明股份有限公司", "上海中谷物流股份有限公司", "老凤祥股份有限公司", "上海机电股份有限公司", "港中旅华贸国际物流股份有限公司", "思源电气股份有限公司", "德邦物流股份有限公司",
             "上海华东电脑股份有限公司", "上海华谊集团股份有限公司", "上海现代制药股份有限公司", "上海梅林正广和股份有限公司", "鹏欣环球资源股份有限公司", "上海起帆电缆股份有限公司",
             "东方国际创业股份有限公司", "上海大屯能源股份有限公司", "百联集团有限公司", "宝山钢铁股份有限公司", "大族控股集团有限公司", "光明食品(集团)有限公司", "红星美凯龙家居集团股份有限公司",
             "金光纸业(中国)投资有限公司", "南方水泥有限公司", "上海城投(集团)有限公司", "上海地产(集团)有限公司", "上海电气集团股份有限公司", "上海纺织(集团)有限公司",
             "上海复星高科技(集团)有限公司", "上海复星医药(集团)股份有限公司", "上海国际港务(集团)股份有限公司", "上海海立(集团)股份有限公司", "上海华谊(集团)公司", "上海均瑶(集团)有限公司",
             "上海浦东发展(集团)有限公司", "上海浦东路桥建设股份有限公司", "上海汽车集团股份有限公司", "上海上实(集团)有限公司", "上海外高桥集团股份有限公司", "上海文化广播影视集团有限公司",
             "上海医药集团股份有限公司", "上海永达投资控股集团有限公司", "上海豫园旅游商城(集团)股份有限公司", "上海圆通蛟龙投资发展(集团)有限公司", "上海紫江(集团)有限公司",
             "上海紫江企业集团股份有限公司", "申能(集团)有限公司", "申能股份有限公司", "致达控股集团有限公司", "中国宝武钢铁集团有限公司", "中国船舶工业集团有限公司", "中国远洋海运集团有限公司",
             "中化国际(控股)股份有限公司", "中交上海航道局有限公司", "中交疏浚(集团)股份有限公司", "中芯国际集成电路制造有限公司", "中远海运能源运输股份有限公司", "鸿商产业控股集团有限公司",
             "深圳迈瑞生物医疗电子股份有限公司", "立讯精密工业股份有限公司", "富士康工业互联网股份有限公司", "深圳市汇川技术股份有限公司", "深圳传音控股股份有限公司", "鹏鼎控股(深圳)股份有限公司",
             "稳健医疗用品股份有限公司", "深圳市汇顶科技股份有限公司", "深南电路股份有限公司", "大族激光科技产业集团股份有限公司", "深圳市兆驰股份有限公司", "深圳市裕同包装科技股份有限公司",
             "深圳长城开发科技股份有限公司", "健康元药业集团股份有限公司", "华润三九医药股份有限公司", "国药集团一致药业股份有限公司", "深圳市中金岭南有色金属股份有限公司", "深圳市爱施德股份有限公司",
             "神州数码信息服务股份有限公司", "深圳华强实业股份有限公司", "深圳齐心集团股份有限公司", "天虹数科商业股份有限公司", "深圳市共进电子股份有限公司", "深圳市深粮控股股份有限公司",
             "深圳广田集团股份有限公司", "比亚迪股份有限公司", "创维集团有限公司(境内)", "格林美股份有限公司", "华润股份有限公司", "华为投资控股有限公司", "领益科技(深圳)有限公司",
             "深业集团有限公司", "深圳华大基因股份有限公司", "深圳华强集团有限公司", "深圳能源集团股份有限公司", "深圳市宝德投资控股有限公司", "深圳市地铁集团有限公司", "深圳市东阳光实业发展有限公司",
             "深圳市燃气集团股份有限公司", "深圳市水务(集团)有限公司", "深圳市特发集团有限公司", "深圳市投资控股有限公司", "深圳顺丰泰森控股(集团)有限公司", "天马微电子股份有限公司",
             "招商局港口集团股份有限公司", "中国宝安集团股份有限公司", "中国广核电力股份有限公司", "中国广核集团有限公司", "中国国际海运集装箱(集团)股份有限公司", "中国航空技术深圳有限公司",
             "中国南玻集团股份有限公司", "中航国际控股有限公司", "中燃投资有限公司", "鞍钢股份有限公司", "辽宁禾丰牧业股份有限公司", "本钢板材股份有限公司", "凌源钢铁股份有限公司",
             "鞍钢集团有限公司", "鞍山钢铁集团有限公司", "辽宁方大集团实业有限公司", "辽宁省国有资产经营有限公司", "长城汽车股份有限公司", "晶澳太阳能科技股份有限公司",
             "中国船舶重工集团动力股份有限公司", "新奥天然气股份有限公司", "石家庄以岭药业股份有限公司", "华北制药股份有限公司", "唐山港集团股份有限公司", "河北建投能源投资股份有限公司",
             "河北四通新型金属材料股份有限公司", "保定市长城控股集团有限公司", "曹妃甸国控投资集团有限公司", "河北港口集团有限公司", "河北建设投资集团有限责任公司", "河北省高速公路开发有限公司",
             "冀中能源峰峰集团有限公司", "冀中能源股份有限公司", "开滦能源化工股份有限公司", "凌云工业股份有限公司", "石家庄国控投资集团有限责任公司", "唐山曹妃甸发展投资集团有限公司",
             "唐山港口实业集团有限公司", "唐山冀东水泥股份有限公司", "唐山三友化工股份有限公司", "新奥控股投资股份有限公司", "新天绿色能源股份有限公司", "新兴铸管股份有限公司",
             "苏州东山精密制造股份有限公司", "沪士电子股份有限公司", "江苏亨通光电股份有限公司", "江苏沙钢股份有限公司", "苏州金螳螂建筑装饰股份有限公司", "天顺风能(苏州)股份有限公司",
             "江苏国泰国际集团股份有限公司", "东华能源股份有限公司", "亨通集团有限公司", "江苏东方盛虹股份有限公司", "江苏国泰国际贸易有限公司", "江苏沙钢集团有限公司", "江苏永钢集团有限公司",
             "苏州创元投资发展(集团)有限公司", "山西杏花村汾酒厂股份有限公司", "大秦铁路股份有限公司", "山西美锦能源股份有限公司", "山西焦化股份有限公司", "北方铜业股份有限公司",
             "大唐山西发电有限公司", "格盟国际能源有限公司", "华远国际陆港集团有限公司", "晋能控股电力集团有限公司", "晋能控股山西煤业股份有限公司", "山西国际电力集团有限公司",
             "山西焦煤能源集团股份有限公司", "山西潞安环保能源开发股份有限公司", "山西省文化旅游投资控股集团有限公司", "山西太钢不锈钢股份有限公司", "山西园区建设发展集团有限公司",
             "太原钢铁(集团)有限公司", "阳泉煤业(集团)股份有限公司", "三六零安全科技股份有限公司", "曙光信息产业股份有限公司", "海洋石油工程股份有限公司", "天津港股份有限公司",
             "国机汽车股份有限公司", "天津保税区投资控股集团有限公司", "天津渤海国有资产经营管理有限公司", "天津城市基础设施建设投资集团有限公司", "天津能源投资集团有限公司", "天津市医药集团有限公司",
             "天津中环半导体股份有限公司", "天津中环电子信息集团有限公司", "天士力控股集团有限公司", "天士力医药集团股份有限公司", "中储发展股份有限公司", "中海油田服务股份有限公司",
             "三安光电股份有限公司", "闻泰科技股份有限公司", "中信泰富特钢集团股份有限公司", "居然之家新零售集团股份有限公司", "中航工业机电系统股份有限公司", "安琪酵母股份有限公司",
             "宏发科技股份有限公司", "烽火通信科技股份有限公司", "新洋丰农业科技股份有限公司", "航天时代电子技术股份有限公司", "安道麦股份有限公司", "东风汽车股份有限公司", "骆驼集团股份有限公司",
             "湖北凯乐科技股份有限公司", "武汉力源信息技术股份有限公司", "东风汽车集团股份有限公司", "湖北能源集团股份有限公司", "湖北省宏泰国有资本投资运营集团有限公司", "湖北省文化旅游投资集团有限公司",
             "湖北省长江产业投资集团有限公司", "湖北兴发化工集团股份有限公司", "湖北长江出版传媒集团有限公司", "华新水泥股份有限公司", "九州通医药集团股份有限公司", "启迪环境科技发展股份有限公司",
             "人福医药集团股份公司", "三环集团有限公司", "武汉港航发展集团有限公司", "武汉商贸集团有限公司", "武汉市城市建设投资开发集团有限公司", "武汉正通联合实业投资集团有限公司",
             "宜昌兴发集团有限责任公司", "长飞光纤光缆股份有限公司", "中铁第四勘察设计院集团有限公司", "中国航发动力股份有限公司", "中航西安飞机工业集团股份有限公司", "陕西北元化工集团股份有限公司",
             "中国西电电气股份有限公司", "金堆城钼业股份有限公司", "西安陕鼓动力股份有限公司", "陕西黑猫焦化股份有限公司", "隆基绿能科技股份有限公司", "陕西煤业股份有限公司",
             "陕西煤业化工集团有限责任公司", "陕西燃气集团有限公司", "陕西省天然气股份有限公司", "陕西投资集团有限公司", "陕西延长石油(集团)有限责任公司", "陕西榆林能源集团有限公司",
             "西安城市基础设施建设投资集团有限公司", "西安工业投资集团有限公司", "梅花生物科技集团股份有限公司", "拉萨市城市建设投资经营有限公司", "华润水泥控股有限公司", "中银集团投资有限公司",
             "新疆天山水泥股份有限公司", "天康生物股份有限公司", "新疆天业股份有限公司", "昌吉州国有资产投资经营集团有限公司", "特变电工股份有限公司", "新疆广汇实业投资(集团)有限责任公司",
             "新疆金风科技股份有限公司", "新疆可克达拉市国有资本投资运营有限责任公司", "中建西部建设股份有限公司", "中粮糖业控股股份有限公司", "云南驰宏锌锗股份有限公司", "一心堂药业集团股份有限公司",
             "云南铝业股份有限公司", "贵研铂业股份有限公司", "昆明云内动力股份有限公司", "昆药集团股份有限公司", "华能澜沧江水电股份有限公司", "昆明产业开发投资有限责任公司",
             "昆明交通产业股份有限公司", "云南白药集团股份有限公司", "云南合和(集团)股份有限公司", "云南省滇中产业发展集团有限责任公司", "云南省工业投资控股集团有限责任公司",
             "云南省建设投资控股集团有限公司", "云南省能源投资集团有限公司", "云南省投资控股集团有限公司", "云南铜业(集团)有限公司", "云南锡业股份有限公司", "长春高新技术产业(集团)股份有限公司",
             "富奥汽车零部件股份有限公司", "长春一汽富维汽车零部件股份有限公司", "金圆环保股份有限公司", "吉林市城市建设控股集团有限公司", "龙翔投资控股集团有限公司", "中国第一汽车集团有限公司",
             "吉林省国有资本运营有限责任公司", "爱尔眼科医院集团股份有限公司", "蓝思科技股份有限公司", "芒果超媒股份有限公司", "安克创新科技股份有限公司", "益丰大药房连锁股份有限公司",
             "株洲旗滨集团股份有限公司", "老百姓大药房连锁股份有限公司", "湖南华菱钢铁股份有限公司", "中南出版传媒集团股份有限公司", "湖南黄金股份有限公司", "山河智能装备股份有限公司",
             "唐人神集团股份有限公司", "步步高商业连锁股份有限公司", "金杯电工股份有限公司", "湖南富兴集团有限公司", "湖南华菱钢铁集团有限责任公司", "湖南五江轻化集团有限公司", "三一集团有限公司",
             "长沙市城市建设投资开发集团有限公司", "长沙先导投资控股集团有限公司", "中联重科股份有限公司", "株洲市国有资产投资控股集团有限公司", "河南双汇投资发展股份有限公司", "中航光电科技股份有限公司",
             "郑州宇通客车股份有限公司", "郑州煤矿机械集团股份有限公司", "三全食品股份有限公司", "河南明泰铝业股份有限公司", "河南豫能控股股份有限公司", "河南平高电气股份有限公司",
             "神马实业股份有限公司", "第一拖拉机股份有限公司", "中原大地传媒股份有限公司", "河南交通投资集团有限公司", "河南省交通运输发展集团有限公司", "河南投资集团有限公司",
             "洛阳栾川钼业集团股份有限公司", "牧原食品股份有限公司", "平顶山天安煤业股份有限公司", "许继电气股份有限公司", "郑州发展投资集团有限公司", "郑州航空港兴港投资集团有限公司",
             "郑州宇通集团有限公司", "中国水利水电第十一工程局有限公司", "中国一拖集团有限公司", "中原出版传媒投资控股集团有限公司", "重庆智飞生物制品股份有限公司", "重庆百货大楼股份有限公司",
             "华邦生命健康股份有限公司", "重庆宗申动力机械股份有限公司", "隆鑫通用动力股份有限公司", "重庆钢铁股份有限公司", "重庆市国有文化资产经营管理有限责任公司", "重庆市水务资产经营有限公司"]
    p = CmpHolder()
    p.process(names)
