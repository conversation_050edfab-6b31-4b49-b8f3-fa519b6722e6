# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2020/12/24
from pprint import pprint
from core.data import DataFiller, Field, CondField
from core.excel_base import ExcelBase
from moduler.base_info_api_thread import BaseInfoMaster
from moduler.company_reward_info import Reward
from moduler.company_standard_info import StandardInfo


# 获取公司名单
class Mater(ExcelBase):
    def __init__(self):
        super(Mater, self).__init__()
        self.file_name = self._in_file_path + "/丰台企业.xlsx"

    def process(self, *args, **kwargs):
        result = list()
        items = self.read_to_excel()
        for item in items:
            name = item["企业名称"].replace("(", "（").replace(")", "）").strip()
            result.append(name)
        return result

    def read_to_excel(self):
        data_list = self._extract_data(self.file_name, "丰台")
        return data_list


# 整合层
class Task(DataFiller):
    # 获取公司名单
    names = Field(Mater.run)

    base_info = CondField(BaseInfoMaster.run, depend=["names"])

    reword_info = CondField(Reward.run, depend=["names"])

    standard_info = CondField(StandardInfo.run, depend=["names"])

    def __init__(self):
        super(Task, self).__init__()


class Result(ExcelBase):
    def __init__(self):
        super(Result, self).__init__()

    def process(self):
        error_list = list()
        try:
            task = Task()
        except Exception as exc:
            error_list = [self.error_message(exc)]
        finally:
            error_list.extend(task.exc_info_list)
            item_list = self.dict_merge(task.data)
            self.save_data_excel(item_list)
        if error_list:
            pprint(error_list)

    @staticmethod
    def dict_merge(item_dict):
        result = list()
        for name in item_dict["names"]:
            tmp = dict()
            for info in ["base_info", "reword_info", "standard_info"]:
                data_dict = item_dict[info]
                _data = data_dict.get(name, dict())
                if info == 'reword_info':
                    tmp["reward"] = ",".join(list({i.get("reward_name", "") for i in _data}))
                elif info == 'standard_info':
                    tmp["standard"] = ",".join(list({i.get("standard_name", "") for i in _data}))
                elif info == 'base_info':
                    tmp.update(_data)
            result.append(tmp)
        return result

    def save_data_excel(self, result):
        field_cfg = {
            'companyName': ('公司名称', 0),
            'credit_code': ('统一信用代码', 1),
            'legal_person_name': ('法定代表人', 2),
            'reg_capital': ('注册资本', 3),
            'establish_date': ('成立时间', 4),
            'reg_status': ('注册状态', 5),
            'company_org_type': ('公司组织类型', 6),
            'reg_province': ('省', 7),
            'reg_city': ('市', 8),
            'reg_district': ('区', 9),
            'reg_location': ('注册地址', 10),
            'postal_address': ('办公地址', 11),
            'phone_number': ('联系电话', 12),
            'business_scope': ('经营范围', 13),
            'company_nature': ('性质标签', 14),
            'market': ('所属市场', 15),
            'reward': ('政府奖励', 16),
            'licence': ('专项证照', 17),
            'standard': ('起草标准', 18),
            'company_business': ('业务亮点', 19)}
        self._excel_name = self.name_add_date("中关村科技软件POC数据.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})


if __name__ == '__main__':
    Result.run()
