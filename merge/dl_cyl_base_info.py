# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/24
import regex
from copy import deepcopy
from collections import defaultdict

from core.excel_base import ExcelBase
from moduler.base_info_api_thread import BaseInfoMaster
from moduler.company_area_info import CompanyArea
from moduler.company_cur_name import CurName
from moduler.company_cyl_and_industry import ChinaToExcel
from moduler.company_gb_industry import CompanyGBIndustry
from moduler.company_law_data import CompanyCPWS, CompanyFYGG, CompanyKTGG
from moduler.company_mysql_reward import MysqlReward
from moduler.company_repoet_shebao import She<PERSON><PERSON>
from moduler.company_supply_data import CompanySupply


class DLCYL(ExcelBase):
    def __init__(self):
        super(DLCYL, self).__init__()
        self.cyl = ChinaToExcel()
        self.base = BaseInfoMaster()
        self.gb_ind = CompanyGBIndustry()
        self.gys = CompanySupply()
        self.shebao = SheBao()
        self.jiangli = MysqlReward()
        self.area = CompanyArea()
        self.cpws = CompanyCPWS()
        self.fygg = CompanyFYGG()
        self.ktgg = CompanyKTGG()
        self.alias = CurName()
        self.pattern = regex.compile(r"注销|取消|撤销|吊销|不予换发|过期|作废|变更|收缴|退出|停业")

    def process(self, *args, **kwargs):
        result = list()
        cyl_items = self.cyl.run(["电力产业链", "电力设备产业链"])
        cyl_tmp_dict = defaultdict(list)
        for cyl_item in cyl_items:
            company_name = cyl_item["company_name"]
            cyl_alias = self.alias.run(company_name)
            if cyl_alias:
                cur_n = cyl_alias[0]["cur_name"]
            else:
                cur_n = company_name
            cyl_tmp_dict[cur_n].append(cyl_item)
        licence_names = self.read_excel_data(self._in_file_path + "/四川电力行业公司名录.xlsx", "Sheet1")
        lic_list = {i["name"] for i in licence_names}
        lic_alias = self.query_cur_name(list(lic_list))
        lic_dict = {lic_alias.get(i["name"], i["name"]): [{"cyl_name": "电力证照"}] for i in licence_names}
        inv_names = self.read_excel_data(self._in_file_path + "/国家电网下属投资公司_20210329.xlsx")
        inv_list = {i["成员企业名称"] for i in inv_names}
        inv_alias = self.query_cur_name(list(inv_list))
        inv_dict = {inv_alias.get(i["成员企业名称"], i["成员企业名称"]): [{"cyl_name": "国家电网下属投资公司"}] for i in inv_names}
        base_info = self.base.run(list(cyl_tmp_dict.keys()) + list(lic_dict.keys()) + list(inv_dict.keys()))
        base_list, names = self.data_process(cyl_tmp_dict, base_info, lic_dict, inv_dict)
        # 国标行业
        gb_dict = self.gb_ind.run(list(names))
        # 社保数据
        sb_dict = self.shebao.run(list(names))
        # 奖励数据
        jl_dict = self.jiangli.run(list(names))
        # 供应商
        raw_gys_dict = self.gys.run(list(names))
        gys_dict = self.gys_data_process(raw_gys_dict)
        # 财务数据
        yl_dict = self.query_yl_data()
        fz_dict = self.query_fz_data()
        self.get_data(gb_dict, sb_dict, jl_dict, base_list, gys_dict, yl_dict, fz_dict)
        # 法律数据处理
        self.law_process(list(names), base_list)
        self.save_data_excel(base_list)

    def law_process(self, names, base_list):
        cpws_dict = self.cpws.run(names)
        fygg_dict = self.fygg.run(names)
        ktgg_dict = self.ktgg.run(names)
        law_dict = {
            "judgeTime": cpws_dict,
            "ctimeDate": fygg_dict,
            "eventTime": ktgg_dict}
        for item in base_list:
            name = item["companyName"]
            is_ss = False
            for field, dic in law_dict.items():
                ss_list = dic.get(name)
                if ss_list:
                    for ss in ss_list:
                        date = ss.get(field)
                        if date:
                            if str(date) > "2020-01-01":
                                item.update({"law": "是"})
                                is_ss = True
                                break

            if not is_ss:
                item.update({"law": "否"})

    def read_excel_data(self, file_name, sheet="Sheet"):
        data_list = self._extract_data(file_name, sheet)
        return data_list

    def gys_data_process(self, gys_dict):
        result = dict()
        for name, items in gys_dict.items():
            gys, kh = set(), set()
            for item in items:
                gys_name = item["company_name"]
                rel = item["relation"]
                if rel == "供应商":
                    gys.add(gys_name)
                elif rel == "销售客户":
                    kh.add(gys_name)

            r_gys, r_kh = list(), list()
            area_dict = self.area.run(list(gys | kh))
            for n, raw_item in area_dict.items():
                province = raw_item.get("provinceName")
                if not province or province != '四川省':
                    continue
                if n in gys:
                    r_gys.append(n)
                if n in kh:
                    r_kh.append(n)
            if r_gys or r_kh:
                result.setdefault(name, {"gys": ",".join(r_gys), "kh": ",".join(r_kh)})
        return result

    def save_data_excel(self, result):
        field_cfg = {
            'cyl_name': ('类别', 0),
            'companyName': ('公司名称', 1),
            'credit_code': ('统一信用代码', 2),
            'legal_person_name': ('法定代表人', 3),
            'reg_capital': ('注册资本', 4),
            'establish_date': ('成立时间', 5),
            'reg_status': ('注册状态', 6),
            'company_org_type': ('公司组织类型', 7),
            'reg_province': ('省', 8),
            'reg_city': ('市', 9),
            'reg_district': ('区', 10),
            'reg_location': ('注册地址', 11),
            'postal_address': ('办公地址', 12),
            'phone_number': ('联系电话', 13),
            'business_scope': ('经营范围', 14),
            'company_nature': ('性质标签', 15),
            'market': ('所属市场', 16),
            'company_business': ('业务亮点', 17),
            'first_industry': ('视野一级行业', 18),
            'second_industry': ('视野二级行业', 19),
            'ind_m': ('国标行业（门）', 20),
            'ind_d': ('国标行业（大）', 21),
            'ind_z': ('国标行业（中）', 22),
            'endowment_insurance': ('社保缴纳人数', 23),
            'jl': ('是否有政府奖励', 24),
            'gys': ('供应商', 25),
            'kh': ('销售客户', 26),
            'law': ('是否涉诉/涉案', 27),
            'bizInco': ('营业收入（万元）', 28),
            'netProfit': ('净利润（万元）', 29),
            'accoRece': ('应收账款（万元）', 30),
            'accoPaya': ('应付账款（万元）', 31),
        }

        self._excel_name = self.name_add_date("电力产业画像数据.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    @staticmethod
    def get_data(gb_dict, sb_dict, jl_dict, base_list, gys_dict, yl_dict, fz_dict):
        for item in base_list:
            name = item["companyName"]
            gb = gb_dict.get(name)
            if gb:
                item.update(gb)
            sb = sb_dict.get(name)
            if sb:
                item.update(sb)
            jl = jl_dict.get(name)
            if jl:
                item.update({"jl": "是"})
            else:
                item.update({"jl": "否"})
            gys = gys_dict.get(name)
            if gys:
                item.update(gys)
            yl = yl_dict.get(name)
            if yl:
                item.update(yl)
            fz = fz_dict.get(name)
            if fz:
                item.update(fz)

    def data_process(self, cyl_tmp_dict, cyl_base, lic_dict, inv_dict):
        result, names = list(), set()
        for dic in [cyl_tmp_dict, lic_dict, inv_dict]:
            for name, items in dic.items():
                raw_base_info = cyl_base.get(name)
                if not raw_base_info:
                    continue
                base_info = deepcopy(raw_base_info)
                reg_province = base_info.get("reg_province")
                reg_status = base_info.get("reg_status") or ""
                if reg_province != "四川省" or self.pattern.search(reg_status):
                    continue
                # 一级行业
                first_industry = base_info.get("first_industry")
                if first_industry:
                    first = first_industry[0]["industry_name"]
                    base_info["first_industry"] = first
                # 二级行业
                second_industry = base_info.get("second_industry")
                if second_industry:
                    second = second_industry[0]["industry_name"]
                    base_info["second_industry"] = second

                for item in items:
                    item.update(base_info)
                    result.append(item)
                names.add(name)
        return result, names

    def query_yl_data(self):
        result = dict()
        sql_statement = """
        SELECT companyName, bizInco /10000 as bizInco, netProfit/10000 as netProfit from (
        SELECT  companyName, bizInco, netProfit, endDate, reportType, publishDate from   sy_cd_ms_fin_sk_inc as a JOIN 
        (SELECT max(endDate) as end_date, innerCode as cmp_code from sy_cd_ms_fin_sk_inc where
         reportType=3 and endDate in ("********", "********") and dataStatus != 3 GROUP BY innerCode ) as b on a.innerCode=b.cmp_code and a.endDate=b.end_date and a.reportType=3  and a.dataStatus != 3 
        JOIN sy_cd_ms_base_cn_stock c on a.innerCode=c.innerCode  and c.dataStatus != 3 ) as x GROUP BY companyName, bizInco, netProfit, endDate, reportType, publishDate
        UNION all
        SELECT  companyName, operatRevenue/10000 as bizInco, netProfit/10000 as netProfit from  sy_cd_ms_fin_nq_inc as a JOIN 
        (SELECT max(endDate) as end_date, companyCode as cmp_code from sy_cd_ms_fin_nq_inc where ifMerged=1 and endDate in ("2020-12-31", "2019-12-31") and dataStatus != 3 GROUP BY companyCode ) as b 
        on a.companyCode=b.cmp_code and a.endDate=b.end_date and a.ifMerged=1  and a.dataStatus != 3 
        JOIN sy_cd_ms_base_cn_stock c on a.companyCode=c.companyCode  and c.dataStatus != 3 
        """
        query_schema = dict(db_key="tidb_152", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result.setdefault(item["companyName"], item)
        return result

    def query_fz_data(self):
        result = dict()
        sql_statement = """
         SELECT companyName, accoPaya/10000 as accoPaya, accoRece/10000 as accoRece from (
        SELECT  companyName, accoPaya, accoRece, endDate, reportType, publishDate from  sy_cd_ms_fin_sk_balsheet as a JOIN 
        (SELECT max(endDate) as end_date, innerCode as cmp_code from sy_cd_ms_fin_sk_balsheet where
         reportType=3 and endDate in ("********", "********") and dataStatus != 3 GROUP BY innerCode ) as b on a.innerCode=b.cmp_code and a.endDate=b.end_date and a.reportType=3  and a.dataStatus != 3 
        JOIN sy_cd_ms_base_cn_stock c on a.innerCode=c.innerCode  and c.dataStatus != 3 ) as x GROUP BY companyName, accoPaya, accoRece, endDate, reportType, publishDate
        UNION
        SELECT  companyName, AR/10000 as accoRece, accountsPayable/10000 as accoPaya from  sy_cd_ms_fin_nq_balsheet as a JOIN 
        (SELECT max(endDate) as end_date, companyCode as cmp_code from sy_cd_ms_fin_nq_balsheet where ifMerged=1 and endDate in ("2020-12-31", "2019-12-31") and dataStatus != 3 GROUP BY companyCode ) as b 
        on a.companyCode=b.cmp_code and a.endDate=b.end_date and a.ifMerged=1  and a.dataStatus != 3 
        JOIN sy_cd_ms_base_cn_stock c on a.companyCode=c.companyCode and c.dataStatus != 3 GROUP BY companyName, AR, accountsPayable
        """
        query_schema = dict(db_key="tidb_152", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result.setdefault(item["companyName"], item)
        return result

    def query_cur_name(self, names):
        result = dict()
        for idx in range(0, len(names), 50):
            name_list = names[idx:idx + 50]
            query_schema = {
                "db_name": "raw_data",
                "collection_name": "company_alias_name",
                "query_condition": {'alias_name': {"$in": name_list}},
                "query_field": {"_id": 0, "alias_name": 1, "cur_name": 1}}
            query_result = self._data_server.call("query_item", query_schema) or list()
            for item in query_result:
                alias_name = item["alias_name"]
                cur_name = item["cur_name"]
                result.setdefault(alias_name, cur_name)
        return result


if __name__ == '__main__':
    p = DLCYL()
    p.process()
