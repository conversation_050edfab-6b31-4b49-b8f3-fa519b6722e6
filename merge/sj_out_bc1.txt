
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000106105',
     " with sour_df AS ( select * from seeyii_data_house.dwd_me_buss_per_jzqyzz_df where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_per_jzqyzz_df ) and datastatus != 3 ), next_df AS ( SELECT date_format(issueDate, 'yyyy-MM-dd') as eventdate, date_format(invalidDate, 'yyyy-MM-dd') as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106105' as eventtype, qualifType, qualifSeq, qualifMajor, url, concat( concat( '100127', '&#&', if(qualifType is null, '', qualifType), '&#&', '3' ) , '@@' , concat( '100128', '&#&', if( qualifClass is null, '', qualifClass ), '&#&', '3' ) ) as eigenvalue from sour_df ), add_desc_df as ( select *, concat( eventdate, '，公司获得建筑企业资质（地方）' ) as a, if( (qualifType is null) or (qualifType = ''), '', concat( '，资质类别为', qualifType ) ) as b, if( (qualifSeq is null) or (qualifSeq = ''), '', concat('，资质序列为', qualifSeq) ) as c, if( (qualifMajor is null) or (qualifMajor = ''), '', concat( '，资质专业为', qualifMajor ) ) as d, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as ( select *, concat_ws('', a, b, c, d, e) as desc1 from add_desc_df ) insert into {ku}.{tb} partition (filedate = {fileDate}) select fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, null as retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000106106',
     " with sour_df AS ( select * from seeyii_data_house.dwd_me_buss_per_jzqyzz_qg where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_per_jzqyzz_qg ) and datastatus != 3 ), next_df AS ( SELECT date_format(issueDate, 'yyyy-MM-dd') as eventdate, date_format(invalidDate, 'yyyy-MM-dd') as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106106' as eventtype, qualifType, qualifClass, url, concat( concat( '100129', '&#&', if(qualifType is null, '', qualifType), '&#&', '3' ) , '@@' , concat( '100130', '&#&', if( qualifClass is null, '', qualifClass ), '&#&', '3' ) ) as eigenvalue from sour_df ), add_desc_df as ( select *, concat( eventdate, '，公司获得建筑企业资质（全国）' ) as a, if( (qualifType is null) or (qualifType = ''), '', concat( '，资质类别为', qualifType ) ) as b, if( (qualifClass is null) or (qualifClass = ''), '', concat( '，资质等级为', qualifClass ) ) as c, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as ( select *, concat_ws('', a, b, c, e) as desc1 from add_desc_df ) insert into {ku}.{tb} partition (filedate = {fileDate}) select fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, null as retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000106129',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, cartoonnm, subjects, date_format(publishdate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, url, concat( concat( '100137', '&#&', if(subjects is null, '', subjects), '&#&', '3' ), '@@', concat( '100138', '&#&', regexp_replace(filmlgth, '，|,| ', ''), '&#&', '3' ) ) as eigenvalue from seeyii_data_house.dwd_me_buss_cn_tv_cartoon where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_cn_tv_cartoon ) and datastatus != 3 and publishdate is not null and publishdate != '' and compcode is not null ), next_df AS ( select *, if( (cartoonnm is null) or (cartoonnm = ''), '', concat('，片名为', cartoonnm) ) as a0, if( (subjects is null) or (subjects = ''), '', concat('，题材为', subjects) ) as a1 from base_df ), final_df AS ( select *, concat( eventdate, '，公司获得全国国产电视动画片制作备案', a0, a1, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000106129', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000106129' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, null as retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000308003',
     " with base_df AS ( select opunitcode as subjectcode, operaunit as eventsubject, proname, protype, date_format(opentime, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, url, concat( concat( '100141', '&#&', proActInvest, '&#&', '3' ) ) as eigenvalue from seeyii_data_house.dwd_me_buss_per_qyhpyskey where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_per_qyhpyskey ) and datastatus != 3 and isvalid = 1 and opentime is not null and opentime != '' and opunitcode is not null ), next_df AS ( select *, if( (proname is null) or (proname = ''), '', concat('，项目名称为', proname) ) as a0, if( (protype is null) or (protype = ''), '', concat('，项目类型为', protype) ) as a1 from base_df ), final_df AS ( select *, concat( eventdate, '，企业环评项目获验收', a0, a1, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = {fileDate}) select fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , 'SJ000308003' , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000308003' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, null as retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');


       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000103044',
        " with base_df AS ( select compcode as subjectcode, compname as eventsubject,prjname,energytype,targetprice, date_format(pubbegin, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, url ,concat( concat('100142','&#&',if(installedCap is null, '', installedCap),'&#&','3') ,'@@' ,concat('100143','&#&',if(targetprice is null, '', targetprice),'&#&','3') ) as eigenvalue from seeyii_data_house.dwd_me_buss_per_esgccfbs where filedate in (select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_per_esgccfbs) and datastatus != 3 and isvalid = 1 and pubbegin is not null and pubbegin != '' and compcode is not null ), next_df AS ( select *, if((prjname is null) or (prjname =''),'',concat('，项目名称为',prjname)) as a0, if((energytype is null) or (energytype =''),'',concat('，能源类型为',energytype)) as a1, if((targetprice is null) or (targetprice =''),'',concat('，上网电价（含税）为',targetprice,'元/千瓦时')) as a2 from base_df ), final_df AS ( select *, concat(eventdate,'，公司项目获得最新分布式新能源入网补贴',a0,a1,a2,'。') as desc1 from next_df ) insert into {ku}.{tb} partition (filedate={fileDate}) select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000103044' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000103044' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue,null as retrovalue, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
        map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000103045',
        " with base_df AS ( select compcode as subjectcode, compname as eventsubject,prjname,energytype,upprice, date_format(pubbegin, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, url ,concat( concat('100144','&#&',if(realscale is null, '', realscale),'&#&','3') ,'@@' ,concat('100145','&#&',if(upprice is null, '', upprice),'&#&','3') ) as eigenvalue from seeyii_data_house.dwd_me_buss_per_esgccjzs where filedate in (select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_per_esgccjzs) and datastatus != 3 and isvalid = 1 and pubbegin is not null and pubbegin != '' and compcode is not null ), next_df AS ( select *, if((prjname is null) or (prjname =''),'',concat('，项目名称为',prjname)) as a0, if((energytype is null) or (energytype =''),'',concat('，能源类型为',energytype)) as a1, if((upprice is null) or (upprice =''),'',concat('，上网电价（含税）为',upprice,'元/千瓦时')) as a2 from base_df ), final_df AS ( select *, concat(eventdate,'，公司项目获得最新集中式新能源入网补贴',a0,a1,a2,'。') as desc1 from next_df ) insert into {ku}.{tb} partition (filedate={fileDate}) select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000103045' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000103045' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue,null as retrovalue, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
        map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000307008',
        " with base_se_df AS ( select pkid, compcode, compname from seeyii_data_house.dwd_me_buss_gov_project_se where filedate in (select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_gov_project_se) and datastatus != 3 ), bf_base_df AS ( select id,projectname,projectstatus, date_format(publishdate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, sourceurl as url, concat(concat('100146','&#&',regexp_replace(totalinvest, '，|待定|,| ', ''),'&#&','3'),'@@',concat('100170','&#&',if(datasource is null, '', datasource),'&#&','3')) as eigenvalue from seeyii_data_house.dwd_me_buss_gov_project where filedate in (select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_gov_project) and datastatus != 3 and publishdate is not null and publishdate != '' ), base_df AS ( select b.compcode as subjectcode, b.compname as eventsubject,projectname,projectstatus, eventdate,expiredate,url,a.eigenvalue as eigenvalue from bf_base_df as a left join base_se_df as b on a.id=b.pkid where b.pkid is not null ), next_df AS ( select *, if((projectname is null) or (projectname =''),'',concat('，项目名称为',projectname)) as a0, if((projectstatus is null) or (projectstatus =''),'',concat('，项目建设状态为',projectstatus)) as a1 from base_df ), final_df AS ( select *, concat(eventdate,'，公司发布参与重点政府重大建设项目',a0,a1,'。') as desc1 from next_df ) insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000307008' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000307008' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, eigenvalue,null as retrovalue, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
        map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000106150',
        " with source_dat AS ( select date_format(effstartdate, 'yyyy-MM-dd') as eventdate,srcurl as url,date_format(effenddate, 'yyyy-MM-dd') as expiredate ,compname AS eventsubject, compcode AS subjectcode,enterprisetype,qualitylevel ,case when qualitytype = '1' then '施工资质' when qualitytype = '2' then '监理资质' when qualitytype = '3' then '设计资质' else null end as lev from seeyii_data_house.dwd_me_buss_per_nwtisd_cqe where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_nwtisd_cqe) and effstartdate is not NULL and compcode is not NULL AND datastatus !=3 AND isvalid =1 ), des_dat as ( select eventsubject,subjectcode,eventdate ,'SJ000106150' as eventtype ,url ,expiredate ,concat(eventdate,'，公司获评全国水运行业监理设计施工资质企业' ,if((enterprisetype is null) or (enterprisetype =''), '' ,concat('，企业类别为',enterprisetype)) ,if((lev is null) or (lev =''), '' ,concat('，资质类别为',lev)) ,if((qualitylevel is null) or (qualitylevel =''), '' ,concat('，资质等级为',qualitylevel)) , '。' ) as desc1 ,concat(concat('100149','&#&',if(lev is null, '', lev),'&#&','3'),'@@',concat('100150','&#&',if(qualitylevel is null, '', qualitylevel),'&#&','2')) as eigenvalue,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,{fileDate} as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,eigenvalue,datastatus,filedate,modifytime ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,expiredate ,null as property1,null as property2,null as property3,null as property4 ,eigenvalue,null as retrovalue,datastatus,modifytime,filedate from ret_dat ",
        map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');





       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000205003',
        " with source_dat AS ( select from_unixtime(unix_timestamp(publishdate,'yyyyMMdd'),'yyyy-MM-dd') as eventdate ,CAST(NULL AS STRING ) AS eventsubject, CAST(compcode AS STRING ) AS subjectcode,issuemode,newtotraiseamt ,case when issuetype = '02' then '增发' when issuetype = '06' then '增发（配套募集资金）' else null end as tp from seeyii_data_house.dwd_me_trad_sk_proaddiss where filedate in (select max(filedate) from seeyii_data_house.dwd_me_trad_sk_proaddiss) and publishdate is not NULL and compcode is not NULL AND datastatus !=3 AND isvalid=1 and isfinsuc=1 ), mt_ct_sys_const AS ( SELECT * FROM seeyii_data_house.dwd_mt_ct_sys_const WHERE datastatus!=3 AND constCode=37 and cValue !='' AND cValue IS NOT NULL ), deal_a AS ( select a.eventdate,a.eventsubject,a.subjectcode,a.tp,b.constvaluedesc,a.newtotraiseamt from source_dat as a join mt_ct_sys_const as b on a.issuemode=b.cValue ), des_dat as ( select eventsubject,subjectcode,eventdate ,'SJ000205003' as eventtype ,concat('100151','&#&',newtotraiseamt,'&#&','2') as eigenvalue ,CAST(null AS STRING) as url ,CAST(null AS STRING) as expiredate ,concat(eventdate,'，A股公司发布股票增发公告信息' ,if((tp is null) or (tp =''), '' ,concat('，发行类型为',tp)) ,if((constvaluedesc is null) or (constvaluedesc =''), '' ,concat('，发行方式为',constvaluedesc)) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,'{fileDate}' as filedate from deal_a ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime,eigenvalue ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue,null as retrovalue ,datastatus,modifytime,filedate from ret_dat ",
        map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000209001',
        " with source_dat AS ( select from_unixtime(unix_timestamp(publishdate,'yyyyMMdd'),'yyyy-MM-dd') as eventdate ,CAST(NULL AS STRING ) AS eventsubject, CAST(compcode AS STRING ) AS subjectcode,graobj,totcashdv ,case when divitype = '0' then '不分配' when divitype = '1' then '现金分红' when divitype = '5' then '送股' when divitype = '8' then '转增' when divitype = '15' then '现金分红+送股' when divitype = '18' then '现金分红+转增' when divitype = '58' then '送股+转增' when divitype = '158' then '现金分红+送股+转增' when divitype = 'W' then '赠股' else null end as dictp ,case when graobjtype = '1' then '全体股东' when graobjtype = '2' then '流通股东' when graobjtype = '3' then '非流通股东' when graobjtype = '9' then '其他' when graobjtype = '4' then '无限售股东' when graobjtype = '5' then '有限售股东' else null end as gractp ,case when projecttype = '1' then '正案' when projecttype = '2' then '预案1' when projecttype = '3' then '预案2' when projecttype = '4' then '预案3' when projecttype = '5' then '预案4' when projecttype = '6' then '预案5' when projecttype = '7' then '预案6' when projecttype = '8' then '预案7' else null end as protp from seeyii_data_house.dwd_ms_sh_sk_divident where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_sh_sk_divident) and publishdate is not NULL and compcode is not NULL AND datastatus !=3 AND isvalid=1 and projecttype=1 ), des_dat as ( select eventsubject,subjectcode,eventdate ,'SJ000209001' as eventtype ,concat('100152','&#&',totcashdv,'&#&','2') as eigenvalue ,CAST(null AS STRING) as url ,CAST(null AS STRING) as expiredate ,concat(eventdate,'，A股公司发布分红公告' ,if((dictp is null) or (dictp =''), '' ,concat('，权益类型为',dictp)) ,if((gractp is null) or (gractp =''), '' ,concat('，发放对象类型为',gractp)) ,if((graobj is null) or (graobj =''), '' ,concat('，发放对象为',graobj)) ,if((protp is null) or (protp =''), '' ,concat('，方案类型为',protp)) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,'{fileDate}' as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime,eigenvalue ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue,null as retrovalue ,datastatus,modifytime,filedate from ret_dat ",
        map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000210002',
        " with source_dat AS ( select null AS eventsubject, compcode AS subjectcode,null as url,regexp_replace(format_number(entrustFinanceSum, 2), ',', '') as es ,date_format(entrustFinanceEndDate, 'yyyy-MM-dd') as expiredate, date_sub(date_format(entrustFinanceEndDate, 'yyyy-MM-dd'), 60) as eventdate from seeyii_data_house.dwd_me_trad_lc_entrustinv where filedate in (select max(filedate) from seeyii_data_house.dwd_me_trad_lc_entrustinv) and compcode is not null AND entrustFinanceEndDate is not null AND datastatus !=3 ), des_dat as ( select eventsubject,subjectcode,eventdate,url, 'SJ000210002' as eventtype, concat('100153','&#&',es,'&#&','2') as eigenvalue, CAST(null AS STRING) as expiredate, concat('A股公司的委托理财将于2个月后到期，委托截止日为',expiredate ,if((es is null) or (es =''), '' ,concat('，涉及金额：', es,'元')) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,'{fileDate}' as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime,eigenvalue ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,null as expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue,null as retrovalue ,datastatus,modifytime,filedate from ret_dat ",
        map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000310001',
        " with source_dat AS ( select date_format(infopubldate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) AS eventsubject, compcode as subjectcode, regexp_replace( eventcontent, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r|。', '' ) as eventcontent, concat( '100154', '&#&', amountinvolved, '&#&', '2' ) as eigenvalue from seeyii_data_house.dwd_me_buss_lc_majorcontract where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_lc_majorcontract ) and infopubldate is not NULL and compcode is not NULL AND datastatus != 3 ), des_dat as ( select eventsubject, subjectcode, eventdate, 'SJ000310001' as eventtype, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate, concat( eventdate, '，A股公司签订重大经营合同' , if( (eventcontent is null) or (eventcontent = ''), '', concat( '，事件内容：', eventcontent ) ) ) as desc1, eigenvalue, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, eigenvalue, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue,null as retrovalue, datastatus, modifytime, filedate from ret_dat ",
        map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000208001',
        " with source_dat AS ( select date_format(putdate, 'yyyy-MM-dd') as eventdate ,compname AS eventsubject,compcode as subjectcode,pledgor,pledgee,equityamount ,case when pledgortype = '0' then '未知' when pledgortype = '1' then '人' when pledgortype = '2' then '公司' else null end as pledtp ,case when pledgeetype = '0' then '未知' when pledgeetype = '1' then '人' when pledgeetype = '2' then '公司' else null end as pledgeetp, CASE WHEN SUBSTRING(equityamount, -2) = '万元' THEN REGEXP_REPLACE(equityamount, '万元$', '') * 10000 WHEN SUBSTRING(equityamount, -2) = '万股' THEN REGEXP_REPLACE(equityamount, '万股$', '') * 10000 ELSE equityamount END AS equityamount2 from seeyii_data_house.dwd_me_buss_equity_pledge where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_equity_pledge) and putdate is not NULL and compcode is not NULL AND datastatus !=3 AND isvalid=1 ), des_dat as ( select eventsubject,subjectcode,eventdate ,'SJ000208001' as eventtype ,CAST(null AS STRING) as url ,CAST(null AS STRING) as expiredate ,concat(eventdate,'，公司将股权出质' ,if((pledgor is null) or (pledgor =''), '' ,concat('，出质人为',pledgor)) ,if((pledtp is null) or (pledtp =''), '' ,concat('，出质人类型为',pledtp)) ,if((pledgee is null) or (pledgee =''), '' ,concat('，质权人为',pledgee)) ,if((pledgeetp is null) or (pledgeetp =''), '' ,concat('，质权人类型为',pledgeetp)) ,if((equityamount is null) or (equityamount =''), '' ,concat('，出质股权数额为',equityamount)) , '。' ) as desc1 ,concat('100155','&#&',equityamount2,'&#&','3') as eigenvalue ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,{fileDate} as filedate from source_dat where eventdate is not null ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,eigenvalue,datastatus,filedate,modifytime ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,expiredate ,null as property1,null as property2,null as property3,null as property4, eigenvalue,null as retrovalue,datastatus,modifytime,filedate from ret_dat ",
        map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000205001',
        " with source_dat AS ( select date_format(initinfopubld, 'yyyy-MM-dd') as eventdate ,CAST(null AS STRING ) AS eventsubject,compcode as subjectcode,plaProceeds ,regexp_replace(issuepurpose, '。', '') as issuepurpose ,regexp_replace(pricingmodel, '。', '') as pricingmodel ,regexp_replace(plaproceeduse, '。', '') as plaproceeduse from seeyii_data_house.dwd_me_trad_nq_sharesissue where filedate in (select max(filedate) from seeyii_data_house.dwd_me_trad_nq_sharesissue) and initinfopubld is not NULL and compcode is not NULL AND datastatus !=3 and EventProcedure=30 ), des_dat as ( select eventsubject,subjectcode,eventdate ,'SJ000205001' as eventtype ,CAST(null AS STRING) as url ,CAST(null AS STRING) as expiredate ,concat('100156','&#&',plaProceeds,'&#&','2') as eigenvalue ,concat(eventdate,'，三板公司发布股票发行信息' ,if((issuepurpose is null) or (issuepurpose =''), '' ,concat('，发行目的为',issuepurpose)) ,if((pricingmodel is null) or (pricingmodel =''), '' ,concat('，定价方式为',pricingmodel)) ,if((plaproceeduse is null) or (plaproceeduse =''), '' ,concat('，募集资金用途为',plaproceeduse)) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,'{fileDate}' as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime,eigenvalue ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue,null as retrovalue ,datastatus,modifytime,filedate from ret_dat ",
        map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');



       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000208002',
        " with base_se_df AS ( select distinct compcode,compname,id from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc_se where filedate in (select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc_se) and datastatus != 3 and compcode is not null ), base_all AS ( select tssubtype,ipname,pledgee,invenname,iptype,id, date_format(appdate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, CAST(null AS STRING) as url from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc where filedate in (select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc) and datastatus != 3 and isvalid = 1 and appdate is not null and appdate != '' and tssubtype='专利权质押合同登记的生效' ), base_df AS ( select distinct b.compcode as subjectcode, b.compname as eventsubject,tssubtype,ipname,pledgee,invenname,iptype, eventdate,expiredate,url,concat(concat('100161','&#&',iptype,'&#&','3')) as eigenvalue from base_all as a left join base_se_df as b on a.id = b.id where b.id is not null ), next_df AS ( select *, if((iptype is null) or (iptype =''),'',concat('。专利类型为',iptype)) as a, if((tssubtype is null) or (tssubtype =''),'',concat('，细分事务分类为',tssubtype)) as a0, if((ipname is null) or (ipname =''),'',concat('，专利名称为',ipname)) as a1, if((pledgee is null) or (pledgee =''),'',concat('，质权人为',pledgee)) as a2, if((invenname is null) or (invenname =''),'',concat('，发明名称为',invenname)) as a3 from base_df ), final_df AS ( select *, concat(eventdate,'，公司申请专利权质押合同登记',a,a0,a1,a2,a3,'。') as desc1 from next_df ) insert into {ku}.{tb} partition (filedate='{fileDate}') select distinct fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000208002' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000208002' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue,null as retrovalue, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
        map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000205002',
        " with base_df AS ( select compcode as subjectcode, CAST(null AS STRING) as eventsubject, maturitydescription,bondnature, to_date(cast(initialinfopubldate as timestamp))as eventdate, CAST(null AS STRING) as expiredate, CAST(null AS STRING) as url, concat(concat('100163','&#&',if(bondnature is null, '', bondnature),'&#&','3'),'@@',concat('100164','&#&',if(upperlimit is null, '', upperlimit),'&#&','2')) as eigenvalue from seeyii_data_house.dwd_mm_cn_bond_lcbondsissueplan where filedate in (select max(filedate) as filedate from seeyii_data_house.dwd_mm_cn_bond_lcbondsissueplan) and datastatus != 3 and initialinfopubldate is not null and compcode is not null ), next_df AS ( select *, if((bondnature is null) or (bondnature =''),'',concat('。债券性质为',bondnature)) as a, if((maturitydescription is null) or (maturitydescription =''),'',concat('，债券年限描述为',maturitydescription)) as a0 from base_df ), final_df AS ( select *, concat(eventdate,'，上市公司发布债券发行计划',a,a0,'。') as desc1 from next_df ) insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000205002' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000205002' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue,null as retrovalue, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
        map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000310002',
        " with source_dat AS ( select date_format(tsddate, 'yyyy-MM-dd') as eventdate,ipname,iptype,chgaftname AS eventsubject,chgaftcode as subjectcode,chgbefname from (select tsddate,ipname,iptype,chgaftname,chgaftcode,chgbefname,datastatus,isvalid,tsSubType ,row_number() over (partition by sourceid order by filedate desc) num from seeyii_data_house.dwd_me_buss_ip_tst_at ) as t where t.num=1 and t.tsddate is not NULL and t.chgaftcode is not NULL AND t.datastatus !=3 and t.isvalid =1 and t.tsSubType in ('专利申请权的转移','专利权的转移') ), des_dat as ( select eventsubject,subjectcode,eventdate ,'SJ000310002' as eventtype ,CAST(null AS STRING) as url ,CAST(null AS STRING) as expiredate ,concat('100165','&#&',iptype,'&#&','3') as eigenvalue ,concat(eventdate,'，公司发布购买专利信息' ,if((chgbefname is null) or (chgbefname =''), '' ,concat('，交易对手为',chgbefname)) ,if((ipname is null) or (ipname =''), '' ,concat('，专利名称为',ipname)) ,if((iptype is null) or (iptype =''), '' ,concat('，专利类型为',iptype)) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,'{fileDate}' as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime,eigenvalue ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue,null as retrovalue ,datastatus,modifytime,filedate from ret_dat ",
        map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000304004',
        " with source_dat AS ( select compname, compcode, concat(int(fiscalyear)+1, '-06-30') as eventdate, concat(int(fiscalyear)+1, '-12-31') as expiredate, eigenvalue from tempview3 ), des_dat as ( select compname as eventsubject, compcode as subjectcode, eventdate, CAST(null AS STRING) as url, 'SJ000304004' as eventtype, expiredate, '公司员工人数突破200人。' as desc1, eigenvalue, '1' as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime, {fileDate} as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,eigenvalue,datastatus,filedate,modifytime ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate, expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue,null as retrovalue,datastatus,modifytime,filedate from ret_dat ",
        map("tempview2","select compCode, reportid, endowmentInsure from( select compCode, reportid, regexp_extract(endowmentInsure, '[0-9]+', 0) as endowmentInsure, datastatus, row_number() over (partition by id order by filedate desc, modifyTime desc) num from seeyii_data_house.dwd_ms_base_comp_report_ssf ) t where t.num=1 and t.datastatus != 3",
        "tempview3","with new_report1_tb as ( select compCode, compname, reportid, fiscalyear from( select *, row_number() over (partition by compCode order by fiscalyear desc) num from tempview1 ) t where t.num=1 ), new_report2_tb as ( select compCode, compname, reportid, fiscalyear from( select *, row_number() over (partition by compCode order by fiscalyear desc) num from tempview1 ) t where t.num=2 ), new_report_ssf1_tb as ( select t1.*, t2.fiscalyear, t2.compname from tempview2 t1 join new_report1_tb t2 on t1.compCode = t2.compCode and t1.reportid = t2.reportid ), new_report_ssf2_tb as ( select t1.*, t2.fiscalyear, t2.compname from tempview2 t1 join new_report2_tb t2 on t1.compCode = t2.compCode and t1.reportid = t2.reportid ), join_tb as ( select t1.compCode,t2.compname, t1.fiscalyear, concat('100166','&#&',t1.endowmentInsure,'&#&','3') as eigenvalue,null as retrovalue from new_report_ssf1_tb t1 join new_report_ssf2_tb t2 on t1.compCode = t2.compCode where t1.endowmentInsure >= 200 and t2.endowmentInsure < 200 ) select * from join_tb ",
        "tempview1","select compCode, compname, reportid, fiscalyear from( select compCode, compname, reportid, fiscalyear, datastatus, sourceid, row_number() over (partition by sourceid order by filedate desc, modifyTime desc) num from seeyii_data_house.dwd_me_news_comp_annual_report ) t where t.num=1 and t.datastatus != 3 and sourceid is not null"),
        "8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000304005',
        " with source_dat AS ( select compname, compcode, concat(int(fiscalyear)+1, '-06-30') as eventdate, concat(int(fiscalyear)+1, '-12-31') as expiredate, eigenvalue from tempview3 ), des_dat as ( select compname as eventsubject, compcode as subjectcode, eventdate, CAST(null AS STRING) as url, 'SJ000304005' as eventtype, expiredate, '公司近1年参保人数增长50%。' as desc1, eigenvalue, '1' as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime, {fileDate} as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,eigenvalue,datastatus,filedate,modifytime ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select distinct eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate, expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue,null as retrovalue,datastatus,modifytime,filedate from ret_dat ",
        map("tempview2"," select compCode, reportid, regexp_extract(endowmentInsure, '[0-9]+', 0) as endowmentInsure from( select compCode, reportid, endowmentInsure, datastatus, row_number() over (partition by id order by filedate desc, modifyTime desc) num from seeyii_data_house.dwd_ms_base_comp_report_ssf ) t where t.num=1 and t.datastatus != 3 ",
        "tempview3"," with new_report1_tb as ( select compCode, compname, reportid, FISCALYEAR from( select *, row_number() over (partition by compCode order by fiscalyear desc) num from tempview1 ) t where t.num=1 ), new_report2_tb as ( select compCode, compname, reportid, FISCALYEAR from( select *, row_number() over (partition by compCode order by fiscalyear desc) num from tempview1 ) t where t.num=2 ), new_report_ssf1_tb as ( select t1.*, t2.fiscalyear from tempview2 t1 join new_report1_tb t2 on t1.compCode = t2.compCode and t1.reportid = t2.reportid ), new_report_ssf2_tb as ( select t1.*, t2.fiscalyear, t2.compname from tempview2 t1 join new_report2_tb t2 on t1.compCode = t2.compCode and t1.reportid = t2.reportid ), join_tb as ( select t1.compCode,t2.compname, t1.fiscalyear, concat('100167','&#&',(t1.endowmentInsure/t2.endowmentInsure - 1),'&#&','2') as eigenvalue,null as retrovalue from new_report_ssf1_tb t1 join new_report_ssf2_tb t2 on t1.compCode = t2.compCode where (t1.endowmentInsure/t2.endowmentInsure - 1) >= 0.5 ) select * from join_tb ",
        "tempview1"," select compCode, compname, reportid, fiscalyear from( select compCode, compname, reportid, fiscalyear, datastatus, sourceid, row_number() over (partition by sourceid order by filedate desc, modifyTime desc) num from seeyii_data_house.dwd_me_news_comp_annual_report ) t where t.num=1 and t.datastatus != 3 and sourceid is not null "),
        "8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000340002',
        " with gt90_tb AS ( SELECT DISTINCT t1.compname, t1.compcode, t1.startDate AS eventdate , sum(t2.employernum) OVER (PARTITION BY t1.compcode, t1.compname, t1.startDate ) AS employ_num FROM tempview1 t1 JOIN tempview1 t2 ON t1.compcode = t2.compcode WHERE t2.startDate > date_add(t1.startDate, -90) AND t2.startDate <= t1.startDate ), gt180_tb AS ( SELECT DISTINCT t1.compname, t1.compcode, t1.startDate AS eventdate , sum(t2.employernum) OVER (PARTITION BY t1.compcode, t1.compname, t1.startDate ) AS employ_num FROM tempview1 t1 JOIN tempview1 t2 ON t1.compcode = t2.compcode WHERE t2.startDate > date_add(t1.startDate, -180) AND t2.startDate <= date_add(t1.startDate, -90) ), source_dat as ( select t1.compname, t1.compcode, t1.eventdate, concat('100169','&#&',(t1.employ_num/t2.employ_num -1),'&#&','2') as eigenvalue from gt90_tb t1 join gt180_tb t2 on t1.compname = t2.compname and t1.compcode = t2.compcode and t1.eventdate = t2.eventdate where t1.employ_num/t2.employ_num > 2 ), des_dat as ( select compname as eventsubject, compcode as subjectcode, eventdate, CAST(null AS STRING) as url, 'SJ000340002' as eventtype, date_add(eventdate, 90) as expiredate, '公司近3个月职位总数对比上3个月增长100%。' as desc1,eigenvalue, '1' as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime, {fileDate} as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,eigenvalue,datastatus,filedate,modifytime ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat where expiredate >= current_date()) insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate, expiredate ,null as property1,null as property2,null as property3,null as property4 ,eigenvalue,null as retrovalue,datastatus,modifytime,filedate from ret_dat ",
        map("tempview1"," SELECT COMPNAME, COMPCODE, STARTDATE, EMPLOYERNUM FROM ( SELECT COMPNAME, COMPCODE, EDUCATION , REGEXP_EXTRACT(EMPLOYERNUMBER, '[0-9]+', 0) AS EMPLOYERNUM , ISVALID, DATASTATUS, DATE_FORMAT(STARTDATE, 'yyyy-MM-dd') AS STARTDATE , DATE_FORMAT(ENDDATE, 'yyyy-MM-dd') AS ENDDATE , DATE_ADD(DATE_FORMAT(STARTDATE, 'yyyy-MM-dd'), 90) AS EXPIREDATE , ROW_NUMBER() OVER (PARTITION BY ID ORDER BY FILEDATE DESC) AS NUM FROM seeyii_data_house.DWD_ME_BUSS_COMP_EMPLOY ) T WHERE T.NUM = 1 AND T.COMPCODE IS NOT NULL AND T.STARTDATE IS NOT NULL AND T.DATASTATUS != 3 AND T.ISVALID != 0 and STARTDATE >= DATE_ADD(current_date(), -270) "),
        "8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000304003',
        " with base_df AS ( select compcode as subjectcode,compname as eventsubject, concat('公司近1个月注册资本变更，增幅100%以上。注册资本变更时间：',changetime,'。') as desc1, changetime as eventdate, date_format(date_add(changetime, 30), 'yyyy-MM-dd') as expiredate, CAST(null AS STRING) as url,concat('100168','&#&',afchgcapital,'&#&','2') as eigenvalue from ( select compcode,compname,date_format(changetime, 'yyyy-MM-dd') as changetime,datastatus,bechgcapital,afchgcapital, row_number()over(partition by id order by filedate desc) as rnumber from seeyii_data_house.dwd_ms_base_comp_cap_chg ) as a where rnumber = 1 and datastatus != 3 and compcode is not null and changetime != '' and changetime is not null and afchgcapital/bechgcapital > 2 ) insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000304003' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000304003' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, eigenvalue,null as retrovalue, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from base_df ",
        map("tempview1", ""),
        "8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000320001',
        " with base_df AS ( select compcode as subjectcode, companyname as eventsubject, taketype, regexp_replace( tradingrivals, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r|。', '' ) as tradingrivals, regexp_replace( takeoverbid, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r|。', '' ) as takeoverbid, takeoverratio, price, isprt, if( (ccy is null) or (ccy = ''), '', ccy ) as ccy, date_format(enddate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, CAST(null AS STRING) as url, concat( concat( '100157', '&#&', if( price like '%.%', if( price REGEXP '^[0-9]+.[0-9]+$', price, '' ), if(price REGEXP '^[0-9]+$', price, '') ), '&#&', '3' ), '@@', concat( '100158', '&#&', if(taketype is null, '', taketype), '&#&', '3' ) ) as eigenvalue from seeyii_data_house.dwd_me_trad_nq_takeover where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_me_trad_nq_takeover ) and datastatus != 3 and enddate is not null and enddate != '' and compcode is not null ), next_df AS ( select *, if( (taketype is null) or (taketype = ''), '', concat('。类型为', taketype) ) as a, if( (tradingrivals is null) or (tradingrivals = ''), '', concat( '，交易对手为', tradingrivals ) ) as a0, if( (takeoverbid is null) or (takeoverbid = ''), '', concat( '，收购标的为', takeoverbid ) ) as a1, if( (takeoverratio is null) or (takeoverratio = ''), '', concat( '，收购股比为', takeoverratio ) ) as a2, if( (price is null) or (price = ''), '', concat( '，金额为', price, '万元', ccy ) ) as a3, if( (isprt is null) or (isprt = ''), '', concat( '，是否关联交易：', isprt ) ) as a4 from base_df ), final_df AS ( select *, concat( eventdate, '，三板公司发布收购信息', a, a0, a1, a2, a3, a4, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000320001', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000320001' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue,null as retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
        map("tempview1", ""),
        "8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       
       INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
       SELECT  'SJ000208003',
        " with subsist_list as ( select id, relieveDate as eventdate, ipType, tsSubType, ipName, invenName from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc ) AND datastatus != 3 ), sk_stock as ( select id, compcode, compname from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc_se where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc_se ) AND datastatus != 3 and typecode = 1 ), join_tb as ( select a.compcode as subjectcode, a.compname as eventsubject, b.eventdate, ipType, tsSubType, ipName, invenName, concat( '100162', '&#&', ipType, '&#&', '3' ) as eigenvalue from sk_stock as a join subsist_list as b on a.id = b.id ), next_df as ( select *, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司专利权质押合同解除登记' ) as a, if( (ipType is null) or (ipType = ''), '', concat('，专利类型为', ipType) ) as b, if( (tsSubType is null) or (tsSubType = ''), '', concat( '，细分事务分类为', tsSubType ) ) as c, if( (ipName is null) or (ipName = ''), '', concat('，专利名称为', ipName) ) as d, if( (eventsubject is null) or (eventsubject = ''), '', concat('，质权人为', eventsubject) ) as e, if( (invenName is null) or (invenName = ''), '', concat('，发明名称为', invenName) ) as f, '。' g from join_tb where eventdate is not NULL ), final_df as ( select *, concat_ws('', a, b, c, d, e, f, g) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000208003', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000208003' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue,null as retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
        map("tempview1", ""),
        "7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
       
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240328' )
    SELECT  'SJ000101001',
     " with source_df as ( SELECT tb2.subjectcode, tb1.eventdate, tb1.title, tb1.pt1, concat_ws( '&#&', '专利', 'sy_cd_me_buss_ip_patn_new', 'id', string(tb1.id) ) as retrovalue FROM ( select id, eventdate, title, case when pattype = '1' then '发明' when pattype = '2' then '实用新型' when pattype = '3' then '外观设计' when pattype = '4' then '短期专利' when pattype = '5' then '其它' when pattype = '6' then '译文' when pattype = '7' then '检索报告' when pattype = '8' then 'pct发明' when pattype = '9' then 'pct实用新型' else '' end as pt1 from ( select id, pattype, title, pubdate as eventdate, isvalid, datastatus, row_number() over ( partition by id order by filedate desc ) num from seeyii_emr_gs.dwd_me_buss_ip_patn ) t where t.num = 1 and t.eventdate >= '2021-01-01' AND t.datastatus != 3 AND t.isvalid = '1' ) AS tb1 JOIN ( select pkid, subjectcode from ( select pkid, compcode AS subjectcode, row_number() over ( partition by fingerid order by filedate desc ) num2 from seeyii_emr_gs.dwd_me_buss_ip_patn_se ) t where t.num2 = 1 and t.subjectcode is not null ) AS tb2 ON tb1.id = tb2.pkid ), des_dat as ( select subjectcode, eventdate, concat( concat('100021', '&#&', pt1, '&#&', '7') ) as eigenvalue, retrovalue, CAST(null AS STRING) as eventsubject, CAST(null AS STRING) as url, 'SJ000101001' as eventtype, CAST(null AS STRING) as expiredate, concat( '本单位于', eventdate, '新增专利', if( (pt1 is null) or (pt1 = ''), '', concat(',专利类型为', pt1) ), if( (title is null) or (title = ''), '', concat(',专利名称为', title) ), '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate from source_df ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, retrovalue, fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), if(eventtype is NULL, '#', eventtype), if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"9", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    