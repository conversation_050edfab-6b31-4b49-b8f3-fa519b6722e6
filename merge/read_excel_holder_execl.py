# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/11
from core.excel_base import ExcelBase
from moduler.company_holder import CompanyHolder


class Holder(ExcelBase):
    def __init__(self):
        super(Holder, self).__init__()
        self.holder = CompanyHolder()

    def process(self):
        result = list()
        items = self.read_excel_data(self._in_file_path + "/企业名单_212.xlsx", "明细")
        names = [i["优质企业"] for i in items]
        holder_dict = self.holder.run(names)
        for item in items:
            name = item["优质企业"]
            holder_list = holder_dict.get(name)
            if holder_list:
                item["gd"] = ",".join([i["shName"] for i in holder_list if len(i["shName"]) > 4])
            result.append(item)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            '优质企业': ('优质企业', 0),
            '企业介绍': ('企业介绍', 1),
            '行业': ('行业', 2),
            'gd': ('股东名称', 3)}
        self._excel_name = self.name_add_date("优质企业名单.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def read_excel_data(self, file_name, sheet="Sheet"):
        data_list = self._extract_data(file_name, sheet)
        return data_list


if __name__ == '__main__':
    p = Holder()
    p.process()
