# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2024/8/5
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Load the data
df = pd.read_csv('dwd_ms_mtc_bs_achieve1.csv')

# Get the unique indicationids
indication_ids = df['indicationid'].unique()

# Set up the plot style
sns.set(style="whitegrid")

for ind_id in indication_ids:
    # Filter data for the current indicationid
    data = df[df['indicationid'] == ind_id]

    # Create the histogram
    plt.figure(figsize=(10, 6))
    sns.histplot(data['score'], bins=30, kde=True)
    plt.title(f'Histogram of Scores for Indication ID {ind_id}')
    plt.xlabel('Score')
    plt.ylabel('Frequency')

    # Save the plot
    plt.savefig(f'{ind_id}.png')
    plt.close()

    print(f'Histogram for Indication ID {ind_id} saved.')

print('All histograms generated and saved.')