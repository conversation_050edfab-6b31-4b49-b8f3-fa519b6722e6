# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/21
from core.excel_base import ExcelBase
from moduler.company_area_info import CompanyArea
from moduler.company_in_category import InCategory


class CatAndAddr(ExcelBase):
    def __init__(self):
        super(CatAndAddr, self).__init__()
        self.cat = InCategory
        self.addr = CompanyArea

    def process(self, *args, **kwargs):
        result = list()
        name_dict = self.cat.run(["Com900001"])
        names = list(name_dict.keys())
        addr_dict = self.addr.run(names)
        for name, addr_info in addr_dict.items():
            city = addr_info.get("cityName")
            if city == "宁波市":
                result.append({"company_name": name})
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'company_name': ('公司名称', 0),
        }
        self._excel_name = self.name_add_date("宁波地区民营集团名录.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})


if __name__ == '__main__':
    p = CatAndAddr()
    p.process()
