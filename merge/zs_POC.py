# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/05/24
from core.excel_base import ExcelBase
from moduler.company_area_info import CompanyArea
from moduler.company_category import CompanyCategory
from moduler.company_cyl_and_industry import ChinaToExcel


class CW(ExcelBase):
    def __init__(self):
        super(CW, self).__init__()
        self.cat = CompanyCategory()
        self.area = CompanyArea()
        self.cyl = ChinaToExcel()

    def process(self):
        result = list()
        cyl_list = self.cyl.run(["家电产业链", "建材家具产业链"])
        cyl_dict = {i["company_name"]: {"second_industry_name": i.get("second_industry_name", "")} for i in cyl_list}
        cat_dict = self.cat.run(list(cyl_dict.keys()))
        names = {i["cname"] for i in cat_dict.values() if set(i["category"]) & {"ACompany", "ThirdCompany"}}
        area_dict = self.area.run(list(names))
        for name in names:
            tmp = dict()
            tmp["company_name"] = name
            tmp.update(cyl_dict.get(name, dict()))
            tmp.update(cat_dict.get(name, dict()))
            tmp.update(area_dict.get(name, dict()))
            self.data_process(tmp, name, result)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'company_name': ('上市公司名称', 0),
            'company_code': ('上市公司代码', 1),
            'provinceName': ('地区（省份）', 2),
            'cityName': ('地区（城市）', 3),
            'second_industry_name': ('细分行业', 4),
            'endDate': ('年份', 5),
            'totAsset': ('资产总额', 6),
            'bizInco': ('营业收入', 7),
            'totProfit': ('利润总额', 8)}
        self._excel_name = self.name_add_date("招行POC.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def data_process(self, tmp, name, result):
        yl_list = self.query_yl_data(name)

        yl_dict = {str(i["endDate"]): i for i in yl_list}
        fz_list = self.query_fz_data(name)
        fz_dict = {str(i["endDate"]): i for i in fz_list}
        for year in ["2015", "2016", "2017", "2018", "2019", "2020"]:
            new_dict = dict()
            new_dict.update(yl_dict.get(year, dict()))
            new_dict.update(fz_dict.get(year, dict()))
            if new_dict:
                new_dict.update(tmp)
                result.append(new_dict)

    def query_yl_data(self, name):
        result = dict()
        sql_statement = """
                 SELECT
         b.companyName,
         a.bizInco,
         a.totProfit,
         year(a.endDate) as endDate
        FROM
         sy_cd_ms_fin_sk_inc AS a
        JOIN sy_cd_ms_base_cn_stock b ON a.innerCode = b.innerCode
        WHERE
         a.reportType = 3
        AND a.dataStatus != 3
        AND b.dataStatus != 3
        AND a.endDate IN (
         "20201231",
         "20191231",
         "20181231",
         "20171231",
         "20161231",
         "20151231"
        )
        AND b.companyName = '{}'
        UNION ALL
         SELECT
          b.companyName,
          a.operatRevenue AS bizInco,
          a.totalProfit AS totProfit,
          year(a.endDate) as endDate
         FROM
          sy_cd_ms_fin_nq_inc AS a
         JOIN sy_cd_ms_base_cn_stock b ON a.companyCode = b.companyCode
         WHERE
          a.endDate IN (
           "2020-12-31",
           "2019-12-31",
           "2018-12-31",
           "2017-12-31",
           "2016-12-31",
           "2015-12-31"
          )
         AND a.ifMerged = 1
         AND a.ifAdjusted = 2
         AND a.infoSource = 5
         AND b.dataStatus != 3
         AND a.dataStatus != 3
         AND b.companyName = '{}'
        """.format(name, name)
        query_schema = dict(db_key="tidb_152", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result.setdefault(item["companyName"], item)
        return result

    def query_fz_data(self, name):
        result = dict()
        sql_statement = """
        SELECT
         b.companyName,
         a.totAsset,
         year(a.endDate) as endDate
        FROM
         sy_cd_ms_fin_sk_balsheet AS a
        JOIN sy_cd_ms_base_cn_stock b ON a.innerCode = b.innerCode
        WHERE
         a.reportType = 3
        AND a.dataStatus != 3
        AND b.dataStatus != 3
        AND a.endDate IN (
         "20201231",
         "20191231",
         "20181231",
         "20171231",
         "20161231",
         "20151231"
        )
        AND b.companyName='{}'
        UNION ALL
         SELECT
          b.companyName,
          a.totalAssets AS totAsset,
          year(a.endDate) as endDate
         FROM
          sy_cd_ms_fin_nq_balsheet AS a
         JOIN sy_cd_ms_base_cn_stock b ON a.companyCode = b.companyCode
         WHERE
          a.ifMerged = 1
         AND a.ifAdjusted = 2
         AND a.infoSource = 5
         AND b.dataStatus != 3
         AND a.dataStatus != 3
         AND a.endDate IN (
          "2020-12-31",
          "2019-12-31",
          "2018-12-31",
          "2017-12-31",
          "2016-12-31",
          "2015-12-31"
         )
         AND b.companyName ='{}'
        """.format(name, name)
        query_schema = dict(db_key="tidb_152", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result.setdefault(item["companyName"], item)
        return result


if __name__ == '__main__':
    p = CW()
    p.process()
