# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2023/2/15
# 先安装包
# pip install simple-ddl-parser
from pprint import pprint
import json
from simple_ddl_parser import DDLParser

OUT_TMP = {"parameter": {
    "column": list(), "writeMode": "update"}, "table": str(),
    "columnMapping": list()}


class SQLParser:
    def __init__(self, sql):
        self.sql = sql
        self.type_map = {
            "bigint": "Long",
            "tinyint": "Long",
            "string": "String",
            "int": "Long",
            "float": "String",
            "date": "DATE"
        }

    def process(self, parce_type=None):
        if parce_type == 1:
            self.in_process()
        elif parce_type == 2:
            self.out_process_2()
        elif parce_type == 3:
            self.field_process("a.")
        else:
            self.out_process()

    def field_process(self, default=""):
        result_dict = DDLParser(self.sql).run(output_mode="hql")[0]
        columns = result_dict["columns"]
        field_str = ""
        for idx, item in enumerate(columns):
            field_str += default
            name = item["name"].replace("`", "").lower()
            field_str += name
            if idx + 1 != len(columns):  # 最后一个不加逗号
                field_str += ","

        print(field_str)

    def out_process(self):
        """
        {"index":0,"name":"compcode","type":"Long"}
        :return:
        """
        result, fild_list = list(), list()
        result_dict = DDLParser(self.sql).run(output_mode="hql")[0]
        # print(result_dict, end='\n\n')
        columns = result_dict["columns"]
        for idx, item in enumerate(columns):
            name = item["name"].replace("`", "").lower()
            type = self.type_map.get(item["type"], "String")
            if name in {"filedate", "appnumber", "createtime", "id"}:  # 过滤字段
                continue
            result.append({
                "index": idx,
                "name": name,
                "type": type})
            fild_list.append(name)
        print(','.join(fild_list))
        print(len(fild_list))
        print(json.dumps(fild_list), end='\n\n')
        print(json.dumps(result))

    def in_process(self):
        result, filder_list = list(), list()
        result_dict = DDLParser(self.sql).run(output_mode="hql")[0]
        # print(result_dict, end='\n\n')
        columns = result_dict["columns"]
        for idx, item in enumerate(columns):
            name = item["name"].replace("`", "").lower()
            # if name in {"modifytime", "id", "createtime"}:  # 过滤字段
            #     continue
            result.append({"sourceColName": name, "dstColName": name})
            filder_list.append(name)
        print(json.dumps(filder_list), end='\n\n')
        print(json.dumps(result, indent=4))

    def out_process_2(self):
        result_dict = DDLParser(self.sql).run(output_mode="hql")[0]
        OUT_TMP["table"] = result_dict["table_name"].replace("dwd_", "sy_cd_").replace("dws_", "sy_cs_").replace("app_",
                                                                                                                 "sy_sa_").replace(
            "`", "")
        columns = result_dict["columns"]
        for idx, item in enumerate(columns):
            name = item["name"].replace("`", "").lower()
            # if name in ("filedate", "modifytime", "createtime","createdate","pkid"):
            #     continue
            OUT_TMP["parameter"]["column"].append(name)
            OUT_TMP["columnMapping"].append({"sourceColName": name, "dstColName": name})
        print("出仓字段数量：{}".format(len(OUT_TMP["parameter"]["column"])))
        print(json.dumps(OUT_TMP))


if __name__ == '__main__':
    sql = """

   
     """
    SQLParser(sql).process()  # 默认 1: 入仓；2: 出仓; 3: 全部字段

