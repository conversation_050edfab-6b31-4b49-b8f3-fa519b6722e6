# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022/11/29
import re
from copy import deepcopy
from collections import defaultdict

from core.excel_base import ExcelBase


class PX(ExcelBase):
    def __init__(self):
        super(PX, self).__init__()
        self.__character_partner = re.compile(u"[一-龥]")
        self.__ship_partner = re.compile(u"（(普通|有限)合伙(人?)）$")
        self.__letter_partner = re.compile("[a-zA-Z]")
        self.__person_pattern = re.compile("[-—－]")

    def process(self, *args, **kwargs):
        result = list()
        name_dict = defaultdict(list)
        name_list = self.read_excel_data(self._in_file_path + "/知名谱系.xlsx", "Sheet1")
        for item in name_list:
            name = item["名称"]
            name_dict[name].append(item)
        for n, h_list in self.d_holder_cmp(list(name_dict.keys())).items():
            if len(h_list) < 2:
                continue
            px_list = name_dict[n]
            for px_item in px_list:
                for h in h_list:
                    c_h = deepcopy(h)
                    c_h.update(px_item)
                    result.append(c_h)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            '谱系名称': ('谱系名称', 0),
            'name': ('公司名称', 1),
            'holder': ('股东', 2),
            'radio': ('持股比例', 3),
            '类型': ('类型', 4)
        }
        self._excel_name = self.name_add_date("谱系多个大股东明细.xlsx")
        self.save_to_excel(field_cfg, {"Sheet": result})

    def read_excel_data(self, file_name, sheet="Sheet"):
        data_list = self._extract_data(file_name, sheet)
        return data_list

    @staticmethod
    def max_holder(holder_list):
        # 大股东
        result = defaultdict(list)
        for item in holder_list:
            ratio = float(item.get("holdRatio")) or 0.0
            result[ratio].append(item)
        if not result:
            return []
        max_ratio = max(result)
        if max_ratio == 0 or not max_ratio:
            return []
        else:
            return result[max_ratio]

    def d_holder_cmp(self, names):
        # 大股东
        result = defaultdict(list)
        for i in names:
            holder_data = self.query_cmp_holder([i])
            holder_list = holder_data.get(i) or list()
            d_holder_list = self.max_holder(holder_list)  # 大股东数据列表
            for d_holder in d_holder_list:
                holder_name = d_holder["shareholder_name"]
                d_radio = float(d_holder.get("holdRatio")) or 0.0
                if self.get_company_type(holder_name) == 2 and d_radio < 0.25:
                    continue
                result[i].append({"name": i, "holder": holder_name, "radio": d_radio})
        return result

    def query_cmp_holder(self, cname_list):
        result = defaultdict(list)
        name_str = "','".join(cname_list)
        sql = """
        select compName as cname, shName as shareholder_name, holdRatio
        from sy_cd_ms_sh_best_stk_rati where 
        dataStatus != 3 and compName IN ('{}')""".format(name_str)
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql)
        result_list = self._data_server.call("query_sql_item", query_schema)
        for item in result_list:
            name = item["cname"]
            result[name].append(item)
        return result

    # 判断法定代表人类型  1：法人    2：自然人
    def get_company_type(self, raw_name):
        if self.__character_partner.search(raw_name):
            return 2 if self._is_person(raw_name) else 1
        return 2 if self._letter_filter(raw_name) is None else 1

    def _letter_filter(self, raw_name):
        """
        if the proportion of letter in string is greater than 0.9 and
        doesn't contain words
        {`LIMITED`, `LTD`, `GROUP`, `LP`, `LLC`, `INC`, `GmbH`} ,
        return None.
        """
        name_list = raw_name.lower().split(" ")
        total_len = len("".join(name_list))
        letter_len = len(self.__letter_partner.findall(raw_name))
        if 1.0 * letter_len / total_len < 0.9:
            return raw_name
        for word in name_list:
            if word in {"limited", "ltd", "group", "lp", "llc", "inc", "gmbh"}:
                return raw_name
        return None

    def _is_person(self, raw_name):
        name_str = self._name_parser(raw_name)
        name_str = re.sub(self.__ship_partner, u"", name_str)
        return len(name_str) <= 4

    def _name_parser(self, raw_name):
        name_fields = self.__person_pattern.split(raw_name)
        return name_fields[-1]


if __name__ == '__main__':
    p = PX()
    p.process()
