# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/7/28
# from pymysql import connect
# from pymongo import MongoClient
#
# MONGODB = {
#     "raw_data": {
#         'host': "***********",
#         'port': 30000,
#         "username": "raw_db_u",
#         "password": "shiye1805A"
#     },
#     "bond_crawl_data": {
#         'host': "*************",
#         'port': 30000,
#         "username": "bond_crawl_db_u",
#         "password": "shiye1805A"
#     },
#     "kf3_sy_project_raw": {
#         "host": "*************",
#         "port": 3306,
#         "username": "shiye_KF2",
#         "password": "shiye1805A",
#         "database": "sy_project_raw"
#     },
# }
#
#
# class MongodbEngine(object):
#     def __init__(self, database):
#         db_config = MONGODB[database]
#
#         self.connection = MongoClient(host=db_config["host"], port=db_config["port"])
#         self.connection[database].authenticate(db_config["username"], db_config["password"])
#
#     def __enter__(self):
#         return self
#
#     def __exit__(self, exc_type, exc_val, exc_tb):
#         self.connection.close()
#
#
# class MysqlConnectEngine(object):
#     def __init__(self, database):
#         self.connection = None
#         self.cursor = None
#         self.database = database
#
#     def __enter__(self):
#         db_config = MONGODB[self.database]
#         self.connection = connect(
#             host=db_config['host'],
#             port=db_config['port'],
#             user=db_config['username'],
#             password=db_config['password'],
#             database=db_config['database'],
#             charset="utf8"
#         )
#         self.cursor = self.connection.cursor()
#         return self.connection, self.cursor
#
#     def __exit__(self, exc_type, exc_val, exc_tb):
#         self.cursor.close()
#         self.connection.close()
from core.excel_base import ExcelBase
import re


class UpdateDate(ExcelBase):
    def __init__(self):
        super(UpdateDate, self).__init__()
        self.dwd_me_xzjg_csgq = {
            '北京产权交易所': 'equity_transfer_formal_sa_bj', '上海联合产权交易所': 'equity_transfer_formal_sa_sh',
            '天津产权交易中心': 'equity_transfer_formal_sa_tj', '山东产权交易中心': 'chanquan_transfer_sd',
            '海南产权交易所': 'chanquan_transfer_hncq', '广州产权交易所': 'chanquan_transfer_gzcq',
            '江西省产权交易所': 'chanquan_transfer_jxcq', '黑龙江联合产权交易所': 'chanquan_transfer_hlj',
            '山西省产权交易市场': 'chanquan_transfer_sx', '湖南省联合产权交易所': 'chanquan_transfer_hunan',
            '武汉光谷联合产权交易所': 'chanquan_transfer_wh', '内蒙古产权交易市场': 'chanquan_transfer_nmgcqjy',
            '大连产权交易所': 'chanquan_transfer_dalian', '河南省产权交易中心': 'chanquan_transfer_hn',
            '广东联合产权交易中心': 'chanquan_transfer_gduaee', '贵州阳光产权交易所': 'chanquan_transfer_gz',
            '江苏省产权交易所': 'chanquan_transfer_js', '西南联合产权交易所': 'chanquan_transfer_swuee',
            '安徽省产权交易中心': 'chanquan_transfer_ah', '云南产权交易所': 'chanquan_transfer_yn',
            '沈阳联合产权交易所': 'chanquan_transfer_sy', '浙江产权交易所': 'chanquan_transfer_zjpse',
            '西部产权交易所': 'chanquan_transfer_xbcq', '甘肃省产权交易所': 'chanquan_transfer_gscq',
            '河北产权市场': 'chanquan_transfer_hebaee', '北部湾产权交易所集团': 'chanquan_transfer_bbwcq',
            '南方联合产权交易中心': 'chanquan_transfer_csuaee', '深圳联合产权交易所': 'chanquan_transfer_sotcbb'}
        self.dwd_me_xzjg_rzxztd = {
            "包头市自然资源和规划局": "idlela_letters_baotou",
            "巴彦淖尔市自然资源和规划局": "idlela_letters_bynr",
            "沧州市自然资源和规划局": "idlela_letters_cangzhou",
            "长治市自然资源和规划局": "idlela_letters_changzhi",
            "承德市自然资源和规划局": "idlela_letters_chengde",
            "赤峰市自然资源和规划局": "idlela_letters_chifeng",
            "邯郸市自然资源和规划局": "idlela_letters_hd",
            "河北省自然资源厅": "idlela_letters_hebei",
            "黑龙江省自然资源厅": "idlela_letters_hljlr",
            "呼和浩特市自然资源和规划局": "idlela_letters_huhhot",
            "晋城市自然资源和规划局": "idlela_letters_jcgov",
            "吉林省自然资源厅": "idlela_letters_jl",
            "吉林市自然资源和规划局": "idlela_letters_jlcity",
            "廊坊市自然资源和规划局": "idlela_letters_lf",
            "临汾市自然资源和规划局": "idlela_letters_linfen",
            "牡丹江市自然资源和规划局": "idlela_letters_mdj",
            "秦皇岛市自然资源和规划局": "idlela_letters_qhd",
            "山西省自然资源厅": "idlela_letters_shanxi",
            "石家庄市自然资源和规划局": "idlela_letters_sjz",
            "晋中市自然资源和规划局": "idlela_letters_sxjz",
            "太原市自然资源和规划局": "idlela_letters_taiyuan",
            "唐山市自然资源和规划局": "idlela_letters_tangshan",
            "天津市规划和自然资源和规划局": "idlela_letters_tj",
            "通化市自然资源和规划局": "idlela_letters_tonghua",
            "通辽市自然资源和规划局": "idlela_letters_tongliao",
            "乌海市自然资源和规划局": "idlela_letters_wuhai",
            "百色市自然资源和规划局": "idlela_letters_bs",
            "来宾市自然资源和规划局": "idlela_letters_lb",
            "梧州市自然资源和规划局": "idlela_letters_wz",
            "贵港市自然资源和规划局": "idlela_letters_gg",
            "玉林市自然资源和规划局": "idlela_letters_yl",
            "崇左市自然资源和规划局": "idlela_letters_cz",
            "钦州市自然资源和规划局": "idlela_letters_qzlr",
            "杭州市自然资源和规划局": "idlela_letters_hangzhou",
            "湖州市自然资源和规划局": "idlela_letters_huzhou",
            "绍兴市自然资源和规划局": "idlela_letters_sx",
            "舟山市自然资源和规划局": "idlela_letters_zhoushan",
            "金华市自然资源和规划局": "idlela_letters_jh",
            "合肥市自然资源和规划局": "idlela_letters_hefei",
            "淮北市自然资源和规划局": "idlela_letters_huaibei",
            "蚌埠市自然资源和规划局": "idlela_letters_bengbu",
            "淮南市自然资源和规划局": "idlela_letters_huainan",
            "铜陵市自然资源和规划局": "idlela_letters_tl",
            "池州市自然资源和规划局": "idlela_letters_chizhou",
            "安庆市自然资源和规划局": "idlela_letters_anqing",
            "莆田市自然资源和规划局": "idlela_letters_putian",
            "龙岩市自然资源和规划局": "idlela_letters_longyan",
            "漳州市自然资源和规划局": "idlela_letters_zhangzhou",
            "厦门市自然资源和规划局": "idlela_letters_xm",
            "江西省自然资源厅": "idlela_letters_jiangxi",
            "南昌市自然资源和规划局": "idlela_letters_nc",
            "鹰潭市自然资源和规划局": "idlela_letters_yingtan",
            "萍乡市自然资源和规划局": "idlela_letters_pingxiang",
            "山东省自然资源厅": "idlela_letters_shandong",
            "防城港市自然资源和规划局": "idlela_letters_fcg",
            "北海市自然资源和规划局": "idlela_letters_bh",
            "海南省自然资源厅": "idlela_letters_lr",
            "三亚市自然资源和规划局": "idlela_letters_zgj",
            "广元市自然资源和规划局": "idlela_letters_zrzy",
            "南阳市自然资源和规划局": "idlela_letters_ny",
            "怀化市自然资源和规划局": "idlela_letters_huaihua",
            "天门市自然资源和规划局": "idlela_letters_tm",
            "随州市自然资源和规划局": "idlela_letters_suizhou",
            "荆门市自然资源和规划局": "idlela_letters_jingmen",
            "孝感市自然资源和规划局": "idlela_letters_xiaogan",
            "黄冈市自然资源和规划局": "idlela_letters_hg",
            "黄石市自然资源和规划局": "idlela_letters_hs",
            "仙桃市自然资源和规划局": "idlela_letters_xiantao",
            "漯河市自然资源和规划局": "idlela_letters_lhgtj",
            "长沙市自然资源和规划局": "idlela_letters_changsha",
            "岳阳市自然资源和规划局": "idlela_letters_yueyang",
            "驻马店市自然资源和规划局": "idlela_letters_zmd",
            "信阳市自然资源和规划局": "idlela_letters_xygt",
            "常州市自然资源和规划局": "idlela_letters_changzhou",
            "成都市自然资源和规划局": "idlela_letters_chengdu",
            "巴中市自然资源和规划局": "idlela_letters_cnbz",
            "丹东市自然资源和规划局": "idlela_letters_dandong",
            "贵州省自然资源厅": "idlela_letters_guizhou",
            "淮安市自然资源和规划局": "idlela_letters_huaian",
            "焦作市自然资源和规划局": "idlela_letters_jzgtzy",
            "开封市自然资源和规划局": "idlela_letters_kfgtfcj",
            "昆明市自然资源和规划局": "idlela_letters_km",
            "林芝市自然资源和规划局": "idlela_letters_linzhi",
            "洛阳市自然资源和规划局": "idlela_letters_lyblr",
            "连云港市自然资源和规划局": "idlela_letters_lyg",
            "南通市自然资源和规划局": "idlela_letters_nantong",
            "攀枝花市自然资源和规划局": "idlela_letters_panzhihua",
            "濮阳市自然资源和规划局": "idlela_letters_pyblr",
            "宿迁市自然资源和规划局": "idlela_letters_suqian",
            "苏州市自然资源和规划局": "idlela_letters_suzhou",
            "泰州市自然资源和规划局": "idlela_letters_taizhou",
            "铜川市自然资源和规划局": "idlela_letters_tongchuan",
            "无锡市自然资源和规划局": "idlela_letters_wuxi",
            "徐州市自然资源和规划局": "idlela_letters_xuzhou",
            "新乡市自然资源和规划局": "idlela_letters_xxblr",
            "盐城市自然资源和规划局": "idlela_letters_yancheng",
            "扬州市自然资源和规划局": "idlela_letters_yangzhou",
            "自贡市自然资源和规划局": "idlela_letters_zg",
            "郑州市自然资源和规划局": "idlela_letters_zhengzhou",
            "镇江市自然资源和规划局": "idlela_letters_zhenjiang",
            "资阳市自然资源和规划局": "idlela_letters_ziyang",
            "广东省自然资源厅": "idlela_letters_gd",
            "广州市自然资源和规划局": "idlela_letters_gzlpc",
            "韶关市自然资源和规划局": "idlela_letters_sg",
            "梅州市自然资源和规划局": "idlela_letters_mzgtzy",
            "清远市自然资源和规划局": "idlela_letters_qylr",
            "揭阳市自然资源和规划局": "idlela_letters_jieyang",
            "肇庆市自然资源和规划局": "idlela_letters_zhaoqing",
            "佛山市自然资源和规划局": "idlela_letters_foshan",
            "南宁市自然资源和规划局": "idlela_letters_nanning",
            "桂林市自然资源和规划局": "idlela_letters_gl",
            "河池市自然资源和规划局": "idlela_letters_hc",
            "贺州市自然资源和规划局": "idlela_letters_hz",
            "柳州市自然资源和规划局": "idlela_letters_lz",
            "德州市自然资源和规划局": "idlela_letters_dezhou",
            "烟台市自然资源和规划局": "idlela_letters_yantai",
            "威海市自然资源和规划局": "idlela_letters_weihai",
            "淄博市自然资源和规划局": "idlela_letters_zibo",
            "潍坊市自然资源和规划局": "idlela_letters_wf",
            "寿光市自然资源和规划局": "idlela_letters_shouguang",
            "青岛市自然资源和规划局": "idlela_letters_qingdao",
            "日照市自然资源和规划局": "idlela_letters_rizhao",
            "枣庄市自然资源和规划局": "idlela_letters_zz",
            "河南省自然资源厅": "idlela_letters_henan"}
        self.raw_list = ["equity_transfer_formal_sa_bj", "equity_transfer_formal_sa_sh",
                         "equity_transfer_formal_sa_tj", ]

    def process(self):
        self.query_dwd_me_xzjg_rzxztd()
        self.query_dwd_me_xzjg_csgq()

    def query_dwd_me_xzjg_csgq(self):
        sql_statement = """SELECT dupId, srcUrl, exchange, title from dwd_me_xzjg_csgq where dataStatus !=3 ;"""
        query_schema = dict(db_key="kf3_sy_project_raw", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            exchange = item.get("exchange")
            url = item.get("srcUrl")
            title = item.get("title")
            _id = item.get("dupId")
            if title != "出售股权":
                continue
            if exchange and url:
                temp_data = self.query_mongo(self.dwd_me_xzjg_csgq[exchange], url)
                if not temp_data: continue
                raw_title = temp_data[0].get("title")
                if not raw_title: continue
                print(raw_title)
                self.update_mysql("dwd_me_xzjg_csgq", raw_title, _id)

    def update_mysql(self, table, title, dupId):
        sql_statement = """UPDATE {table} SET title='{title}'  where dupId='{dupId}';""".format(
            **{"table": table, "title": title, "dupId": dupId})
        # print(sql_statement)
        # import pdb; pdb.set_trace()
        query_schema = dict(db_key="kf3_sy_project_raw", sql_statement=sql_statement)
        self._data_server.call("execute_sql_item", query_schema)

    def query_dwd_me_xzjg_rzxztd(self):
        coll_list = list(self.dwd_me_xzjg_rzxztd.values())
        sql_statement = """SELECT dupId, srcUrl, title from dwd_me_xzjg_rzxztd where dataStatus !=3 ;"""
        query_schema = dict(db_key="kf3_sy_project_raw", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            url = item.get("srcUrl")
            title = item.get("title")
            _id = item.get("dupId")
            if "认定为闲置土地" not in title:
                continue
            if not url:
                continue
            for coll in coll_list:
                temp_data = self.query_mongo(coll, url)
                if not temp_data: continue
                raw_title = temp_data[0].get("letters_abs")
                if not raw_title: continue
                print(coll, raw_title)
                self.update_mysql("dwd_me_xzjg_rzxztd", raw_title, _id)

    def query_mongo(self, coll, url):
        db_name = "bond_crawl_data"
        if coll in self.raw_list:
            db_name = "raw_data"
        query_schema = {
            "db_name": db_name,
            "collection_name": coll,
            "query_condition": {"url": url},
            "query_field": {"aa": 1, "_id": 1, "letters_abs": 1, "title": 1}}
        query_result = self._data_server.call("query_item", query_schema) or list()
        return query_result


if __name__ == '__main__':
    p = UpdateDate()
    p.process()
