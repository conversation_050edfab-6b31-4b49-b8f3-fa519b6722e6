# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022-9-19
from copy import deepcopy
from core.excel_base import ExcelBase
from moduler.company_holder import CompanyHolder


class PX(ExcelBase):
    def __init__(self):
        super(PX, self).__init__()

    def process(self, *args, **kwargs):
        result = dict()
        raw_org = self.query_org_raw_data()
        print(len(raw_org))
        raw_fund = self.query_fund_raw_data()
        print(len(raw_fund))
        org_dict, fund_dict = dict(), dict()
        px_name = self.__gen_spt_info()
        for item in self.query_org_data():
            name = item["cName"]
            px_id = item["spectrumId"]
            item["px_name"] = px_name[px_id]
            if name in raw_org:
                item["type"] = "备案"
            else:
                item["type"] = "未备案"
            org_dict.setdefault(name, list())
            org_dict[name].append(item)

        for item in self.query_fund_data():
            px_id = item["spectrumId"]
            item["px_name"] = px_name[px_id]
            name = item["cName"]
            if name in raw_fund:
                item["type"] = "备案"
            else:
                item["type"] = "未备案"
            fund_dict.setdefault(name, list())
            fund_dict[name].append(item)

        org_holder = CompanyHolder.run(list(org_dict.keys()))
        fund_holder = CompanyHolder.run(list(fund_dict.keys()))
        for org, org_item_list in org_dict.items():
            org_holder_list = org_holder.get(org)
            result.setdefault("机构", list())
            if not org_holder_list:
                result["机构"].extend(org_item_list)
            else:
                for holder in org_holder_list:
                    for it in org_item_list:
                        h = deepcopy(holder)
                        h.update(it)
                        result["机构"].append(h)

        for fund, fund_item_list in fund_dict.items():
            fund_holder_list = fund_holder.get(fund)
            result.setdefault("基金", list())
            if not fund_holder_list:
                result["基金"].extend(fund_item_list)
            else:
                for holder in fund_holder_list:
                    for it in fund_item_list:
                        h = deepcopy(holder)
                        h.update(it)
                        result["基金"].append(h)
        self.save_data_excel(result)

    def __gen_spt_info(self):
        result = dict()
        query_condition = dict()
        offset = None
        while True:
            if offset:
                query_condition["_id"] = {"$gt": offset}
            query_item = {
                "db_name": "raw_data",
                "collection_name": "investor_spectrum_info_v2",
                "query_condition": query_condition,
                "query_field": {
                    '_id': 1, "feature_id": 1, "spectrum_name": 1, "keyword_1": 1},
                "sort_field": [("_id", 1)],
                "limit_n": 1000}
            query_result = self._data_server.call("query_item", query_item)
            if not query_result:
                break
            for item in query_result:
                result[item["feature_id"]] = item["spectrum_name"]
            offset = query_result[-1]["_id"]
        return result

    def save_data_excel(self, result):
        field_cfg = {
            'px_name': ('谱系名称', 0),
            'cName': ('名称', 1),
            'legalRepresent': ('法定代表人', 2),
            'shareholder_name': ('股东', 3),
            'holdRatio': ('持股比例', 4),
            'type': ("是否备案", 5)
        }
        self._excel_name = self.name_add_date("谱系股东测试数据.xlsx")
        self.save_to_excel(field_cfg, result)

    def query_org_raw_data(self):
        sql = """
        select cName from sy_cd_ms_base_pe_equ_info
        """
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return {i["cName"] for i in result_list}

    def query_fund_raw_data(self):
        sql = """
        select fundName  from sy_cd_mm_cn_smjj_equ_info
        """
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return {i["fundName"] for i in result_list}

    def query_org_data(self):
        sql = """
        select cName, legalRepresent, spectrumId from dwd_ms_cn_px_invo_info
        """
        query_schema = dict(db_key="section_data_63306", sql_statement=sql)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list

    def query_fund_data(self):
        sql = """
            select cName, legalRepresent, spectrumId from dwd_ms_cn_px_pdt_info
            """
        query_schema = dict(db_key="section_data_63306", sql_statement=sql)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    p = PX()
    p.process()
