# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2023/1/12
import re

from cfg.config import out_file_path
from core.excel_base import ExcelBase
from utils.datetime_util import DatetimeUtil


class GenDat(ExcelBase):
    def __init__(self):
        super(GenDat, self).__init__()
        self._date = DatetimeUtil()
        self.is_inc = "1"  # 0存量 1增量
        self.db_name = 'seeyii_db'  # 数据库 key
        self.table_name = 'dws_me_trad_ipo_base'  # 查询的表名

    def process(self):
        date_t_1, cur_time = self.get_cur_date()
        filename = self.gen_file_name(self.is_inc, cur_time)
        self.field_list = self.get_field_list()
        print("field_list = {}".format(",".join(self.field_list)))
        data_list = self.query_mysql_data(self.is_inc)
        print(out_file_path + "/" + filename)
        self.white_out_data(data_list, out_file_path + "/" + filename)

    def get_cur_date(self):
        cur_date = self._date.get_datetime_now()
        # T-1
        up_date = self._date.get_date_by_delta(cur_date, -1)
        date_t_1 = self._date.date_to_str(up_date, str_format="%Y%m%d")
        # cur
        cur_time = self._date.date_to_str(cur_date, str_format="%Y%m%d")
        return date_t_1, cur_time

    def gen_file_name(self, is_inc, cur_time):
        table = "{}_{}{}.dat"
        return table.format(self.table_name, cur_time, is_inc)

    def get_field_list(self):
        result = list()
        sql_statement = """
        SELECT
        TABLE_NAME 表名,
        TABLE_NAME 表名,
        ORDINAL_POSITION 序号,
        COLUMN_NAME 列名,
        COLUMN_COMMENT 备注,
        COLUMN_TYPE 数据类型,
        (case
        when IS_NULLABLE = 'YES' then 'Y'
        when IS_NULLABLE = 'NO' then 'N'
        end
        ) as 是否为空,
        (case
        when COLUMN_KEY = 'PRI' then 'Y'
        end
        ) as 是否主键
        FROM
        INFORMATION_SCHEMA.COLUMNS
        WHERE
        TABLE_NAME = '{}'
        """.format(self.table_name)
        query_schema = dict(db_key=self.db_name, sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            name = item["列名"]
            result.append(name)
        return result

    def query_mysql_data(self, is_inc):
        result = list()
        if is_inc == "1":
            sql_statement = """SELECT * from {} limit 1000""".format(self.table_name)
            query_schema = dict(db_key=self.db_name, sql_statement=sql_statement)
            result_list = self._data_server.call("query_sql_item", query_schema) or list()
            for item in result_list:
                _str = ""
                for field in self.field_list:
                    val = item.get(field)
                    if isinstance(val, str):
                        val = re.sub(r'([^\S\n]*\n(?:[^\S\n]*\n)+[^\S\n]*)|[^\S\n]*\n[^\S\n]*', "", val)
                    if val:
                        _str += str(val) + "|@|"
                    else:
                        _str += "|@|"
                result.append(_str)
        else:
            sql = """SELECT * FROM %s WHERE id>'{}' order by id ASC limit 1000;""" % self.table_name
            for result_list in self._query_sql_iter_by_id(sql, self.db_name):
                for item in result_list:
                    _str = ""
                    for field in self.field_list:
                        if field == "downTime":
                            if item.get(field):
                                val = item.get(field).date()
                                cur_date = self._date.get_today_base()
                                if val == cur_date:
                                    break
                        val = item.get(field)
                        if isinstance(val, str):
                            val = re.sub(r'([^\S\n]*\n(?:[^\S\n]*\n)+[^\S\n]*)|[^\S\n]*\n[^\S\n]*', "", val)
                        if val:
                            _str += str(val) + "|@|"
                        else:
                            _str += "|@|"
                    result.append(_str)
        return result

    @staticmethod
    def white_out_data(item_list, filename="无标题.dat"):
        """
        一位数组，item_list中的每个元素为字符串
        """
        if item_list:
            with open(filename, 'w', encoding="utf-8") as fw:
                for item in item_list:
                    fw.write(f'{item}\n')
        else:
            with open(filename, 'w', encoding="utf-8") as fw:
                fw.write("")


if __name__ == '__main__':
    p = GenDat()
    p.process()
