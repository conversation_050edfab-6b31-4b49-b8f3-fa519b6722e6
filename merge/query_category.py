# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/11
from core.excel_base import ExcelBase


class Cat(ExcelBase):
    def __init__(self):
        super(Cat, self).__init__()

    def process(self):
        for sheet, cat in {"高校直属投资企业": "UniversityCompany"}.items():
            result = self.query_company_category(cat)
            self.save_data_excel(result, sheet)

    def save_data_excel(self, result, excel):
        field_cfg = {
            'cname': ('公司名称', 0)}
        self._excel_name = self.name_add_date("{}.xlsx".format(excel))
        self.save_to_excel(field_cfg, {"Sheet": result})

    def query_company_category(self, cat):
        query_schema = {
            "db_name": "category_data",
            "collection_name": "company_category",
            "query_condition": {"category": cat, "is_valid": True},
            "query_field": {"_id": 0, "cname": 1}}
        query_result = self._data_server.call("query_item", query_schema) or list()
        return query_result

    def query_school_data(self):
        name_list = list()
        for table in ["ods_artif_higher_educ_school", "ods_artif_adult_higher_educ_school"]:
            query_schema = {
                "db_name": "raw_data",
                "collection_name": table,
                "query_condition": {},
                "query_field": {"_id": 0, "school_name": 1}}
            query_result = self._data_server.call("query_item", query_schema)
            for item in query_result:
                school_name = item["school_name"]
                name_list.append(school_name.replace('(', '（').replace(')', '）').strip())
        return "({})".format("|".join(name_list))


if __name__ == '__main__':
    p = Cat()
    # p.run()
    print(p.query_school_data())
