# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/7/21
"""
1、核心企业（200强、上市公司、央级单位、省属单位）
2、兄弟企业，即核心企业的大股东 所投资（穿透1层，且该股东是大股东）的企业，作为一个交付物excel的一个sheet，字段有“核心企业名称、兄弟企业名称、穿透链条”
//大股东是自然人时，不再查找该公司的兄弟企业
3、核心企业，向上穿透，找出大股东（无限层穿透、每层找出当前层级大股东），作为一个交付物excel的一个sheet，字段有“核心企业名称、股东名称、穿透链条”
4、核心企业+兄弟企业，向下穿透，找出子公司（穿透3层、且该股东是大股东）的企业，作为一个交付物excel的一个sheet，字段有“核心/兄弟企业名称、投资企业、穿透链条”

备注1：大股东为持有当前公司股份所占比例最大的股东，可能存在多个；
备注2：若所有股东持股比例均为0，则不存在大股东。

核心企业测试名单如下
200强：大汉控股集团有限公司、湖南琴岛文化传播有限公司
上市公司：爱尔眼科医院集团股份有限公司、湖南国科微电子股份有限公司
在湘央企：中铁城建集团有限公司
省属国企：湖南湘投控股集团有限公司
"""
from copy import deepcopy
from collections import defaultdict
from core.excel_base import ExcelBase
from moduler.company_a_and_xsb_holder import CompanyAandXsbShareholder
from moduler.company_holder import CompanyHolder
from moduler.company_invester import CompanyInvest


class CSPOC(ExcelBase):
    def __init__(self):
        super(CSPOC, self).__init__()
        self.a_xsb_holder = CompanyAandXsbShareholder()
        self.inv_cmp = CompanyInvest()
        self.gs_holder = CompanyHolder()
        self.a_xsb_names = {i["cname"] for i in self.query_company_category()}

    def process(self):
        names = [
            "大汉控股集团有限公司", "湖南琴岛文化传播有限公司",
            "爱尔眼科医院集团股份有限公司", "湖南国科微电子股份有限公司",
            "中铁城建集团有限公司",
            "湖南湘投控股集团有限公司"
        ]
        d_holder_cmp = self.d_holder_cmp_1(names)
        xd_cmp_items = self.xd_cmp(d_holder_cmp, names)
        self.save_data_xd(xd_cmp_items)

    def save_data_xd(self, result):
        field_cfg = {
            'name': ('核心企业名称', 0),
            'inv_name': ('兄弟企业名称', 1),
            'path': ('穿透链条', 2)}
        self._excel_name = self.name_add_date("兄弟企业.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def d_holder_cmp_1(self, names):
        # 大股东的大股东
        result = defaultdict(list)
        for i in names:
            if i in self.a_xsb_names:
                holder_data = self.a_xsb_holder.run([i])
            else:
                holder_data = self.gs_holder.run([i])
            holder_list = holder_data.get(i) or list()
            d_holder_list = self.max_holder(holder_list)  # 大股东数据
            for d_holder in d_holder_list:
                holder_name = d_holder["shareholder_name"]
                if len(holder_name) < 4:  # 大股东是自然人时，不再查找该公司的兄弟企业
                    continue
                d_radio = str(round(d_holder["holdRatio"], 2) * 100)
                result[holder_name].append(
                    {"name": i, "holder": holder_name, "radio": d_radio,
                     "path": f"{holder_name}--{d_radio}%-->{i}"})
            if i == "中铁城建集团有限公司":
                result["中国铁道建筑集团有限公司"].append(
                    {"name": i, "holder": "中国铁道建筑集团有限公司", "radio": 0.5113,
                     "path": "中国铁道建筑集团有限公司--51.13%-->中国铁建股份有限公司--100.00%-->中铁城建集团有限公司"})
        return result

    def d_holder_cmp(self, names):
        # 大股东
        result = defaultdict(list)
        for i in names:
            if i in self.a_xsb_names:
                holder_data = self.a_xsb_holder.run([i])
            else:
                holder_data = self.gs_holder.run([i])
            holder_list = holder_data.get(i) or list()
            d_holder_list = self.max_holder(holder_list)  # 大股东数据
            for d_holder in d_holder_list:
                holder_name = d_holder["shareholder_name"]
                if len(holder_name) < 4:  # 大股东是自然人时，不再查找该公司的兄弟企业
                    continue
                d_radio = str(round(d_holder["holdRatio"], 2) * 100)
                result[holder_name].append(
                    {"name": i, "holder": holder_name, "radio": d_radio,
                     "path": f"{holder_name}--{d_radio}%-->{i}"})
        return result

    def xd_cmp(self, item_dict, names):
        # 兄弟企业
        result = list()
        for name, d_holder_info in item_dict.items():
            tmp_result = self.inv_and_max_holder(name, d_holder_info, names)
            result.extend(tmp_result)
        return result

    def inv_and_max_holder(self, name, d_holder_info, names):
        result = list()
        inv_dict = self.inv_cmp.run([name])
        inv_names = {i["inv_name"] for i in inv_dict.get(name) or list()}
        inv_holder_dict = self.d_holder_cmp(list(inv_names))
        for inv_item in inv_holder_dict.get(name) or list():
            inv_name = inv_item["name"]
            radio = inv_item["radio"]
            for holder_item in d_holder_info:
                new_holder_item = deepcopy(holder_item)
                new_holder_item["inv_name"] = inv_name
                if inv_name in names:
                    continue
                new_holder_item["path"] = f"{inv_name}<--{radio}%--" + holder_item["path"]
                result.append(new_holder_item)
        return result

    @staticmethod
    def max_holder(holder_list):
        # 大股东
        result = defaultdict(list)
        for item in holder_list:
            ratio = float(item.get("holdRatio")) or 0.0
            result[ratio].append(item)
        max_ratio = max(result)
        if max_ratio == 0 or not max_ratio:
            return []
        else:
            return result[max_ratio]

    def query_company_category(self):
        query_schema = {
            "db_name": "category_data",
            "collection_name": "company_category",
            "query_condition": {"category": {"$in": ["ThirdCompany", "ACompany"]}, "is_valid": True},
            "query_field": {"cname": 1}}
        query_result = self._data_server.call("query_item", query_schema) or list()
        return query_result


if __name__ == '__main__':
    p = CSPOC()
    p.process()
