# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/05/08
from pymongo import MongoClient

MONGODB = {
    "raw_data": {
        'host': "***********",
        'port': 30000,
        "username": "raw_db_u",
        "password": "shiye1805A",
        "db": "raw_data"
    },
    "raw_data_new": {
        'host': "***********",
        'port': 30000,
        "username": "raw_db_u",
        "password": "shiye1805A",
        "db": "raw_data"
    },
    "base_data": {
        'host': "***********",
        'port': 30000,
        "username": "base_db_u",
        "password": "shiye1805A",
        "db": "base_data"
    },
    "base_data_new": {
        'host': "***********",
        'port': 30000,
        "username": "base_db_u",
        "password": "shiye1805A",
        "db": "base_data"
    },
}


class MongodbEngine(object):
    __ADMIN_USER = "db_server_root_admin"
    __ADMIN_PWD = "db_server_root_admin_shiye1805A"

    def __init__(self, database):
        db_config = MONGODB[database]

        self.connection = MongoClient(host=db_config["host"], port=db_config["port"])
        self.connection[db_config["db"]].authenticate(db_config["username"], db_config["password"])
        db_admin = self.connection.get_database("admin")
        self.db = self.connection.get_database(db_config["db"])
        db_admin.authenticate(self.__ADMIN_USER, self.__ADMIN_PWD)
        self.db.authenticate(db_config["username"], db_config["password"])

    def __enter__(self):
        return self.db

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.connection.close()


def query_data():
    s_db = "base_data"
    sour_dict = dict()
    with MongodbEngine(s_db) as db:
        collections = db.list_collection_names()
        # collections = ["company_base_info_v2"]
        for collection_name in collections:
            source_collection = db.get_collection(collection_name)
            indexes = source_collection.list_indexes()
            # print("Indexes for", collection_name + ":")
            sour_dict.setdefault(collection_name, list())
            for idx, index in enumerate(indexes):
                if idx == 0: continue
                keys = dict(index['key'])
                sour_dict[collection_name].append(keys)

    with MongodbEngine(s_db+"_new") as db:
        for coll, indexes in sour_dict.items():
            print(coll)
            collection = db.get_collection(coll)
            for idx, idx_d in enumerate(indexes):
                tmp = list()
                for k, v in idx_d.items():
                    tmp.append((k, v))
                try:
                    collection.ensure_index(tmp, name="index_" + str(idx + 1), background=True)
                except:
                    pass



if __name__ == '__main__':
    query_data()
