# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/11

from core.excel_base import ExcelBase
from moduler.company_cyl_and_industry import ChinaToExcel


class CYLData(ExcelBase):
    def __init__(self):
        super(CYLData, self).__init__()

    def process(self, *args, **kwargs):
        cyl = ["网络教育产业链", "教育产业链"]
        cyl_info = ChinaToExcel().run(cyl)
        print(len(cyl_info))
        self.save_data_excel(cyl_info)

    def save_data_excel(self, result):
        field_cfg = {
            'company_name': ('公司名称', 2),
            'second_industry_name': ('视野行业', 1),
            'c_name': ('产业链名称', 0),
        }
        self._excel_name = self.name_add_date("中国银行教育产业链POC.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})


if __name__ == '__main__':
    CYLData().run()
