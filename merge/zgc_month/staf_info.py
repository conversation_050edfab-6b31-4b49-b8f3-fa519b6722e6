# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/6/7
import operator
from functools import reduce

from core.excel_base import ExcelBase
from moduler.company_alias_name import CompanyAlias
from moduler.company_staff_data import CompanyStaffData


class StaffInfo(ExcelBase):
    def __init__(self):
        super(StaffInfo, self).__init__()
        self.alias = CompanyAlias()
        self.staff = CompanyStaffData()

    def process(self, *args, **kwargs):
        result = list()
        file_name = self._in_file_path + "/中关村软件园名录.xlsx"
        names_result = self._extract_data(file_name, "Sheet1")
        raw_names = list({i["公司名称"].replace("(", "（").replace(")", "）").strip() for i in names_result})
        alias_dict = self.alias.run(raw_names)
        names = list({alias_dict.get(i, i) for i in raw_names})
        staf_dict = self.staff.run(names)
        holder_list = reduce(operator.add, staf_dict.values())
        result.extend(holder_list)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'cmpName': ('公司名称', 0),
            'staffName': ('高管名称', 1),
            'position': ('职位', 2)}
        self._excel_name = self.name_add_date("高管信息.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})


if __name__ == '__main__':
    p = StaffInfo()
    p.process()
