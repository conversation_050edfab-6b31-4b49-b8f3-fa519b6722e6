# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/6/7
import operator
from functools import reduce

from core.excel_base import ExcelBase
from moduler.company_alias_name import CompanyAlias
from moduler.company_change_info import CompanyChange


class ChangeInfo(ExcelBase):
    def __init__(self):
        super(ChangeInfo, self).__init__()
        self.alias = CompanyAlias()
        self.change = CompanyChange()

    def process(self, *args, **kwargs):
        result = list()
        file_name = self._in_file_path + "/中关村软件园名录.xlsx"
        names_result = self._extract_data(file_name, "Sheet1")
        raw_names = list({i["公司名称"].replace("(", "（").replace(")", "）").strip() for i in names_result})
        alias_dict = self.alias.run(raw_names)
        names = list({alias_dict.get(i, i) for i in raw_names})
        staf_dict = self.change.run(names)
        holder_list = reduce(operator.add, staf_dict.values())
        result.extend(holder_list)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'compName': ('公司名称', 0),
            'chgType': ('变动类型', 1),
            'beChg': ('变动前', 2),
            'afChg': ('变动后', 3),
            'changeTime': ('变动时间', 4),
        }
        self._excel_name = self.name_add_date("工商变更.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})


if __name__ == '__main__':
    p = ChangeInfo()
    p.process()
