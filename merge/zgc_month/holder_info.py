# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/6/7
import operator
from functools import reduce

from core.excel_base import ExcelBase
from moduler.company_a_and_xsb_holder import CompanyAandXsbShareholder
from moduler.company_alias_name import CompanyAlias
from moduler.company_category import CompanyCategory
from moduler.company_holder_old import CompanyHolderOld


class HolderInfo(ExcelBase):
    def __init__(self):
        super(HolderInfo, self).__init__()
        self.alias = CompanyAlias()
        self.gs_holder = CompanyHolderOld()
        self.cat = CompanyCategory()
        self.a_xsb = CompanyAandXsbShareholder()

    def process(self):
        result = list()
        file_name = self._in_file_path + "/中关村软件园名录.xlsx"
        names_result = self._extract_data(file_name, "Sheet1")
        raw_names = list({i["公司名称"].replace("(", "（").replace(")", "）").strip() for i in names_result})
        alias_dict = self.alias.run(raw_names)
        names = list({alias_dict.get(i, i) for i in raw_names})
        cat_dict = self.cat.run(names)
        for name in names:
            cat = cat_dict.get(name, dict())
            category = cat.get("category", list())
            if "ACompany" in category or "ThirdCompany" in category:
                holder_list = self.a_xsb.run([name])
            else:
                holder_list = list(self.gs_holder.run([name]).values())
                if not holder_list:
                    print(name)
                    continue
                holder_list = reduce(operator.add, holder_list)
            result.extend(holder_list)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'cname': ('公司名称', 0),
            'shareholder_name': ('股东名称', 1),
            'holdRatio': ('持股比例', 2)}
        self._excel_name = self.name_add_date("股东信息.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})


if __name__ == '__main__':
    p = HolderInfo()
    p.process()
