# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/6/7
from core.excel_base import ExcelBase
from moduler.company_alias_name import CompanyAlias
from moduler.company_area_info import CompanyArea
from moduler.company_base_info import CompanyGS


class ZGCBase(ExcelBase):
    def __init__(self):
        super(ZGCBase, self).__init__()
        self.base = CompanyGS()
        self.area = CompanyArea()
        self.alias = CompanyAlias()

    def process(self):
        result = list()
        file_name = self._in_file_path + "/中关村软件园名录.xlsx"
        names_result = self._extract_data(file_name, "Sheet1")
        raw_names = list({i["公司名称"].replace("(", "（").replace(")", "）").strip() for i in names_result})
        alias_dict = self.alias.run(raw_names)
        names = list({alias_dict.get(i, i) for i in raw_names})
        base_dict = self.base.run(names)
        area_dict = self.area.run(names)
        for name in names:
            item = dict()
            base = base_dict.get(name, dict())
            item.update(base)
            area = area_dict.get(name, dict())
            item.update(area)
            if item:
                result.append(item)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'compName': ('公司名称', 0),
            'legalPersonName': ('法人代表', 1),
            'regCapital': ('注册资本', 2),
            'currencyCode': ('注册资本币种', 3),
            'creditCode': ('统一信用代码', 4),
            'regNum': ('工商注册码', 5),
            'estiblishDate': ('成立日期', 6),
            'regStatus': ('企业状态', 7),
            'regAddr': ('注册地址', 8),
            'provinceName': ('注册省份', 9),
            'cityName': ('注册城市', 10),
            'district': ('注册区/县', 11),
            'compType': ('公司组织类型', 12),
            'businessScope': ('营业范围', 13)}
        self._excel_name = self.name_add_date("基本信息.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})


if __name__ == '__main__':
    p = ZGCBase()
    p.process()
