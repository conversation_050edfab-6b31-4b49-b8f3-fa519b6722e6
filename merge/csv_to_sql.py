# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2023/10/27

# 设置 CSV 输入文件路径
csv_file_path = 'chg_lqp.csv'

import csv
import os
from datetime import datetime

# 设置 SQL 输出文件路径


# 设置每批次读取的行数
batch_size = 1000000


def process_batch(rows, sql_file):
    for row in rows:
        # 获取 CSV 数据的每个字段
        id_val = row[0].replace(',', "")
        comp_code_val = row[1].replace(',', "")
        comp_name_val = row[2].replace("'", '"')
        first_type_val = row[3].replace("'", '"')
        chg_type_val = row[4].replace("'", '"')
        be_chg_val = row[5].replace("'", '"')
        af_chg_val = row[6].replace("'", '"')
        change_time_val = datetime.strptime(row[7], '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d %H:%M:%S')
        source_id_val = row[8].replace(',', "")
        data_status_val = row[9].replace(',', "")
        create_time_val = datetime.strptime(row[10], '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d %H:%M:%S')
        modify_time_val = datetime.strptime(row[11], '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d %H:%M:%S')

        # 生成 SQL 插入语句
        sql_statement = f"insert into `JY_cd_ms_base_gs_comp_chg_new` (`id`, `compCode`, `compName`, `firstType`, `chgType`, `beChg`, `afChg`, `changeTime`, `sourceId`, `dataStatus`, `createTime`, `modifyTime`) values" \
                        f"({id_val}, {comp_code_val}, '{comp_name_val}', '{first_type_val}', " \
                        f"'{chg_type_val}', '{be_chg_val}', '{af_chg_val}', '{change_time_val}', " \
                        f"{source_id_val}, {data_status_val}, '{create_time_val}', '{modify_time_val}');\n"

        # 将 SQL 插入语句写入 SQL 文件
        sql_file.write(sql_statement)


# 打开 SQL 文件


# 生成 SQL 文件的表头


# 计数器，用于控制每批次的行数
count = 0
f = 0
# 打开 CSV 文件
with open(csv_file_path, 'r', encoding='utf-8') as csv_file:
    # 创建 CSV 读取器
    reader = csv.reader(csv_file)

    # 跳过 CSV 文件的标题行
    next(reader)

    # 逐批次读取 CSV 数据
    batch_rows = []
    for row in reader:
        batch_rows.append(row)
        count += 1

        # 当达到批次大小时处理当前批次数据
        if count % batch_size == 0:
            f += 1
            sql_file_path = f'JY_cd_ms_base_gs_comp_chg_new_{f}.sql'
            sql_file = open(sql_file_path, 'w', encoding='utf-8')
            if f == 1:
                sql_file.write("""CREATE TABLE `JY_cd_ms_base_gs_comp_chg_new` (
                      `id` bigint(20) COMMENT '主键id',
                      `compCode` bigint(20) NOT NULL COMMENT '公司编码',
                      `compName` varchar(512) COLLATE utf8_bin DEFAULT NULL COMMENT '公司名称（冗余）',
                      `firstType` varchar(512) COLLATE utf8_bin DEFAULT NULL COMMENT '变动类型（大类）',
                      `chgType` varchar(512) COLLATE utf8_bin DEFAULT NULL COMMENT '变动类型',
                      `beChg` mediumtext COLLATE utf8_bin COMMENT '变动前',
                      `afChg` mediumtext COLLATE utf8_bin COMMENT '变动后',
                      `changeTime` datetime DEFAULT NULL COMMENT '变动时间',
                      `sourceId` bigint(20) NOT NULL COMMENT '同步标识',
                      `dataStatus` int(4) NOT NULL COMMENT '数据状态',
                      `createTime` timestamp COMMENT '创建时间',
                      `modifyTime` timestamp COMMENT '修改时间'
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='工商变更记录';\n""")
            process_batch(batch_rows, sql_file)
            sql_file.close()
            batch_rows = []
            # if f==3:
            #     break
        print(count)

    # 处理剩余的数据
    if batch_rows:
        f += 1
        sql_file_path = f'JY_cd_ms_base_gs_comp_chg_new_{f}.sql'
        sql_file = open(sql_file_path, 'w', encoding='utf-8')
        process_batch(batch_rows, sql_file)
        sql_file.close()

# 关闭 SQL 文件

# 打印完成消息
print("转换完成！")
