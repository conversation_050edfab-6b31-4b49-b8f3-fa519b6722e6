# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/6/2
from core.excel_base import ExcelBase
from moduler.company_area_info import CompanyArea


class GXNames(ExcelBase):
    def __init__(self):
        super(GXNames, self).__init__()
        self.area = CompanyArea()

    def process(self):
        table_dict = {
            "高新技术企业名录": "dwd_ms_cn_comp_gxjs_list", "科技型中小企业名录": "dwd_ms_cn_comp_kjx_list",
            "国企名录": "dwd_ms_cn_comp_gq_list", "技术创新示范企业名录": "dwd_ms_cn_comp_jscx_list",
            "专精特新小巨人企业名录": "dwd_ms_cn_comp_xjr_list", "工业设计中心名录": "dwd_ms_cn_comp_gysj_list",
            "技术先进型服务企业名录": "dwd_ms_cn_comp_jsxj_list", "工程技术研究中心名录": "dwd_ms_cn_comp_gcjs_list",
            "企业重点实验室名录": "dwd_ms_cn_comp_sys_list", "院士专家工作站名录": "dwd_ms_cn_comp_zjgzz_list",
            "科技成果转化项目名录": "dwd_ms_cn_comp_kjcg_list", "服务型制造示范企业名录": "dwd_ms_cn_comp_fwx_list",
            "新型研发机构名录": "dwd_ms_cn_comp_xxyf_list", "企业技术中心名录": "dwd_ms_cn_comp_jszx_list"}
        for t_n, table in table_dict.items():
            tmp_result = self.query_cmp(table)
            names = {i["compName"] for i in tmp_result}
            area_dict = self.area.run(list(names))
            result = list()
            for item in tmp_result:
                name = item["compName"]
                area = area_dict.get(name, dict()).get("provinceName")
                if area == '天津市':
                    result.append(item)
            self.save_data_excel(result, t_n)

    def save_data_excel(self, result, t_n):
        field_cfg = {
            'compName': ('公司名称', 0),
            'regCode': ('工商注册码', 1),
            'orgCode': ('组织机构码', 2),
            'creditCode': ('社会统一信用码', 3),
            'taxCode': ('税务识别码', 4),
            'level': ('相应级别', 5), }
        if t_n == "高新技术企业名录":
            field_cfg = {
                'compName': ('公司名称', 0),
                'regCode': ('工商注册码', 1),
                'orgCode': ('组织机构码', 2),
                'creditCode': ('社会统一信用码', 3),
                'taxCode': ('税务识别码', 4),
                'area': ('区域', 5),
                'period': ('年度', 6),
                'permitId': ('证书编号', 7),
            }
        elif t_n in ["科技型中小企业名录", "国企名录"]:
            field_cfg = {
                'compName': ('公司名称', 0),
                'regCode': ('工商注册码', 1),
                'orgCode': ('组织机构码', 2),
                'creditCode': ('社会统一信用码', 3),
                'taxCode': ('税务识别码', 4),
            }
        self._excel_name = self.name_add_date("{}.xlsx".format(t_n))
        self.save_to_excel(field_cfg, {"sheet1": result})

    def query_cmp(self, table):
        result = list()
        sql = """SELECT * FROM %s WHERE id>'{}' order by id ASC limit 1000;""" % table
        for result_list in self._query_sql_iter_by_id(sql, "db_seeyii"):
            for item in result_list:
                data_status = item["dataStatus"]
                if data_status == 3:
                    continue
                result.append(item)
        return result


if __name__ == '__main__':
    p = GXNames()
    p.process()
