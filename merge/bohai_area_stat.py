# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/6/23
from collections import defaultdict
from core.excel_base import ExcelBase
from moduler.company_area_info import CompanyArea
from moduler.company_category import CompanyCategory
from moduler.company_in_industry import InIndustry
from utils.datetime_util import DatetimeUtil


class AreaStat(ExcelBase):
    def __init__(self):
        super(AreaStat, self).__init__()
        self.ind_info = InIndustry()
        self.area = CompanyArea()
        self.cat = CompanyCategory()

    def process(self, ind_list):
        result = list()
        ind_info_list = self.ind_info.run(ind_list)
        names = {i["company_name"] for i in ind_info_list}
        area_dict = self.area.run(list(names))
        cat_dict = self.cat.run(list(names))
        tmp_ind_dict = defaultdict(set)
        for ind_item in ind_info_list:
            name = ind_item["company_name"]
            ind_name = ind_item["second_industry_name"]
            tmp_ind_dict[ind_name].add(name)

        for ind_n, name_set in tmp_ind_dict.items():
            tmp_area_dict = defaultdict(list)
            dist_map = defaultdict(str)
            for name in name_set:
                district = area_dict.get(name, dict()).get("district")
                district_code = area_dict.get(name, dict()).get("districtCode")
                if district and district_code:
                    dist_map[district] = district_code
                if district:
                    tmp_area_dict[district].append(name)

            for dist, dist_name_list in tmp_area_dict.items():
                new_item = dict()
                new_item["ind"] = ind_n
                new_item["district"] = dist
                new_item["district_code"] = dist_map[dist]
                new_item["cmp_num"] = len(dist_name_list)
                new_item["stat_date"] = DatetimeUtil.get_today_base().date()
                new_item["bg_zq"] = "年度"
                new_item["bg_rq"] = "20201231"
                tmp_cat_dict = defaultdict(int)
                tmp_name_dict = defaultdict(list)
                for dis_name in dist_name_list:
                    name_cat = cat_dict.get(dis_name, dict()).get("category", list())
                    if "ACompany" in name_cat:
                        tmp_cat_dict["ACompany"] += 1
                        tmp_name_dict["A"].append(dis_name)
                    if "ThirdCompany" in name_cat:
                        tmp_cat_dict["ThirdCompany"] += 1
                        tmp_name_dict["S"].append(dis_name)
                    if "FourthCompany" in name_cat:
                        tmp_cat_dict["FourthCompany"] += 1
                    if "PrepareIPOCompany" in name_cat or "PrepareThirdCompany" in name_cat:
                        tmp_cat_dict["ipo"] += 1
                    if "InvestedCompany" in name_cat and "OthersCompany" in name_cat:
                        tmp_cat_dict["InvestedCompany"] += 1
                    if "ACompany" in name_cat or "ThirdCompany" in name_cat:
                        je_dict = self.query_yysr_info(dis_name)
                        if je_dict:
                            je = je_dict[0].get("yysr")
                            if je:
                                tmp_cat_dict["yysr"] += je
                new_item.update(tmp_cat_dict)
                new_item.update(tmp_name_dict)
                result.append(new_item)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'ind': ('产业环节名称', 0),
            'district_code': ('行政区域代码', 1),
            'district': ('行政区域', 2),
            'cmp_num': ('企业数', 3),
            'ACompany': ('A股企业数', 4),
            'ThirdCompany': ('三板企业数', 5),
            'FourthCompany': ('四板企业数', 6),
            'ipo': ('拟挂牌上市企业数', 7),
            'InvestedCompany': ('已私募融资企业数', 8),
            'stat_date': ('数据统计日期', 9),
            'yysr': ('营收金额(万）', 10),
            'bg_zq': ('营收报告周期', 11),
            'bg_rq': ('营收报告日期', 12),
            'A': ('A股企业', 13),
            'S': ('三板企业', 14),
        }
        self._excel_name = self.name_add_date("渤海银行POC1.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def query_yysr_info(self, name):
        sql = """SELECT a.`companyName`,b.enddate, bizIncome/10000 AS yysr FROM  `sq_sk_basicinfo` a 
                LEFT JOIN `sy_fin_mainindex` b ON a.`innercode`=b.`companyCode` 
                WHERE b.setype in (101, 103) AND a.`companyName`='{}' AND b.enddate='20201231'"""
        query_schema = dict(db_key="157_3306_db_seeyii", sql_statement=sql.format(name))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    p = AreaStat()
    # p.process(["白酒"])
    # p.process(["显示器件", "医药零售及电商", "塑料及合成原料", "汽车传动配件", "矿产金属贸易", "工程机械", "速冻方便食品", "网络信息安全系统", "化合物半导体", "专用车辆", "轮胎",
    #            "航运服务", "行业数据服务", "进出口贸易", "煤炭开采", "通用软件外包", "日用化工", "广电运营", "文化用品", "电梯楼宇设备", "肉制品", "乘用车制造", "建筑工程",
    #            "光伏电池组件", "化学制剂", "发电机组", "百货零售", "电力线缆", "图像传感处理芯片设计", "投资基金管理", "天然气销售", "系统集成", "农副产品贸易", "其他专业工程",
    #            "钢材", "综合物流服务", "图书出版发行", "农用机械", "玻璃纤维", "铁路车辆制造", "化工工程", "石油化工", "医药流通批发", "供配电服务", "显示面板玻璃", "货代",
    #            "新能源汽车电池", "核能发电", "水泥", "旅行社", "电信运营商", "石油天然气开采", "军用航空装备", "航天设备", "船舶制造", "海洋工程装备", "变压器设备", "卡车制造",
    #            "铁路通信信号设备", "铝加工", "水力发电", "金矿开采", "混凝土及构件", "石油工程技术服务", "智慧物联技术服务", "无机盐化工", "生物能源", "制糖", "电子连接器及线束",
    #            "航空机载设备", "航空设备零件", "飞机及发动机制造", "半导体芯片制造", "贵金属加工", "超硬材料及工具", "工程规划设计服务", "铁路运输", "钢构", "光电通信器件",
    #            "纸制卫生品", "厨房家电", "中成药", "铜加工", "铅锌矿开采", "汽车车身配件", "液晶显示屏", "电气设备贸易", "供应链物流", "调味发酵品", "燃气", "乳品",
    #            "家用设备附件", "电力工程技术服务", "有机化工原料", "军用车辆装备", "火力发电", "高温合金材料", "电梯楼宇广告", "广电接收终端设备", "网络数字营销", "防水防腐材料",
    #            "食品分销配送", "数据中心服务", "水务", "饲料", "基建工程", "供热", "休闲时装", "啤酒", "整合营销", "房地产开发", "白酒", "禽类养殖", "生态恢复", "墙体材料",
    #            "通信终端设备", "汽车内饰配件", "化学合成纤维", "数字电视内容运营", "风力发电", "工业传输设备", "农药", "园区开发运营", "电动机设备", "港口运营服务", "金融控股集团",
    #            "钨钼", "触摸屏系统", "硅化工", "生活垃圾处理", "注射剂", "汽车贸易", "智能电网设备", "IT运维服务", "快递服务", "激光加工切割设备", "冶金锻造设备",
    #            "光伏系统工程服务", "煤炭矿山机械", "泵及真空设备", "半导体封装测试", "半导体硅材料", "锂电池", "生猪养殖", "煤化工", "电池正极材料", "电脑手机周边设备", "橡胶制品",
    #            "起重机械", "农药化肥贸易", "游戏开发", "继电保护设备", "氯碱化工", "消费电子制造服务商", "房地产租赁运营", "家纺", "造纸", "铝合金及型材", "石油贸易",
    #            "医学诊疗设备", "高速公路", "屠宰及肉类加工", "炼焦", "氟化工", "铝箔", "印刷电路板", "污水处理", "消费电子结构件", "煤炭贸易", "地铁运营", "公交运营",
    #            "军用通信设备", "医学检验检测服务", "粮食谷物种植", "配电开关控制设备", "复混肥料", "医用敷料", "纺织服装贸易", "钒合金材料", "钢管", "教辅图书", "风电设备",
    #            "汽车配气配件", "工程项目管理服务", "医药研发服务", "内燃机及配件", "原料药及中间体", "计算机设备", "LED照明灯具", "五金电动工具", "安防视频监控设备", "电子密封填料",
    #            "光伏玻璃", "动物保健药品", "磁性材料", "照明灯具及附件", "日用家具", "电声器件", "电子元器件贸易", "幕墙装饰工程", "光纤光缆", "抗肿瘤药物", "液压气压动力机械",
    #            "核电设备", "车轮与轮毂", "空调制冷设备", "镍钴", "其他电池", "维生素及营养类药物", "有机硅", "染料", "服务器存储设备", "白色家电", "油料作物加工", "海洋工程服务",
    #            "水利工程", "生物基因技术", "电视机", "工业控制设备", "人机交互触控芯片设计", "粮食谷物加工", "消费电子产品贸易", "建筑装饰工程", "医疗监测监护仪器", "办公打印设备",
    #            "化工制品贸易", "通信传输设备", "贵金属贸易", "石油钻采设备", "专科医院", "光伏硅片材料", "物业管理服务", "环卫机械", "轮胎骨架材料", "汽车玻璃", "语音识别信息系统",
    #            "家居装饰贸易", "珠宝首饰", "财务税务信息系统", "电视节目制作", "气体压缩机", "金融资产管理公司", "诊断试剂及仪器", "客车制造", "发动机配件", "生物疫苗制品",
    #            "改性及工程塑料", "危险废物处理", "磷化工", "汽车悬架配件", "摩托车电动车", "西服礼服", "港口物流", "软体家具", "精细化工制品"])
    p.process(
        ["IT运维服务", "LED照明灯具", "安防视频监控设备", "白酒", "白色家电", "百货零售", "办公打印设备", "半导体封装测试", "基建工程", "泵及真空设备", "变压器设备", "玻璃纤维",
         "财务税务信息系统", "超硬材料及工具", "车轮与轮毂", "电动机设备", "电力工程技术服务", "生态恢复", "生物基因技术", "电气设备贸易", "复混肥料", "工程规划设计服务", "工业传输设备",
         "供热", "供应链物流"])
