import copy
import sys

sys.path.append('/shiye_data/lj/panda')
# from core.excel_base import ExcelBase
from cfg.config import source_file_path
import pandas as pd
import numpy as np
from moduler.company_alias_name import CompanyAlias
import os
from core.excel_base import ExcelBase

class GAOGAI(ExcelBase):
    def __init__(self):
        super(GAOGAI, self).__init__()
        self.alias = CompanyAlias()
        self.jidu_map = {
            '01': '1', '02': '1', '03': '1',
            '04': '2', '05': '2', '06': '2',
            '07': '3', '08': '3', '09': '3',
            '10': '4', '11': '4', '12': '4',
        }

    def process(self, *args, **kwargs):
        jidu_list = ['2018-1', '2018-2', '2018-3', '2018-4',
                     '2019-1', '2019-2', '2019-3', '2019-4',
                     '2020-1', '2020-2', '2020-3', '2020-4',
                     '2021-1', '2021-2', '2021-3', '2021-4',
                     '2022-1', '2022-2', '2022-3', '2022-4',
                     '2023-1']
        filename1 = '/shiye_data/yx/lqp_poc_3.csv'
        df = pd.read_csv(filename1)
        data_list = df.to_dict(orient="records")
        data_list = self.filter_data(data_list)

        data_list = list(
            filter(lambda n: True if n.get('estiblishdate') and n.get('provincename') == '北京市' else False, data_list))

        city = '北京市'
        result_list = []

        for i in jidu_list:
            jidu = copy.deepcopy(i)
            dic_xz = {}
            xz_list = list(filter(lambda n: self.xz_qy(n, jidu), data_list))
            xz_qy_len = len(xz_list)
            xz_yg_len = sum(list(filter(None, [i.get('shebao_num') for i in xz_list])))
            xz_zl_len = sum(list(filter(None, [i.get('zhuanli_numfrom') for i in xz_list])))
            dic_xz['hy'] = city
            dic_xz['jd'] = jidu
            dic_xz['zt'] = '新增'
            dic_xz['num_qy'] = xz_qy_len
            dic_xz['num_yg'] = xz_yg_len
            dic_xz['num_zl'] = xz_zl_len

            dic_cx = {}
            cx_list = list(filter(lambda n: self.cx_qy(n, jidu), data_list))
            cx_qy_len = len(cx_list)
            cx_yg_len = sum(list(filter(None, [i.get('shebao_num') for i in cx_list])))
            cx_zl_len = sum(list(filter(None, [i.get('zhuanli_numfrom') for i in cx_list])))
            dic_cx['hy'] = city
            dic_cx['jd'] = jidu
            dic_cx['zt'] = '存续'
            dic_cx['num_qy'] = cx_qy_len
            dic_cx['num_yg'] = cx_yg_len
            dic_cx['num_zl'] = cx_zl_len
            result_list.append(dic_xz)
            result_list.append(dic_cx)
        self.save_data_excel(result_list)

    def save_data_excel(self, result_list):
        field_cfg = {

            'Sheet1':
                {
                    'hy': ("区域", 0),
                    'jd': ("季度", 1),
                    'zt': ("状态", 2),
                    'num_qy': ("企业数量", 3),
                    'num_yg': ("员工数量", 4),
                    'num_zl': ("专利数量", 5),
                },

        }
        self._excel_name = self.name_add_date("国家信息中心_需求3_北京.xlsx")
        self.save_to_excel_multi_sheet(field_cfg,
                                       {'Sheet1': result_list,
                                        })

    def cx_qy(self, n, jidu):
        jd_time = n['estiblishdate'][0:5] + self.jidu_map.get(n['estiblishdate'][5:7])
        if jd_time <= jidu:
            zx_time = n['canceldate'][0:5] + self.jidu_map.get(n['canceldate'][5:7]) if n.get(
                'canceldate') else '2024-1'
            dx_time = n['revokedate'][0:5] + self.jidu_map.get(n['revokedate'][5:7]) if n.get(
                'revokedate') else '2024-1'
            if zx_time > jidu and dx_time > jidu:
                return True
            else:
                return False
        else:
            return False

    def xz_qy(self, n, jidu):
        jd_time = n['estiblishdate'][0:5] + self.jidu_map.get(n['estiblishdate'][5:7])
        if jd_time == jidu:
            return True
        else:
            return False

    def merge_dic_a(self, a, b, aname, bname):
        name_key = {}
        for i in b:
            i[aname] = i.pop(bname)
            name_key[i[aname]] = i
        for i in a:
            dict1 = name_key.get(i.get(aname, 0), {})
            i.update(dict1)
        return a

    def get_category_list(self, lst, cat):
        dic = {}
        for a in lst:
            dic[a[cat]] = dic.get(a[cat], [])
            dic[a[cat]].append(a)
        return dic


if __name__ == '__main__':
    p = GAOGAI()
    p.process()
