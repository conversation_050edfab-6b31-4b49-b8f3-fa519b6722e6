# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/20

import decimal
import json
import os
import xlsxwriter
from collections import defaultdict
from datetime import datetime, date
from cfg.config import out_file_path
from utils.datetime_util import DatetimeUtil
from core.excel_base import ExcelBase


class IPO(ExcelBase):
    def __init__(self):
        super(IPO, self).__init__()
        self.exchange_map = {
            "101": "上交所",
            "201": "深交所"}
        self.market = {
            "101": "主板",
            "201": "中小板",
            "301": "创业板",
            "302": "创业板",
            "401": "科创板"}
        self.stat = {
            "101": "启动上市辅导",
            "102": "完成辅导验收",
            "103": "终止上市辅导",
            "201": "已受理",
            "202": "已问询",
            "203": "已反馈",
            "204": "预披露",
            "205": "更新预披露",
            "206": "中止",
            "207": "终止（审核不通过）",
            "208": "终止（撤回）",
            "209": "发审会通过",
            "210": "发审会未通过",
            "211": "暂缓表决",
            "212": "上市委会议通过",
            "213": "上市委会议未通过",
            "214": "暂缓审议",
            "215": "复审委会议通过",
            "216": "复审委会议未通过",
            "217": "提交注册",
            "218": "注册生效",
            "219": "不予注册",
            "220": "终止注册",
            "221": "证监会核准",
            "222": "证监会不予核准",
            "223": "补充审核",
            "301": "已发行上市",
            "302": "发行失败",
            "303": "发行暂缓"}
        self.list_fetch = [
            "id",
            "projId",
            "compName",
            "shortName",
            "finaAmount",
            "reguInstitution",
            "preName",
            "brokName",
            "acctName",
            "lawName",
            ("exchange", "exchange", lambda x: self.exchange_map[str(x)]),
            ("market", "market", lambda x: self.market[str(x)]),
            "acceDate",
            ("currStat", "currStat", lambda x: self.stat[str(x)]),
            "statDate",
            "fingerId",
            "dataStatus",
            "createTime",
            "modifyTime"]
        self.detail_fetch = [
            "id",
            "projId",
            ("stat", "stat", lambda x: self.stat[str(x)]),
            "statDate",
            "ref",
            "fingerId",
            "dataStatus",
            "createTime",
            "modifyTime",
            "compName",
            ("market", "market", lambda x: self.market[str(x)]),
        ]
        self._excel_name = self.name_add_date("浦发银行_IPO数据.xlsx")

    def process(self, *args, **kwargs):
        detail_result = list()
        list_result = list()
        name_list = self.read_excel_data(self._in_file_path + "/浦发_公司名录_0202.xlsx", "Sheet1")
        names = list({item["公司名称"].replace("(", "（").replace(")", "）").strip() for item in name_list})
        list_data_dict = self.query_ipo_data(names)
        for name, item_list in list_data_dict.items():
            # cat_item = self.query_company_category([name])
            # if cat_item:
            #     cat = ''.join(cat_item[0]["category"])
            #     if "ACompany" in cat:
            #         continue
            for item in item_list:
                item_result = self.fetch_dict(item, self.list_fetch)
                list_result.append(item_result)
                detail_l = self.query_detail_data([item["projId"]])
                for i in detail_l:
                    item.pop("statDate", '')
                    i.update(item)
                    item_result = self.fetch_dict(i, self.detail_fetch)
                    detail_result.append(item_result)
        self.save_data_excel(detail_result, list_result)

    def read_excel_data(self, file_name, sheet="Sheet"):
        data_list = self._extract_data(file_name, sheet)
        return data_list

    def save_data_excel(self, detail_result, list_result):
        field_cfg = {
            "id": ("id", 0), "projId": ("项目ID", 1), "compName": ("公司名称", 2),
            "shortName": ("公司简称", 3), "finaAmount": ("募集资金(亿元)", 4),
            "reguInstitution": ("证监局", 5), "preName": ("辅导机构", 6), "brokName": ("保荐机构", 7),
            "acctName": ("会计师事务所", 8), "lawName": ("律师事务所", 9),
            "exchange": ("交易所", 10), "market": ("板块", 11), "acceDate": ("受理日期", 12),
            "currStat": ("最新状态", 13), "statDate": ("最新状态时间", 14)}
        detail_cfg = {
            "id": ("id", 0),
            "projId": ("项目ID", 1),
            "compName": ("公司名称", 2),
            "market": ("板块", 3),
            "stat": ("状态", 4),
            "statDate": ("状态日期", 5),
            "ref": ("附件", 6)}
        output_file_path = os.path.join(out_file_path, self._excel_name)
        xls_file = xlsxwriter.Workbook(output_file_path)
        self._save_to_excel(field_cfg, {"IPO审核最新状态表": list_result}, xls_file)
        self._save_to_excel(detail_cfg, {"IPO审核明细表": detail_result}, xls_file)
        xls_file.close()
        print("finished.")

    def query_company_category(self, names):
        query_schema = {
            "db_name": "category_data",
            "collection_name": "company_category",
            "query_condition": {"cname": {"$in": names}, "is_valid": True},
            "query_field": {"company_code": 1, "st_name": 1, "_id": 0,
                            "category": 1, "cname": 1}}
        query_result = self._data_server.call("query_item", query_schema) or list()
        return query_result

    def query_ipo_data(self, names):
        result = defaultdict(list)
        name_str = "','".join(names)
        sql_statement = """SELECT * from dws_me_trad_ipo_base where dataStatus !=3 and compName in ('{}');"""
        query_schema = dict(db_key="seeyii_db", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result[item["compName"]].append(item)
        return result

    def query_detail_data(self, pro_list):
        name_str = "','".join(pro_list)
        sql_statement = """SELECT * from dws_me_trad_ipo_detail where dataStatus !=3 and projId in ('{}');"""
        query_schema = dict(db_key="seeyii_db", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list

    def _save_to_excel(self, field_dict, sheet_item_dict, xls_file):
        """
        :param field_dict: {
            eg:  "字段名": (u"字段含义", 插入的列号),
                 "cname": (u"公司名称", 0)
        }
        :param sheet_item_dict: {
            Sheet名称:[data]
        }
        :return:
        """
        for sheet_name, item_list in sheet_item_dict.items():
            xls_sheet = xls_file.add_worksheet(name=sheet_name)
            for title in field_dict.values():
                xls_sheet.write(0, title[1], title[0])
            row_no = 1
            for result_item in item_list:
                for field_name, title in field_dict.items():
                    field_value = result_item.get(field_name, "")
                    if isinstance(field_value, decimal.Decimal):
                        field_value = float(field_value)
                    if isinstance(field_value, datetime):
                        field_value = DatetimeUtil.date_to_str(field_value)
                    if isinstance(field_value, date):
                        field_value = field_value.strftime("%Y-%m-%d")
                    if not isinstance(field_value, str):
                        field_value = json.dumps(field_value, ensure_ascii=False)
                    if field_value == "null" or field_value is None:
                        continue
                    xls_sheet.write(row_no, title[1], field_value)
                row_no += 1


if __name__ == '__main__':
    p = IPO()
    p.process()
