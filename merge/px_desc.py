# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022-9-23
from core.excel_base import ExcelBase


class PXDesc(ExcelBase):
    def __init__(self):
        super(PXDesc, self).__init__()
        self.field = ["spectrumId", "spectrumDesc", "capScale", "fingerId", "dataStatus"]

    def process(self):
        result = list()
        px_map = self.__gen_spt_info()
        excel_list = self.reader_map_data()
        for item in excel_list:
            desc = item.get("旧版简介")
            px_name = item.get("新版谱系名称")
            gm = item.get("旧版规模")
            if px_name and (desc or gm):
                _dict = {}
                if desc:
                    _dict["spectrumDesc"] = desc
                if gm:
                    _dict["capScale"] = gm
                _dict["fingerId"] = px_map[px_name]
                _dict["spectrumId"] = px_map[px_name]
                _dict["dataStatus"] = 1
                result.append(_dict)
        print(len(result))
        self.save_to_mysql(self.field, result, "dwd_ms_cn_px_cmn_info")

    def save_to_mysql(self, valid_fields, result_list, table):
        save_schema = {
            "insert_template": "insert into {} (%s) values %s".format(table),
            "result_list": result_list,
            "field_list": valid_fields,
            "db_key": "section_data_63306"}
        self._data_server.call("insert_sql_list", save_schema)

    def __gen_spt_info(self):
        result = dict()
        query_condition = dict()
        offset = None
        while True:
            if offset:
                query_condition["_id"] = {"$gt": offset}
            query_item = {
                "db_name": "raw_data",
                "collection_name": "investor_spectrum_info_v2",
                "query_condition": query_condition,
                "query_field": {
                    '_id': 1, "feature_id": 1, "spectrum_name": 1, "keyword_1": 1},
                "sort_field": [("_id", 1)],
                "limit_n": 100}
            query_result = self._data_server.call("query_item", query_item)
            if not query_result:
                break
            for item in query_result:
                result[item["spectrum_name"]] = item["feature_id"]
            offset = query_result[-1]["_id"]
        return result

    def reader_map_data(self):
        filename = self._in_file_path + '/新旧谱系关系映射表_全_20220923.xlsx'
        data_list = self._extract_data(filename, "20220923")
        return data_list


if __name__ == '__main__':
    p = PXDesc()
    p.process()
