# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2024/3/5
import re


def sql_parse(sql):
    b = sql.replace('"', "'").replace("\\", "\\\\").replace("'|", '\\"|').replace(" ", '')
    a = re.sub("[\n\t\s]+", " ", b)
    d = re.search("'SJ.*?'", sql).group(0)
    e = """
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240328' )
    SELECT  {},
     "{}",
     map("tempview1",""),"9", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    """.format(d, a)
    # print(e)
    with open("./sj_out_bc1.txt", 'a', encoding='utf-8') as f:
        f.write(e)


if __name__ == '__main__':
    sql = """
  with source_df as (
  SELECT 
    tb2.subjectcode, 
    tb1.eventdate, 
    tb1.title, 
    tb1.pt1, 
    concat_ws(
      '&#&', 
      '专利', 
      'sy_cd_me_buss_ip_patn_new', 
      'id', 
      string(tb1.id)
    ) as retrovalue 
  FROM 
    (
      select 
        id, 
        eventdate, 
        title, 
        case when pattype = '1' then '发明' when pattype = '2' then '实用新型' when pattype = '3' then '外观设计' when pattype = '4' then '短期专利' when pattype = '5' then '其它' when pattype = '6' then '译文' when pattype = '7' then '检索报告' when pattype = '8' then 'pct发明' when pattype = '9' then 'pct实用新型' else '' end as pt1 
      from 
        (
          select 
            id, 
            pattype, 
            title, 
            pubdate as eventdate, 
            isvalid, 
            datastatus, 
            row_number() over (
              partition by id 
              order by 
                filedate desc
            ) num 
          from 
            seeyii_emr_gs.dwd_me_buss_ip_patn
        ) t 
      where 
        t.num = 1 
        and t.eventdate >= '2021-01-01' 
        AND t.datastatus != 3 
        AND t.isvalid = '1'
    ) AS tb1 
    JOIN (
      select 
        pkid, 
        subjectcode 
      from 
        (
          select 
            pkid, 
            compcode AS subjectcode, 
            row_number() over (
              partition by fingerid 
              order by 
                filedate desc
            ) num2 
          from 
            seeyii_emr_gs.dwd_me_buss_ip_patn_se
        ) t 
      where 
        t.num2 = 1 
        and t.subjectcode is not null
    ) AS tb2 ON tb1.id = tb2.pkid
), 
des_dat as (
  select 
    subjectcode, 
    eventdate, 
    concat(
      concat('100021', '&#&', pt1, '&#&', '7')
    ) as eigenvalue, 
    retrovalue, 
    CAST(null AS STRING) as eventsubject, 
    CAST(null AS STRING) as url, 
    'SJ000101001' as eventtype, 
    CAST(null AS STRING) as expiredate, 
    concat(
      '本单位于', 
      eventdate, 
      '新增专利', 
      if(
        (pt1 is null) 
        or (pt1 = ''), 
        '', 
        concat(',专利类型为', pt1)
      ), 
      if(
        (title is null) 
        or (title = ''), 
        '', 
        concat(',专利名称为', title)
      ), 
      '。'
    ) as desc1, 
    '1' as datastatus, 
    date_format(
      current_timestamp(), 
      'yyyy-MM-dd HH:mm:ss'
    ) as modifytime, 
    {fileDate} as filedate 
  from 
    source_df
), 
ret_dat as (
  select 
    eventsubject, 
    subjectcode, 
    eventdate, 
    expiredate, 
    url, 
    eventtype, 
    desc1, 
    datastatus, 
    filedate, 
    modifytime, 
    eigenvalue, 
    retrovalue, 
    fingerId_row(
      concat_ws(
        ',', 
        if(
          eventsubject is NULL, '#', eventsubject
        ), 
        if(
          subjectcode is NULL, '#', subjectcode
        ), 
        if(eventtype is NULL, '#', eventtype), 
        if(desc1 is NULL, '#', desc1), 
        if(url is NULL, '#', url), 
        if(eventdate is NULL, '#', eventdate), 
        if(
          expiredate is NULL, '#', expiredate
        )
      )
    ) as eventid 
  from 
    des_dat
) insert into {ku}.{tb} partition (filedate) 
select 
  eventid, 
  subjectcode, 
  eventsubject, 
  eventtype, 
  desc1 as `desc`, 
  url, 
  eventdate, 
  null as expiredate, 
  null as property1, 
  null as property2, 
  null as property3, 
  null as property4, 
  eigenvalue, 
  retrovalue, 
  datastatus, 
  modifytime, 
  filedate 
from 
  ret_dat
    
    
     """
    sql_parse(sql)
