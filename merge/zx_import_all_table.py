# -*- encoding:utf-8 -*-
# Copyright (c) 2023 Shiye Inc.
# All rights reserved.
#
# <AUTHOR> <EMAIL>
# Date: 2023/10/12
import os
import json
import decimal
import xlsxwriter
from cfg.config import out_file_path
from datetime import datetime, date
from cfg.config import mysql_db_cfg as CONFIG
from db.engine.mysql_engine import DB as MysqlDB
from utils.datetime_util import DatetimeUtil
from sqlalchemy import text

from core.excel_base import ExcelBase


class ZXImport(ExcelBase):
    def __init__(self):
        super(ZXImport, self).__init__()
        self.table_cfg = {
            "xskv2": [
                # {"sy_cd_ms_base_dustry_des_center_list":["and level=1"]},
                # {"sy_cd_ms_base_key_labor_list":[" and compLevel=1 "]},
                # {"sy_cd_ms_cn_comp_gjkjcg_list":[""]},
                # {"sy_cd_ms_cn_comp_ndc":[""]},
                # {"sy_cd_ms_base_key_labor_list":[" and compLevel=2 "]},
                # {"sy_cd_ms_base_key_labor_list":[" and compLevel=3 "]},
                # {"sy_cd_ms_base_tech_center_list":[" and compLevel=1 and compType='专精特新中小企业' "]},
                {"sy_cd_ms_base_sole_comp_list": [""]},  # ing
            ]
        }

    def process(self, *args, **kwargs):
        for db, table_list in self.table_cfg.items():
            for table_dict in table_list:
                for table, con_list in table_dict.items():
                    result = list()
                    idx = 0
                    field_dict = self.import_data_cfg(db, table)
                    # output_file_path = os.path.join(out_file_path, table + ".xlsx")
                    # xls_file = xlsxwriter.Workbook(output_file_path, options={'strings_to_urls': False})
                    for data_list in self.query_mysql_data(table, db, con_list):
                        result.extend(data_list)
                        print(f"len(result)={len(result)}")
                        # for idx in range(0, len(result), 1000000):
                        #     data = result[idx:idx + 1000000]
                        if len(result) >= 100000:
                            idx+=1
                            output_file_path = os.path.join(out_file_path, table + "{}.xlsx".format(idx))
                            xls_file = xlsxwriter.Workbook(output_file_path, options={'strings_to_urls': False})
                            # xls_sheet = xls_file.add_worksheet(name="sheet{}".format(idx + 1))
                            xls_sheet = xls_file.add_worksheet(name="sheet")
                            self._save_to_excel(field_dict, xls_sheet, result)
                            result.clear()
                            xls_file.close()

    def query_mysql_data(self, table, db, con_list):
        sql = """SELECT id, compName, creditCode, legalPersonName, parentCode, estiblishDate, aprvPublDt, regCapitalAm, regOrg, regAddr, provinceCode, cityCode FROM {} where id > %s and dataStatus!=3 {} ORDER BY id ASC limit 10000;""".format(
            table, *con_list)
        print(sql)
        return self.query_sql_iter_v2(sql, db)

    @staticmethod
    def _save_to_excel(field_dict, xls_sheet, item_list):
        for title in field_dict.values():
            xls_sheet.write(0, title[1], title[0])
        row_no = 1
        for result_item in item_list:
            for field_name, title in field_dict.items():
                field_value = result_item.get(field_name, "")
                if isinstance(field_value, decimal.Decimal):
                    field_value = float(field_value)
                if isinstance(field_value, datetime):
                    field_value = DatetimeUtil.date_to_str(field_value)
                if isinstance(field_value, date):
                    field_value = field_value.strftime("%Y-%m-%d")
                if not isinstance(field_value, str):
                    field_value = json.dumps(field_value, ensure_ascii=False)
                if field_value == "null":
                    continue
                xls_sheet.write(row_no, title[1], field_value)
            row_no += 1
        print("finished.")

    def import_data_cfg(self, db, table):
        data_dict = dict()
        item_list = self.query_table_schema(db, table)
        for idx, item in enumerate(item_list):
            data_dict.setdefault(item["COLUMN_NAME"], (item["COLUMN_COMMENT"], idx))
        return data_dict

    @staticmethod
    def query_table_schema(db, table):
        result = list()
        sql = """select column_name, column_comment 
        from information_schema.columns where table_name = '{}';""".format(table)
        with MysqlDB(CONFIG[db]) as db:
            query_result = db.session.execute(text(sql)).fetchall()
            for item in query_result:
                new_item = dict(item)
                if new_item["COLUMN_NAME"] not in (
                        "compName", "creditCode", "legalPersonName", "parentCode", "estiblishDate", "aprvPublDt",
                        "regCapitalAm", "regOrg", "regAddr", "provinceCode", "cityCode"):
                    continue
                result.append(new_item)
        return result

    @classmethod
    def query_sql_iter_v2(cls, sql_statement, db_key):
        with MysqlDB(CONFIG[db_key]) as db:
            obj_id = None
            while True:
                if not obj_id:
                    result_list = db.session.execute(text(sql_statement % "0")).fetchall()
                else:
                    result_list = db.session.execute(text(sql_statement % obj_id)).fetchall()
                if not result_list:
                    break
                result = [dict(item) for item in result_list]
                yield result
                obj_id = result[-1]["id"]
                print(f"obj_id={obj_id}")
                result.clear()


if __name__ == '__main__':
    p = ZXImport()
    p.process()
