
INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000402003',
 " with sour_df as (select * from seeyii_data_house.dwd_me_trad_nq_srmloan where filedate in (select max(filedate) from seeyii_data_house.dwd_me_trad_nq_srmloan where modifytime is not null)), next_df AS ( SELECT date_format(enddate, 'yyyy-MM-dd') as enddate, compcode as subjectcode, '' as eventsubject, 'SJ000402003' as eventtype, int(float(loanterm) * 365) as loanterm, bankpledge, other, rate, mortgagerate from sour_df where compcode is not NULL and enddate > '2021-01-01' and loanterm is not NULL and bankpledge is not NULL ), tmp_df as ( select *, date_add(enddate, loanterm) as expiredate from next_df ), add_desc_df as ( select *,CAST(null AS STRING) as url,date_sub(expiredate, 60) as eventdate, concat('公司质押于',bankpledge, '的股权将于两个月后到期') as a, if((mortgagerate is null) or (mortgagerate =''),'',concat('。本次质押股比',string(round(mortgagerate,4)),'%')) as b, if((rate is null) or (rate =''),'', concat('，利率为', rate)) as c, if((other is null) or (other =''),'', concat('，存在其他情况：', other)) as d, '。' e from tmp_df ), final_df as (select *, concat_ws('', a,b,c,d,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000201001',
 " with main_df as (select * from ( select *, row_number() over(partition by id order by filedate desc) as rnumber from seeyii_data_house.dwd_me_trad_ipo_base ) as tb where rnumber = 1 and datastatus != 3 ), sour_df as (select * from ( select *, row_number() over(partition by id order by filedate desc) as rnumber from seeyii_data_house.dwd_me_trad_ipo_detail ) as tb where rnumber = 1 and datastatus != 3 ), all_df as ( select * from ( select *, lag(stat,1) over(partition by compcode, projid order by statdate desc, stat desc) as up_stat, lag(statdate,1) over(partition by compcode, projid order by statdate desc, stat desc) as up_statdate from ( select a.stat, a.statdate,a.projid, b.compname, b.compcode, b.brokname, b.prename, b.currstat, b.statdate as curdate from sour_df a join main_df b on a.projid=b.projid ) t ) t2 where stat in ('101', '102', '201') ), next_df AS ( SELECT date_format(statdate, 'yyyy-MM-dd') as eventdate, compcode as subjectcode, compname as eventsubject, case stat when '101' then 'SJ000201001' when '102' then 'SJ000201002' when '201' then 'SJ000201003' end as eventtype, CAST(up_statdate AS STRING) as expiredate, brokname, prename from all_df where statdate is not NULL and compcode is not null ), add_desc_df as ( select *, CAST(null AS STRING) as url, concat('公司于',eventdate,'启动上市辅导') as a, if((prename is null) or (prename =''),'',concat('，辅导机构为：',prename)) as b, '。' c from next_df where eventtype='SJ000201001' union select *, CAST(null AS STRING) as url, concat('公司于',eventdate,'完成上市辅导验收') as a, '，即将提交上市申请' as b, '。' c from next_df where eventtype='SJ000201002' union select *, CAST(null AS STRING) as url, concat('公司提交的上市申请已于',eventdate,'被证监会受理，已进入排队状态') as a, if((brokname is null) or (brokname =''),'',concat('，保荐机构为：',brokname)) as b, '。' c from next_df where eventtype='SJ000201003' ), final_df as (select *, concat_ws('', a,b,c) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000204005',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_smjj where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_smjj where modifytime is not null)), next_df AS ( SELECT date_format(filingtime, 'yyyy-MM-dd') as eventdate, compcode as subjectcode, compname as eventsubject, 'SJ000204005' as eventtype, fundname, fundtype from sour_df where filingtime is not NULL and compcode is not null ), add_desc_df as ( select *,CAST(null AS STRING) as url,CAST(null AS STRING) as expiredate, concat('基金管理人于',eventdate,'登记设立私募基金') as a, if((fundname is null) or (fundname =''),'',concat('，名称为',fundname)) as b, if((fundtype is null) or (fundtype =''),'', concat('，业务类型为：', fundtype)) as c, '。' d from next_df ), final_df as (select *, concat_ws('', a,b,c,d) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000304001',
 " with sour_df as ( select * from ( select *, row_number() over(partition by id order by filedate desc) as rnumber from seeyii_data_house.dwd_ms_base_comp_addr_chg ) as tb where rnumber = 1 and datastatus != 3 ), next_df AS ( SELECT date_format(changetime, 'yyyy-MM-dd') as eventdate, compcode as subjectcode, '' as eventsubject, 'SJ000304001' as eventtype, beproname, becitynm,bedistrict,afproname,afcitynm,afdistrict from sour_df where changetime is not NULL and compcode is not NULL and changetime>='2021-06-01' and bedistrict != afdistrict ), final_df as ( select *,CAST(null AS STRING) as url,CAST(null AS STRING) as expiredate, concat('','公司于',eventdate, '发生迁址，从', if(beproname is NULL ,'', beproname), if(becitynm is NULL ,'', becitynm), if(bedistrict is NULL ,'', bedistrict), '迁移至', if(afproname is NULL ,'', afproname), if(afcitynm is NULL ,'', afcitynm), if(afdistrict is NULL ,'', afdistrict),'。') as desc1 from next_df )  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000401001',
 " with main_df as (select * from seeyii_data_house.dwd_ms_base_secumain where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_base_secumain where modifytime is not null) AND datastatus !=3 AND compcode is not null AND secucategory=1 and listedstate=1 ), sour_df as (select * from seeyii_data_house.dwd_me_trad_sk_trustinvestsitn where filedate in (select max(filedate) from seeyii_data_house.dwd_me_trad_sk_trustinvestsitn  where modifytime is not null) AND datastatus !=3 AND compcode is not null AND (trusteename != '' or trusteename is not NULL) AND (trustinvestname != '' or trustinvestname is not NULL) AND trustinvestenddate is not NULL), next_df AS ( SELECT date_format(a.trustinvestenddate, 'yyyy-MM-dd') as expiredate, b.compcode as subjectcode, b.chiname as eventsubject, 'SJ000401001' as eventtype, trusteename, trustinvestname from sour_df as a join main_df as b on a.compCode=b.compCode ), tmp_df as ( select *, date_sub(expiredate, 60) as eventdate from next_df ), final_df as ( select *,CAST(null AS STRING) as url, concat('公司于',trusteename,'处办理的委托理财', trustinvestname, '将于两个月后到期，到期日为', expiredate,'。') as desc1 from tmp_df where eventdate<=current_date() )  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000102009',
 " with source_df as ( select * from ( select *, row_number() over(partition by id order by filedate desc) as rnumber from seeyii_data_house.dwd_ms_base_gjyfzx_base_info ) as tb where rnumber = 1 and datastatus != 3 ), next_df AS ( SELECT date_format(sourcepubdate, 'yyyy-MM-dd') as eventdate, compcode as subjectcode, compname as eventsubject, 'SJ000102009' as eventtype, level,year,rawtype,specificname from source_df where sourcepubdate is not NULL and compcode is not NULL ), add_desc_df as ( select *,CAST(null AS STRING) as url,CAST(null AS STRING) as expiredate, concat('公司于',eventdate,'被认定为') as a, if((year is null) or (year =''),'',concat(year,'年度的')) as b, if((level is null) or (level =''),'', level) as c, '高新技术企业研究开发中心' as d, if((rawtype is null) or (rawtype =''),'', concat('，类型为：',rawtype)) as e, if((specificname is null) or (specificname =''),'', concat('，名目为：',specificname)) as f, '。' as g from next_df ), final_df as (select *, concat_ws('', a,b,c,d,e,f,g) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106100',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_ckxkz where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_ckxkz where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(validDate, 'yyyy-MM-dd') as eventdate, date_format(invalidDate, 'yyyy-MM-dd') as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106100' as eventtype,url,mineralType,miningRightName from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获得采矿许可证') as a, if((miningRightName is null) or (miningRightName =''),'',concat('，矿业权名称为',miningRightName)) as b, if((mineralType is null) or (mineralType =''), '', concat('，矿种为', mineralType)) as c, '。' d from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c,d) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106101',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_kcxkz where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_kcxkz where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(validDate, 'yyyy-MM-dd') as eventdate, date_format(invalidDate, 'yyyy-MM-dd') as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106101' as eventtype,url,mineralType,miningRightName from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获得采矿许可证') as a, if((miningRightName is null) or (miningRightName =''),'',concat('，矿业权名称为',miningRightName)) as b, if((mineralType is null) or (mineralType =''), '', concat('，矿种为', mineralType)) as c, '。' d from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c,d) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106102',
 " with sour_df as (select * from seeyii_data_house.dwd_mm_cn_prop_yqckqdj where filedate in (select max(filedate) from seeyii_data_house.dwd_mm_cn_prop_yqckqdj where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(noticeDate, 'yyyy-MM-dd') as eventdate, date_format(invalidDate, 'yyyy-MM-dd') as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106102' as eventtype,url,prjType,projectName from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获得油气采矿权') as a, if((projectName is null) or (projectName =''),'',concat('，油气田名称为',projectName)) as b, if((prjType is null) or (prjType =''), '', concat('，项目类型为', prjType)) as c, '。' d from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c,d) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106103',
 " with sour_df as (select * from seeyii_data_house.dwd_mm_cn_prop_yqtkqdj where filedate in (select max(filedate) from seeyii_data_house.dwd_mm_cn_prop_yqtkqdj where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(noticeDate, 'yyyy-MM-dd') as eventdate, date_format(invalidDate, 'yyyy-MM-dd') as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106103' as eventtype,url, projectName,prjType from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获得油气探矿权') as a, if((projectName is null) or (projectName =''),'',concat('，油气田名称为',projectName)) as b, if((prjType is null) or (prjType =''), '', concat('，项目类型为', prjType)) as c, '。' d from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c,d) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106104',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_taea_gm where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_taea_gm where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(pubTime, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106104' as eventtype,batch,url from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司被认定为批准开采黄金矿产申请企业') as a, if((batch is null) or (batch =''),'',concat('，批次为',batch)) as b, '。' c from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106105',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_jzqyzz_df where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_jzqyzz_df where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(issueDate, 'yyyy-MM-dd') as eventdate, date_format(invalidDate, 'yyyy-MM-dd') as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106105' as eventtype,qualifType,qualifSeq,qualifMajor,url from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获得建筑企业资质（地方）') as a, if((qualifType is null) or (qualifType =''),'',concat('，资质类别为',qualifType)) as b, if((qualifSeq is null) or (qualifSeq =''),'',concat('，资质序列为',qualifSeq)) as c, if((qualifMajor is null) or (qualifMajor =''),'',concat('，资质专业为',qualifMajor)) as d, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c,d,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106106',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_jzqyzz_qg where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_jzqyzz_qg where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(issueDate, 'yyyy-MM-dd') as eventdate, date_format(invalidDate, 'yyyy-MM-dd') as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106106' as eventtype,qualifType,qualifClass,url from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获得建筑企业资质（全国）') as a, if((qualifType is null) or (qualifType =''),'',concat('，资质类别为',qualifType)) as b, if((qualifClass is null) or (qualifClass =''),'',concat('，资质等级为',qualifClass)) as c, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106107',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_ceonfp where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_ceonfp where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(pubTime, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106107' as eventtype,level,url from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获评网络货运平台A级企业') as a, if((level is null) or (level =''),'',concat('，级别为',level)) as b, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106108',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_sccle where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_sccle where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(pubTime, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106108' as eventtype,compType,stars,url from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获评星级冷链物流企业') as a, if((compType is null) or (compType =''),'',concat('，企业类型为',compType)) as b, if((stars is null) or (stars =''),'',concat('，星级为',stars)) as c, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106109',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_credit where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_credit where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(pubDate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106109' as eventtype,safeLevel,url, case when registerType='1' THEN '备案' when registerType='2' THEN '在案' when registerType='3' THEN '拟备案' else registerType end as register_type from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司被认证为企业征信备案机构') as a, if((register_type is null) or (register_type =''),'',concat('，备案类型为',register_type)) as b, if((safeLevel is null) or (safeLevel =''),'',concat('，信用信息系统安全等级为',safeLevel)) as c, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000402004',
 " with sour_df as (select * from seeyii_data_house.dwd_me_trad_nq_loan where filedate in (select max(filedate) from seeyii_data_house.dwd_me_trad_nq_loan where modifytime is not null)), next_df AS ( SELECT date_format(enddate, 'yyyy-MM-dd') as enddate, compcode as subjectcode, '' as eventsubject, 'SJ000402004' as eventtype, regexp_replace(loantype, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\\r', '') as loantype_r, regexp_replace(loanrate, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\\r', '') as loanrate_r, regexp_replace(loanpeople, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\\r', '') as loanpeople_r, regexp_replace(loanprice, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\\r', '') as loanprice_r, regexp_replace(fctype, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\\r', '') as fctype_r, regexp_replace(guaranteepeople, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\\r', '') as guaranteepeople_r, case WHEN isglation='1' THEN '是' WHEN isglation='0' THEN '不是' end as isglation,pawn, int(float(loanterm) * 365) as loanterm from sour_df where compcode is not NULL and enddate is not NULL and loanterm is not NULL ), tmp_df as ( select *, date_add(enddate, loanterm) as expiredate from next_df ), add_desc_df as ( select *,CAST(null AS STRING) as url,date_sub(expiredate, 60) as eventdate, concat('公司于',enddate) as a, if((loanpeople_r is null) or (loanpeople_r =''),'',concat('公告向',loanpeople_r,'贷款')) as b, if((loanprice_r is null) or (loanprice_r =''),'', concat( string(round(loanprice_r,2)), '万元')) as c, if((expiredate is null) or (expiredate =''),'', concat('，贷款期限', expiredate, '，将于两个月后到期')) as d, if((loantype_r is null) or (loantype_r =''),'', concat('。此笔贷款类型为', loantype_r)) as e, if((fctype_r is null) or (fctype_r =''),'', concat('，币种为', fctype_r)) as f, if((loanrate_r is null) or (loanrate_r =''),'', concat('，贷款利率', string(round(loanrate_r,4)), '%')) as g, if((pawn is null) or (pawn =''),'', concat('，抵押物为', pawn)) as h, if((guaranteepeople_r is null) or (guaranteepeople_r =''),'', concat('，担保方为', guaranteepeople_r)) as i, if((isglation is null) or (isglation =''),'', concat('，担保方', isglation, '公司关联方')) as j, '。' k from tmp_df ), final_df as (select *, concat_ws('', a,b,c,d,e,f,g,h,i,j, k) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106110',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_xslbxzjjgxkz where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_xslbxzjjgxkz where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(publishDate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106110' as eventtype,orgFirstClass,url from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获得新设立保险中介机构许可证') as a, if((orgFirstClass is null) or (orgFirstClass =''),'',concat('，一级机构类型为',orgFirstClass)) as b, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106111',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_sycppwh where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_sycppwh where modifytime is not null) and datastatus!='3' and isvalid='1'), next_df AS ( SELECT date_format(approDate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, regexp_replace(compname, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\\r', '') as eventsubject, 'SJ000106111' as eventtype,url, regexp_replace(usualName, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\\r', '') as usual_name from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获得兽药产品批准文号') as a, if((usual_name is null) or (usual_name =''),'',concat('，兽药通用名为',usual_name)) as b, '。' e from next_df where (subjectcode is not NULL or subjectcode !='') and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,e) as desc1 from add_desc_df )  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106112',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_syqy where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_syqy where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(issueDate, 'yyyy-MM-dd') as eventdate, date_format(untilDate, 'yyyy-MM-dd') as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106112' as eventtype,url from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获评兽药生产企业') as a, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106113',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_syzc where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_syzc where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(noticeDate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106113' as eventtype,productionName,url,category from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获得国内新兽药注册') as a, if((productionName is null) or (productionName =''),'',concat('，新兽药名称为',productionName)) as b, if((category is null) or (category =''),'',concat('，类别为',category)) as c, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106114',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_core_inter_log where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_core_inter_log where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(publishDate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106114' as eventtype,url,batchNum,trafficField, accessCounArea from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获评国际物流运输重点联系企业') as a, if((batchNum is null) or (batchNum =''),'',concat('，批次为',batchNum)) as b, if((trafficField is null) or (trafficField =''),'',concat('，运输领域为',trafficField)) as c, if((accessCounArea is null) or (accessCounArea =''),'',concat('，通达国家和地区为',accessCounArea)) as d, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c,d,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106115',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_hwzdjkxkqy where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_hwzdjkxkqy where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(handlingTime, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106115' as eventtype,url from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获评货物自动进口许可企业') as a, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106116',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_core_e_commerce where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_core_e_commerce where modifytime is not null) and datastatus!='3'), next_df AS ( SELECT date_format(publishDate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106116' as eventtype,url,classifyShow from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获评跨境电商统计调查重点监测企业') as a, if((classifyShow is null) or (classifyShow =''),'',concat('，业务类型为',classifyShow)) as b, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106117',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_trade_credit_chk where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_trade_credit_chk where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(publishDate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106117' as eventtype,url from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获评贸易信贷统计调查企业') as a, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106118',
 " with sour_df as (select * from seeyii_data_house.dwd_me_buss_trade_logistics where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_trade_logistics where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(publishDate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106118' as eventtype,sourceurl as url from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获评全国商贸物流重点联系企业') as a, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106157',
 " with chi_tb as ( select * from ( select *, row_number() OVER (partition by id order by filedate desc) as num from seeyii_data_house.dwd_me_buss_per_slsdkczz_certification) as a where datastatus!='3' and isvalid='1' and num=1 ), par_tb as( select * from ( select *, row_number() OVER (partition by id order by filedate desc) as num from seeyii_data_house.dwd_me_buss_per_slsdkczz_base ) as a where datastatus!='3' and isvalid='1' and num=1 ), sour_df as ( select a.*, b.compcode, b.compname from chi_tb as a join par_tb as b on a.uid=b.uid ), next_df AS ( SELECT date_format(certifTime, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106157' as eventtype,url,certifType,certifDomin,certifLevel from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获得水利水电勘察资质') as a, if((certifType is null) or (certifType =''),'',concat('，资质类型为',certifType)) as b, if((certifLevel is null) or (certifLevel =''),'',concat('，资质等级为',certifLevel)) as c, if((certifDomin is null) or (certifDomin =''),'',concat('，资质专业为',certifDomin)) as d, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), SJ000106157 as (select *, concat_ws('',a,b,c,d,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from SJ000106157 ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106158',
 " with chi_tb as ( select * from seeyii_data_house.dwd_me_buss_per_slsdsjzz_certification where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_slsdsjzz_certification where modifytime is not null) and datastatus!='3' and isvalid='1' ), par_tb as( select * from seeyii_data_house.dwd_me_buss_per_slsdsjzz_base where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_slsdsjzz_base where modifytime is not null) and datastatus!='3' and isvalid='1' ), sour_df_58 as ( select a.*, b.compcode, b.compname from chi_tb as a join par_tb as b on a.uid=b.uid ), next_df AS ( SELECT date_format(certifTime, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106158' as eventtype,url,certifType,certifDomin,certifLevel from sour_df_58 ), add_desc_df as ( select *, concat(eventdate, '，公司获得水利水电设计资质') as a, if((certifType is null) or (certifType =''),'',concat('，资质类型为',certifType)) as b, if((certifLevel is null) or (certifLevel =''),'',concat('，资质等级为',certifLevel)) as c, if((certifDomin is null) or (certifDomin =''),'',concat('，资质专业为',certifDomin)) as d, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), SJ000106158 as (select *, concat_ws('',a,b,c,d,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from SJ000106158 ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106160',
 " with chi_tb as ( select * from seeyii_data_house.dwd_me_buss_per_stbcfabzzz_certification where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_stbcfabzzz_certification where modifytime is not null) and datastatus!='3' and isvalid='1' ), par_tb as( select * from seeyii_data_house.dwd_me_buss_per_stbcfabzzz_base where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_stbcfabzzz_base where modifytime is not null) and datastatus!='3' and isvalid='1' ), sour_df_60 as ( select a.*, b.compcode, b.compname from chi_tb as a join par_tb as b on a.uid=b.uid ), next_df AS ( SELECT date_format(certifTime, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106160' as eventtype,url,certifType,certifDomin,certifLevel from sour_df_60 ), add_desc_df as ( select *, concat(eventdate, '，公司获得水土保持方案编制资质') as a, if((certifType is null) or (certifType =''),'',concat('，资质类型为',certifType)) as b, if((certifLevel is null) or (certifLevel =''),'',concat('，资质等级为',certifLevel)) as c, if((certifDomin is null) or (certifDomin =''),'',concat('，资质专业为',certifDomin)) as d, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), SJ000106160 as (select *, concat_ws('',a,b,c,d,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from SJ000106160 ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106161',
 " with chi_tb as ( select * from seeyii_data_house.dwd_me_buss_per_szylzzz_certification where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_szylzzz_certification where modifytime is not null) and datastatus!='3' and isvalid='1' ), par_tb as( select * from seeyii_data_house.dwd_me_buss_per_szylzzz_base where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_szylzzz_base  where modifytime is not null) and datastatus!='3' and isvalid='1' ), sour_df_61 as ( select a.*, b.compcode, b.compname from chi_tb as a join par_tb as b on a.uid=b.uid ), next_df AS ( SELECT date_format(certifTime, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106161' as eventtype,url,certifType,certifDomin,certifLevel from sour_df_61 ), add_desc_df as ( select *, concat(eventdate, '，公司获得水资源论证资质') as a, if((certifType is null) or (certifType =''),'',concat('，资质类型为',certifType)) as b, if((certifLevel is null) or (certifLevel =''),'',concat('，资质等级为',certifLevel)) as c, if((certifDomin is null) or (certifDomin =''),'',concat('，资质专业为',certifDomin)) as d, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), SJ000106161 as (select *, concat_ws('',a,b,c,d,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from SJ000106161 ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106162',
 " with sour_df_62 as (select * from seeyii_data_house.dwd_me_buss_per_kdywjyxkz where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_kdywjyxkz where modifytime is not null) and datastatus!='3' and isvalid='1' ), next_df AS ( SELECT date_format(startDate, 'yyyy-MM-dd') as eventdate, date_format(expireDate, 'yyyy-MM-dd') as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106162' as eventtype,url,licenseCategory,bussScope from sour_df_62 ), add_desc_df as ( select *, concat(eventdate, '，公司获得快递业务经营许可证') as a, if((licenseCategory is null) or (licenseCategory =''),'',concat('，许可类别为',licenseCategory)) as b, if((bussScope is null) or (bussScope =''),'',concat('，业务范围为',bussScope)) as c, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), SJ000106162 as (select *, concat_ws('',a,b,c,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from SJ000106162 ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106164',
 " with sour_df_64 as (select * from seeyii_data_house.dwd_me_buss_per_sylcsp where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_sylcsp where modifytime is not null) and datastatus!='3' and isvalid='1' ), next_df AS ( SELECT date_format(untilStartDate, 'yyyy-MM-dd') as eventdate, date_format(untilEndDate, 'yyyy-MM-dd') as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106164' as eventtype,url,projectName from sour_df_64 ), add_desc_df as ( select *, concat(eventdate, '，公司获得兽药临床试验审批') as a, if((projectName is null) or (projectName =''),'',concat('，项目名称为',projectName)) as b, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), SJ000106164 as (select *, concat_ws('',a,b,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from SJ000106164 ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106159',
 " with chi_tb as ( select * from seeyii_data_house.dwd_me_buss_per_slsdzxzz_certification where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_slsdzxzz_certification where modifytime is not null) and datastatus!='3' and isvalid='1' ), par_tb as( select * from seeyii_data_house.dwd_me_buss_per_slsdzxzz_base where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_slsdzxzz_base where modifytime is not null) and datastatus!='3' and isvalid='1' ), sour_df_59 as ( select a.*, b.compcode, b.compname from chi_tb as a join par_tb as b on a.uid=b.uid ), next_df AS ( SELECT date_format(certifTime, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106159' as eventtype,url,certifType,certifDomin,certifLevel from sour_df_59 ), add_desc_df as ( select *, concat(eventdate, '，公司获得水利水电咨询资质') as a, if((certifType is null) or (certifType =''),'',concat('，资质类型为',certifType)) as b, if((certifLevel is null) or (certifLevel =''),'',concat('，资质等级为',certifLevel)) as c, if((certifDomin is null) or (certifDomin =''),'',concat('，资质专业为',certifDomin)) as d, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), SJ000106159 as (select *, concat_ws('',a,b,c,d,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from SJ000106159 ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
SELECT  'SJ000106163',
 " with sour_df_63 as (select * from seeyii_data_house.dwd_me_buss_per_xslbxjgxkz where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_xslbxjgxkz where modifytime is not null) and datastatus!='3' and isvalid='1' ), next_df AS ( SELECT date_format(publishDate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106163' as eventtype,url,orgSecondClass,orgFirstClass from sour_df_63 ), add_desc_df as ( select *, concat(eventdate, '，公司获得新设立保险机构许可证') as a, if((orgFirstClass is null) or (orgFirstClass =''),'',concat('，一级机构类型为',orgFirstClass)) as b, if((orgSecondClass is null) or (orgSecondClass =''),'',concat('，二级机构类型为',orgSecondClass)) as c, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), SJ000106163 as (select *, concat_ws('',a,b,c,e) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from SJ000106163 ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231012' )
SELECT  'SJ000204007',
 " with spo_base AS ( SELECT distinct compcode, statdate AS eventdate FROM (SELECT *, row_number() OVER(PARTITION BY id ORDER BY filedate DESC) AS num FROM seeyii_data_house.dwd_me_trad_spo_base ) AS tmp1 WHERE num=1 AND datastatus != 3 and currStat='305' ), subsist_list as ( select distinct compcode, string(DATE_FORMAT(issuedate, 'yyyy-MM-dd')) as eventdate from seeyii_data_house.dwd_ms_base_bond_subsist_list where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_base_bond_subsist_list) AND datastatus != 3 ), sk_stock as ( select compcode, compname from seeyii_data_house.dwd_ms_base_sk_stock where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_base_sk_stock) AND datastatus != 3 ), join_tb as ( select * from ( select *, datediff(eventdate, next_eventdate) as num from ( select *,lead(eventdate, 1, eventdate) OVER(PARTITION BY subjectcode ORDER BY eventdate desc) AS next_eventdate from ( select a.compcode as subjectcode, a.compname as eventsubject, b.eventdate from sk_stock as a join spo_base as b on a.compcode=b.compcode union select a.compcode as subjectcode, a.compname as eventsubject, b.eventdate from sk_stock as a join subsist_list as b on a.compcode=b.compcode ) t ) t1 ) t2 where num <= 365 and num!=0 ), next_df as ( select *, '上市公司近1年融资2次以上。' as desc1, CAST(null AS STRING) as url, DATE_ADD(next_eventdate, 365) as expiredate from join_tb where eventdate is not null )  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000204007' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000204007' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from next_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231012' )
SELECT  'SJ000204008',
 " with spo_base AS ( SELECT compcode, projid, statdate FROM (SELECT *, row_number() OVER(PARTITION BY id ORDER BY filedate DESC) AS num FROM seeyii_data_house.dwd_me_trad_spo_base ) AS tmp1 WHERE num=1 AND datastatus != 3 and currStat='305' and finaAmount>=1 ), subsist_list as ( select compcode, fingerid,string(DATE_FORMAT(issuedate, 'yyyy-MM-dd')) as issuedate from seeyii_data_house.dwd_ms_base_bond_subsist_list where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_base_bond_subsist_list) AND datastatus != 3 and latestIssueSize>=100000000 ), sk_stock as ( select compcode, compname from seeyii_data_house.dwd_ms_base_sk_stock where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_base_sk_stock) AND datastatus != 3 ), join_tb as ( select a.compcode as subjectcode, a.compname as eventsubject, b.statdate as eventdate from sk_stock as a join spo_base as b on a.compcode=b.compcode union select a.compcode as subjectcode, a.compname as eventsubject, b.issuedate as eventdate from sk_stock as a join subsist_list as b on a.compcode=b.compcode ), next_df as ( select *, '上市公司近3个月单笔融资超过1亿人民币。' as desc1, CAST(null AS STRING) as url, DATE_ADD(eventdate, 90) as expiredate from join_tb where eventdate is not null )  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000204008' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000204008' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from next_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231012' )
SELECT  'SJ000204009',
 " with spo_base AS ( SELECT compcode, projid, statdate FROM (SELECT *, row_number() OVER(PARTITION BY id ORDER BY filedate DESC) AS num FROM seeyii_data_house.dwd_me_trad_spo_base ) AS tmp1 WHERE num=1 AND datastatus != 3 and currStat='305' ), subsist_list as ( select compcode, fingerId, string(DATE_FORMAT(issuedate, 'yyyy-MM-dd')) as issuedate from seeyii_data_house.dwd_ms_base_bond_subsist_list where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_base_bond_subsist_list) AND datastatus != 3 ), sk_stock as ( select compcode, compname from seeyii_data_house.dwd_ms_base_sk_stock where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_base_sk_stock) AND datastatus != 3 ), join_tb as ( select a.compcode as subjectcode, a.compname as eventsubject, b.statdate as eventdate from sk_stock as a join spo_base as b on a.compcode=b.compcode union select a.compcode as subjectcode, a.compname as eventsubject, b.issuedate as eventdate from sk_stock as a join subsist_list as b on a.compcode=b.compcode ), next_df as ( select *, '上市公司近3个月内有融资。' as desc1, CAST(null AS STRING) as url, DATE_ADD(eventdate, 90) as expiredate from join_tb where eventdate is not null )  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000204009' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000204009' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from next_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231012' )
SELECT  'SJ000204010',
 " with spo_base AS ( SELECT compcode, statdate as eventdate FROM (SELECT *, row_number() OVER(PARTITION BY id ORDER BY filedate DESC) AS num FROM seeyii_data_house.dwd_me_trad_spo_base ) AS tmp1 WHERE num=1 AND datastatus != 3 and currStat='305' ), subsist_list as ( select compcode, string(DATE_FORMAT(issuedate, 'yyyy-MM-dd')) as eventdate from seeyii_data_house.dwd_ms_base_bond_subsist_list where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_base_bond_subsist_list) AND datastatus != 3 ), sk_stock as ( select compcode, compname from seeyii_data_house.dwd_ms_base_sk_stock where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_base_sk_stock) AND datastatus != 3 ), join_tb as ( select * from ( select *,row_number() OVER(PARTITION BY subjectcode ORDER BY eventdate ASC) AS num from ( select a.compcode as subjectcode, a.compname as eventsubject, b.eventdate from sk_stock as a join spo_base as b on a.compcode=b.compcode union select a.compcode as subjectcode, a.compname as eventsubject, b.eventdate from sk_stock as a join subsist_list as b on a.compcode=b.compcode ) t ) t1 where num =1 ), next_df as ( select *, '上市公司近3个月有首次融资。' as desc1, CAST(null AS STRING) as url, DATE_ADD(eventdate, 90) as expiredate from join_tb where eventdate is not null )  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000204010' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000204010' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from next_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231012' )
SELECT  'SJ000204011',
 " with subsist_list as ( select compcode, from_unixtime(unix_timestamp(publishdate, 'yyyyMMdd'), 'yyyy-MM-dd') AS publishdate, CASE issueMode WHEN 'A' THEN '战略配售' WHEN 'AC' THEN '战略配售、网下询价配售' WHEN 'ACD' THEN '战略配售、网下询价配售、网上定价发行' WHEN 'ACDG' THEN '战略配售、网下询价配售、网上定价发行、吸收合并' WHEN 'B' THEN '定向配售' WHEN 'BC' THEN '定向配售、网下询价配售' WHEN 'BCD' THEN '定向配售、网下询价配售、网上定价发行' WHEN 'BE' THEN '定向配售、网下定价发行' WHEN 'C' THEN '网下询价配售' WHEN 'CD' THEN '网下询价配售、网上定价发行' WHEN 'D' THEN '网上定价发行' WHEN 'DE' THEN '网上定价发行、网下定价发行' WHEN 'E' THEN '网下定价发行' WHEN 'F' THEN '网上优先配售' WHEN 'FCD' THEN '网上优先配售、网下询价配售、网上定价发行' WHEN 'FDE' THEN '网上优先配售、网上定价发行' WHEN 'FHCD' THEN '网上优先配售、网下优先配售、网下询价配售、网上定价发行' WHEN 'FHDE' THEN '网上优先配售、网下优先配售、网上定价发行' WHEN 'G' THEN '吸收合并' WHEN 'H' THEN '网下优先配售' WHEN 'I' THEN '公司分立' WHEN 'Z' THEN '其他' WHEN '2' THEN '二级市场定价配售' WHEN '3' THEN '全额预缴款、摇号抽签、余款转存' WHEN 'CZ' THEN '网下询价发行和其他' WHEN '2D' THEN '二级市场定价配售,网上定价发行' WHEN 'BC2' THEN '定向配售,网下询价发行与二级市场定价配售相结合' WHEN '6' THEN '全额预缴款、比例配售、余款即退' WHEN '4' THEN '全额预缴款、摇号抽签、余款即退' WHEN 'BD' THEN '定向配售,网上定价发行' WHEN 'GD' THEN '比例换购,网上定价发行' WHEN '5' THEN '全额预缴款、比例配售、余款转存' WHEN '1' THEN '网上竞价发行' WHEN '0' THEN '与储蓄存单挂钩' WHEN '9' THEN '国际配售方式' WHEN 'BG' THEN '定向配售、吸收合并' WHEN 'AB' THEN '战略配售、定向配售' WHEN 'AE' THEN '战略配售、网下定价发行' WHEN 'J' THEN '发售以供认购(港股)' WHEN 'K' THEN '发售现有股份(港股)' WHEN 'L' THEN '配售(港股)' WHEN 'M' THEN '介绍上市(港股)' WHEN 'N' THEN '竞价发行' WHEN 'AD' THEN '战略配售、网上定价发行' ELSE '未知' END AS mappedIssueMode, CASE WHEN issueType = '02' THEN '增发' WHEN issueType = '06' THEN '增发（配套募集资金）' END AS mapped_issueType from seeyii_data_house.dwd_me_trad_sk_proaddiss where filedate in (select max(filedate) from seeyii_data_house.dwd_me_trad_sk_proaddiss) AND datastatus != 3 ), sk_stock as ( select compcode, compname from seeyii_data_house.dwd_ms_base_sk_stock where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_base_sk_stock) AND datastatus != 3 ), join_tb as ( select a.compcode as subjectcode, a.compname as eventsubject, b.publishdate as eventdate,mappedIssueMode,mapped_issueType from sk_stock as a join subsist_list as b on a.compcode=b.compcode ), next_df as ( select * ,concat('上市公司近3个月发布股票增发预案。' ,if((eventdate is null) or (eventdate =''), '' ,concat('公告发布时间：',eventdate)) ,if((mapped_issueType is null) or (mapped_issueType =''), '' ,concat('，发行类型：',mapped_issueType)) ,if((mappedIssueMode is null) or (mappedIssueMode =''), '' ,concat('，发行方式：',mappedIssueMode)) , '。' ) as desc1, CAST(null AS STRING) as url, DATE_ADD(eventdate, 90) as expiredate from join_tb where eventdate is not null )  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000204011' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000204011' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from next_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');




INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231114')
SELECT  'SJ000208003',
 " with subsist_list as ( select id, relieveDate as eventdate, ipType,tsSubType,ipName,invenName from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc) AND datastatus != 3 ), sk_stock as ( select id, compcode, compname from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc_se where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc_se) AND datastatus != 3 and typecode=1 ), join_tb as ( select a.compcode as subjectcode, a.compname as eventsubject, b.eventdate ,ipType,tsSubType,ipName,invenName, concat('100162','&#&',ipType,'&#&','3') as eigenvalue from sk_stock as a join subsist_list as b on a.id=b.id ), next_df as ( select *,CAST(null AS STRING) as url,CAST(null AS STRING) as expiredate, concat(eventdate,'，公司专利权质押合同解除登记') as a, if((ipType is null) or (ipType =''),'',concat('，专利类型为',ipType)) as b, if((tsSubType is null) or (tsSubType =''),'', concat('，细分事务分类为', tsSubType)) as c, if((ipName is null) or (ipName =''),'', concat('，专利名称为', ipName)) as d, if((eventsubject is null) or (eventsubject =''),'', concat('，质权人为', eventsubject)) as e, if((invenName is null) or (invenName =''),'', concat('，发明名称为', invenName)) as f, '。' g from join_tb where eventdate is not NULL ), final_df as (select *, concat_ws('', a,b,c,d,e,f,g) as desc1 from next_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000208003' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000208003' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');


INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231114' )
SELECT  'SJ000201001',
 " with main_df as ( select * from ( select *, row_number() over(partition by id order by filedate desc) as rnumber from seeyii_data_house.dwd_me_trad_ipo_base ) as tb where rnumber = 1 and datastatus != 3 ), sour_df as ( select * from ( select *, row_number() over(partition by id order by filedate desc) as rnumber from seeyii_data_house.dwd_me_trad_ipo_detail ) as tb where rnumber = 1 and datastatus != 3 ), all_df as ( select * from ( select *, lag(stat,1) over(partition by compcode, projid order by statdate desc, stat desc) as up_stat, lag(statdate,1) over(partition by compcode, projid order by statdate desc, stat desc) as up_statdate from ( select a.stat, a.statdate,a.projid, b.compname, b.compcode, b.brokname, b.prename,b.finaAmount,b.market, b.currstat, b.statdate as curdate from sour_df a join main_df b on a.projid=b.projid ) t ) t2 where stat in ('101', '102', '201') ), next_df AS ( SELECT date_format(statdate, 'yyyy-MM-dd') as eventdate, compcode as subjectcode, compname as eventsubject, case stat when '101' then 'SJ000201001' when '102' then 'SJ000201002' when '201' then 'SJ000201003' end as eventtype, CAST(up_statdate AS STRING) as expiredate, brokname, prename,finaAmount, case market when '101' then '上交所主板' when '201' then '深交所主板' when '301' then '创业板' when '302' then '创业板' when '401' then '科创板' when '501' then '北交所' end as market from all_df where statdate is not NULL and compcode is not null ), add_desc_df as ( select *, CAST(null AS STRING) as url, CAST(null AS STRING) as eigenvalue, concat('公司于',eventdate,'启动上市辅导') as a, if((prename is null) or (prename =''),'',concat('，辅导机构为：',prename)) as b, '。' c from next_df where eventtype='SJ000201001' union select *, CAST(null AS STRING) as url, CAST(null AS STRING) as eigenvalue, concat('公司于',eventdate,'完成上市辅导验收') as a, '，即将提交上市申请' as b, '。' c from next_df where eventtype='SJ000201002' union select *, CAST(null AS STRING) as url, concat( concat('100007','&#&',finaAmount,'&#&','3') ,'@@' ,concat('100008','&#&',market,'&#&','7') ) as eigenvalue, concat('公司提交的上市申请已于',eventdate,'被证监会受理，已进入排队状态') as a, if((brokname is null) or (brokname =''),'',concat('，保荐机构为：',brokname)) as b, '。' c from next_df where eventtype='SJ000201003' ), final_df as (select *, concat_ws('', a,b,c) as desc1 from add_desc_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');


INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231114' )
SELECT  'SJ000102006',
 " with source_dat AS ( select compname AS eventsubject, compcode AS subjectcode,projname,url,cetificationtime ,date_format(publishdate, 'yyyy-MM-dd') as eventdate ,case when `complevel` = '1' then '国家级' when `complevel` = '2' then '省级' when `complevel` = '3' then '市级' else null end as lev ,case when `rawtype` = '3' then '批建' when `rawtype` = '6' then '拟批建' when `rawtype` = '13' then '拟认定' when `rawtype` = '17' then '撤销' when `rawtype` = '18' then '认证' when `rawtype` = '25' then '保留院士专家工作站' when `rawtype` = '96' then '验收通过' when `rawtype` = '105' then '绩效考评合格' when `rawtype` = '106' then '限期整改' when `rawtype` = '107' then '拟认证' when `rawtype` = '108' then '需整改' when `rawtype` = '109' then '绩效考评优秀' when `rawtype` = '110' then '更名' when `rawtype` = '111' then '绩效考评不合格' when `rawtype` = '112' then '绩效考核良好' when `rawtype` = '119' then '绩效考评优合格' else null end as rawty from seeyii_data_house.dwd_ms_base_acade_expert_station_list where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_base_acade_expert_station_list) and compcode is not null AND publishdate is not null AND datastatus !=3 AND complevel is not NULL AND cetificationtime is not NULL and rawtype in ('3', '6', '18', '96', '105', '107', '109', '112', '119') ), des_dat as ( select eventsubject,subjectcode,eventdate,url ,concat( concat('100036','&#&',lev,'&#&','7') ,'@@' ,concat('100037','&#&',rawty,'&#&','7') ) as eigenvalue ,'SJ000102006' as eventtype ,CAST(null AS STRING) as expiredate ,concat('公司于',eventdate,'被认定为',cetificationtime,'年度的',lev,'院士专家工作站' ,if((rawty is null) or (rawty =''), '' ,concat('，类型为：' , rawty)) ,if((projname is null) or (projname =''), '' ,concat('，涉及项目为：' , projname)) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,'{fileDate}' as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime,eigenvalue ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,null as expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue ,datastatus,modifytime,filedate from ret_dat ",
 map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231114' )
SELECT  'SJ000102003',
 " with source_dat AS ( select compname AS eventsubject, compcode AS subjectcode,complevel as level,cetificationyear,labernm,url ,date_format(publishdate, 'yyyy-MM-dd') as eventdate, CASE WHEN rawType = '1' THEN '备案' WHEN rawType = '3' THEN '批建' WHEN rawType = '5' THEN '认定' WHEN rawType = '6' THEN '拟批建' WHEN rawType = '9' THEN '拟备案' WHEN rawType = '13' THEN '拟认定' WHEN rawType = '14' THEN '拟立项' WHEN rawType = '15' THEN '立项' WHEN rawType = '23' THEN '拟建设' WHEN rawType = '29' THEN '验收' WHEN rawType = '31' THEN '绩效考评' WHEN rawType = '33' THEN '评估验收' WHEN rawType = '37' THEN '拟组建' WHEN rawType = '34' THEN '培育' WHEN rawType = '38' THEN '组建' WHEN rawType = '39' THEN '拟奖补' END AS raw_type, CASE WHEN compLevel = '1' THEN '国家级' WHEN compLevel = '2' THEN '省级' WHEN compLevel = '3' THEN '市级' END AS comp_level ,case when labortype = '1' then '学科类国家重点实验室' when labortype = '2' then '企业国家重点实验室' when labortype = '3' then '省部共建国家重点实验室' when labortype = '4' then '省市共建重点实验室' else '' end as labt from seeyii_data_house.dwd_ms_base_key_labor_list where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_base_key_labor_list) and compcode is not null AND publishdate is not null AND datastatus !=3 AND complevel is not NULL AND cetificationyear is not NULL and labortype = '2' and rawType is not null ), base_dat AS ( select eventsubject, subjectcode,eventdate,level,cetificationyear,labt,url,labernm, raw_type,comp_level from( select eventsubject, subjectcode,level,cetificationyear, eventdate,labt,url,labernm, raw_type,comp_level , row_number() over (partition by subjectcode,level,cetificationyear order by eventdate desc) num2 from source_dat ) t where t.num2 = 1 ), des_dat as ( select eventsubject,subjectcode,eventdate,url ,'SJ000102003' as eventtype, concat( concat('100030','&#&',comp_level,'&#&','7') ,'@@' ,concat('100031','&#&',raw_type,'&#&','7') ) as eigenvalue ,CAST(null AS STRING) as expiredate ,concat('公司于',eventdate,'被认定为',cetificationyear,'年度的企业国家重点实验室' ,if((labernm is null) or (labernm =''), '' ,concat('，实验室名称为' , labernm)) ,if((raw_type is null) or (raw_type =''), '' ,concat('，类型为' , raw_type)) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,'{fileDate}' as filedate from base_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime,eigenvalue ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,null as expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue ,datastatus,modifytime,filedate from ret_dat ",
 map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231114' )
SELECT  'SJ000106008',
 " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, url ,compname AS eventsubject, compcode AS subjectcode,batch,techprod ,case when `complevel` = '1' then '国家级' when `complevel` = '2' then '省级' when `complevel` = '3' then '市级' else null end as lev ,case when rawtype = '0' then '取消资格' when rawtype = '1' then '认定' else null end as tp from seeyii_data_house.dwd_ms_base_nrg_save_list where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_base_nrg_save_list) and publishdate is not null and compcode is not NULL AND datastatus !=3 and rawtype = '1' ), des_dat as ( select eventsubject,subjectcode,eventdate,url ,'SJ000106008' as eventtype, concat('100073','&#&',lev,'&#&','7') as eigenvalue ,CAST(null AS STRING) as expiredate ,concat(eventdate,'，公司获评备案节能服务公司' ,if((techprod is null) or (techprod =''), '' ,concat('，主要节能业务及技术产品为',techprod)) ,if((tp is null) or (tp =''), '' ,concat('，类型为',tp)) ,if((batch is null) or (batch =''), '' ,concat('，批次为',batch)) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,'{fileDate}' as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime,eigenvalue ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,null as expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue ,datastatus,modifytime,filedate from ret_dat ",
 map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231114' )
SELECT  'SJ000106014',
 " with source_dat AS ( select date_format(pubtime, 'yyyy-MM-dd') as eventdate,puburl as url ,compname AS eventsubject, compcode AS subjectcode ,case when grade = '1' then '省级' when grade = '2' then '市级' else null end as grade ,case when certtype = '1' then '撤销' when certtype = '2' then '拟撤销' when certtype = '3' then '拟认定' when certtype = '4' then '拟认定后补' when certtype = '5' then '评估合格' when certtype = '6' then '认定' else null end as tp from seeyii_data_house.dwd_ms_base_poverty_leader_comp_list where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_base_poverty_leader_comp_list) and pubtime is not null and compcode is not NULL AND datastatus !=3 and certtype in ('3', '4', '5', '6') ), des_dat as ( select eventsubject,subjectcode,eventdate ,'SJ000106014' as eventtype ,concat('100074','&#&',grade,'&#&','7') as eigenvalue ,url ,CAST(null AS STRING) as expiredate ,concat(eventdate,'，公司获评扶贫龙头企业' ,if((tp is null) or (tp =''), '' ,concat('，认定类型为',tp)) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,'{fileDate}' as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime,eigenvalue ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat )  insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,null as expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue ,datastatus,modifytime,filedate from ret_dat ",
 map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');


INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231114' )
SELECT  'SJ000106019',
 " with source_dat AS ( select date_format(pubtime, 'yyyy-MM-dd') as eventdate, sourceurl as url ,compname AS eventsubject, compcode AS subjectcode ,case when grade = '1' then '省级' when grade = '2' then '国家级' else null end as grade from seeyii_data_house.dwd_ms_base_farm_leader_comp_list where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_base_farm_leader_comp_list) and pubtime is not null and compcode is not NULL AND datastatus !=3 and rawType in ('9', '3','1', '6', '2', '12') ), des_dat as ( select eventsubject,subjectcode,eventdate ,'SJ000106019' as eventtype ,concat('100075','&#&',grade,'&#&','7') as eigenvalue ,url ,CAST(null AS STRING) as expiredate ,concat(eventdate,'，公司获评农业产业化龙头企业' , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,'{fileDate}' as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime,eigenvalue ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat )  insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,null as expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue ,datastatus,modifytime,filedate from ret_dat ",
 map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');



INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231114' )
SELECT  'SJ000205003',
 " with source_dat AS ( select from_unixtime(unix_timestamp(publishdate,'yyyyMMdd'),'yyyy-MM-dd') as eventdate ,CAST(NULL AS STRING ) AS eventsubject, CAST(compcode AS STRING ) AS subjectcode,issuemode,newtotraiseamt ,case when issuetype = '02' then '增发' when issuetype = '06' then '增发（配套募集资金）' else null end as tp from seeyii_data_house.dwd_me_trad_sk_proaddiss where filedate in (select max(filedate) from seeyii_data_house.dwd_me_trad_sk_proaddiss) and publishdate is not NULL and compcode is not NULL AND datastatus !=3 AND isvalid=1 and isfinsuc=1 ), mt_ct_sys_const AS ( SELECT * FROM seeyii_data_house.dwd_mt_ct_sys_const WHERE datastatus!=3 AND constCode=37 and cValue !='' AND cValue IS NOT NULL ), deal_a AS ( select a.eventdate,a.eventsubject,a.subjectcode,a.tp,b.constvaluedesc,a.newtotraiseamt from source_dat as a join mt_ct_sys_const as b on a.issuemode=b.cValue ), des_dat as ( select eventsubject,subjectcode,eventdate ,'SJ000205003' as eventtype ,concat('100151','&#&',newtotraiseamt,'&#&','2') as eigenvalue ,CAST(null AS STRING) as url ,CAST(null AS STRING) as expiredate ,concat(eventdate,'，A股公司发布股票增发公告信息' ,if((tp is null) or (tp =''), '' ,concat('，发行类型为',tp)) ,if((constvaluedesc is null) or (constvaluedesc =''), '' ,concat('，发行方式为',constvaluedesc)) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,'{fileDate}' as filedate from deal_a ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime,eigenvalue ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat )  insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue ,datastatus,modifytime,filedate from ret_dat ",
 map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231114' )
SELECT  'SJ000209001',
 " with source_dat AS ( select from_unixtime(unix_timestamp(publishdate,'yyyyMMdd'),'yyyy-MM-dd') as eventdate ,CAST(NULL AS STRING ) AS eventsubject, CAST(compcode AS STRING ) AS subjectcode,graobj,totcashdv ,case when divitype = '0' then '不分配' when divitype = '1' then '现金分红' when divitype = '5' then '送股' when divitype = '8' then '转增' when divitype = '15' then '现金分红+送股' when divitype = '18' then '现金分红+转增' when divitype = '58' then '送股+转增' when divitype = '158' then '现金分红+送股+转增' when divitype = 'W' then '赠股' else null end as dictp ,case when graobjtype = '1' then '全体股东' when graobjtype = '2' then '流通股东' when graobjtype = '3' then '非流通股东' when graobjtype = '9' then '其他' when graobjtype = '4' then '无限售股东' when graobjtype = '5' then '有限售股东' else null end as gractp ,case when projecttype = '1' then '正案' when projecttype = '2' then '预案1' when projecttype = '3' then '预案2' when projecttype = '4' then '预案3' when projecttype = '5' then '预案4' when projecttype = '6' then '预案5' when projecttype = '7' then '预案6' when projecttype = '8' then '预案7' else null end as protp from seeyii_data_house.dwd_ms_sh_sk_divident where filedate in (select max(filedate) from seeyii_data_house.dwd_ms_sh_sk_divident) and publishdate is not NULL and compcode is not NULL AND datastatus !=3 AND isvalid=1 and projecttype=1 ), des_dat as ( select eventsubject,subjectcode,eventdate ,'SJ000209001' as eventtype ,concat('100152','&#&',totcashdv,'&#&','2') as eigenvalue ,CAST(null AS STRING) as url ,CAST(null AS STRING) as expiredate ,concat(eventdate,'，A股公司发布分红公告' ,if((dictp is null) or (dictp =''), '' ,concat('，权益类型为',dictp)) ,if((gractp is null) or (gractp =''), '' ,concat('，发放对象类型为',gractp)) ,if((graobj is null) or (graobj =''), '' ,concat('，发放对象为',graobj)) ,if((protp is null) or (protp =''), '' ,concat('，方案类型为',protp)) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,'{fileDate}' as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime,eigenvalue ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat )  insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue ,datastatus,modifytime,filedate from ret_dat ",
 map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231114' )
SELECT  'SJ000310002',
 " with source_dat AS ( select date_format(tsddate, 'yyyy-MM-dd') as eventdate,ipname,iptype,chgaftname AS eventsubject,chgaftcode as subjectcode,chgbefname from (select tsddate,ipname,iptype,chgaftname,chgaftcode,chgbefname,datastatus,isvalid,tsSubType ,row_number() over (partition by sourceid order by filedate desc) num from seeyii_data_house.dwd_me_buss_ip_tst_at ) as t where t.num=1 and t.tsddate is not NULL and t.chgaftcode is not NULL AND t.datastatus !=3 and t.isvalid =1 and t.tsSubType in ('专利申请权的转移','专利权的转移') ), des_dat as ( select eventsubject,subjectcode,eventdate ,'SJ000310002' as eventtype ,CAST(null AS STRING) as url ,CAST(null AS STRING) as expiredate ,concat('100165','&#&',iptype,'&#&','3') as eigenvalue ,concat(eventdate,'，公司发布购买专利信息' ,if((chgbefname is null) or (chgbefname =''), '' ,concat('，交易对手为',chgbefname)) ,if((ipname is null) or (ipname =''), '' ,concat('，专利名称为',ipname)) ,if((iptype is null) or (iptype =''), '' ,concat('，专利类型为',iptype)) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,'{fileDate}' as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime,eigenvalue ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat )  insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue ,datastatus,modifytime,filedate from ret_dat ",
 map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231114' )
SELECT  'SJ000205001',
 " with source_dat AS ( select date_format(initinfopubld, 'yyyy-MM-dd') as eventdate ,CAST(null AS STRING ) AS eventsubject,compcode as subjectcode,plaProceeds ,regexp_replace(issuepurpose, '。', '') as issuepurpose ,regexp_replace(pricingmodel, '。', '') as pricingmodel ,regexp_replace(plaproceeduse, '。', '') as plaproceeduse from seeyii_data_house.dwd_me_trad_nq_sharesissue where filedate in (select max(filedate) from seeyii_data_house.dwd_me_trad_nq_sharesissue) and initinfopubld is not NULL and compcode is not NULL AND datastatus !=3 and EventProcedure=30 ), des_dat as ( select eventsubject,subjectcode,eventdate ,'SJ000205001' as eventtype ,CAST(null AS STRING) as url ,CAST(null AS STRING) as expiredate ,concat('100095','&#&',plaProceeds,'&#&','2') as eigenvalue ,concat(eventdate,'，三板公司发布股票发行信息' ,if((issuepurpose is null) or (issuepurpose =''), '' ,concat('，发行目的为',issuepurpose)) ,if((pricingmodel is null) or (pricingmodel =''), '' ,concat('，定价方式为',pricingmodel)) ,if((plaproceeduse is null) or (plaproceeduse =''), '' ,concat('，募集资金用途为',plaproceeduse)) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,'{fileDate}' as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime,eigenvalue ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat )  insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue ,datastatus,modifytime,filedate from ret_dat ",
 map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231114' )
SELECT  'SJ000106091',
 " with base_df AS ( select compcode as subjectcode, compname as eventsubject,minename,projecttype,miningtype,miningway, date_format(noticedate, 'yyyy-MM-dd') as eventdate, date_format(invaliddate, 'yyyy-MM-dd') as expiredate, url from seeyii_data_house.dwd_me_buss_per_ckqdj where filedate in (select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_per_ckqdj) and datastatus != 3 and isvalid = 1 and noticedate is not null and noticedate != '' and compcode is not null and projectType not like '%注销%' ), next_df AS ( select *, if((minename is null) or (minename =''),'',concat('，矿山名称为',minename)) as a0, if((projecttype is null) or (projecttype =''),'',concat('，项目类型为',projecttype)) as a1, if((miningtype is null) or (miningtype =''),'',concat('，开采主矿种为',miningtype)) as a2, if((miningway is null) or (miningway =''),'',concat('，开采方式为',miningway)) as a3 from base_df ), final_df AS ( select *, concat(eventdate,'，公司获得采矿权登记',a0,a1,a2,a3,'。') as desc1 from next_df )  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000106091' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000106091' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,null as eigenvalue, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');



INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20231113' )
SELECT  'SJ000210002',
 " with source_dat AS ( select null AS eventsubject, compcode AS subjectcode,null as url,regexp_replace(format_number(entrustFinanceSum, 2), ',', '') as es ,date_format(entrustFinanceEndDate, 'yyyy-MM-dd') as expiredate, date_sub(date_format(entrustFinanceEndDate, 'yyyy-MM-dd'), 60) as eventdate from seeyii_data_house.dwd_me_trad_lc_entrustinv where filedate in (select max(filedate) from seeyii_data_house.dwd_me_trad_lc_entrustinv) and compcode is not null AND entrustFinanceEndDate is not null AND datastatus !=3 ), des_dat as ( select eventsubject,subjectcode,eventdate,url, 'SJ000210002' as eventtype, concat('100153','&#&',es,'&#&','2') as eigenvalue, CAST(null AS STRING) as expiredate, concat('A股公司的委托理财将于2个月后到期，委托截止日为',expiredate ,if((es is null) or (es =''), '' ,concat('，涉及金额：', es,'元')) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,'{fileDate}' as filedate from source_dat ), ret_dat as ( select eventsubject,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime,eigenvalue ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat )  insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate,null as expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue ,datastatus,modifytime,filedate from ret_dat ",
 map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo_dev.dwd_me_buss_event_oppo_config_lqp PARTITION(filedate = '20231113' )
SELECT  'SJ000205004',
 " with sk_stock as ( select compcode as subjectcode, compname as eventsubject,date_format(publishDate, 'yyyy-MM-dd') as eventdate,bondName, proposedAmount from seeyii_emr_bus.dwd_mm_cn_bond_audit_prj where filedate in (select max(filedate) from seeyii_emr_bus.dwd_mm_cn_bond_audit_prj) AND datastatus != 3 and isvalid=1 ), next_df as ( select *,CAST(null AS STRING) as url,CAST(null AS STRING) as expiredate, concat('100012','&#&',proposedAmount,'&#&','3') as eigenvalue, concat(eventdate,'公司新增债券发行审核申请') as a, if((bondName is null) or (bondName =''),'',concat('，债券名称为',bondName)) as b, if((proposedAmount is null) or (proposedAmount =''),'', concat('，拟发行金额', proposedAmount, '亿元')) as c, '。' d from sk_stock where eventdate is not NULL ), final_df as (select *, concat_ws('', a,b,c,d) as desc1 from next_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000205004' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000205004' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"3", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240103' )
SELECT  'SJ000205004',
 " with sk_stock as ( select compcode as subjectcode, compname as eventsubject,date_format(publishDate, 'yyyy-MM-dd') as eventdate,bondName, proposedAmount from seeyii_data_tert.dwd_mm_cn_bond_audit_prj where filedate in (select max(filedate) from seeyii_data_tert.dwd_mm_cn_bond_audit_prj) AND datastatus != 3 and isvalid=1 ), next_df as ( select *,CAST(null AS STRING) as url,CAST(null AS STRING) as expiredate, concat('100012','&#&',proposedAmount,'&#&','3') as eigenvalue, concat(eventdate,'公司新增债券发行审核申请') as a, if((bondName is null) or (bondName =''),'',concat('，债券名称为',bondName)) as b, if((proposedAmount is null) or (proposedAmount =''),'', concat('，拟发行金额', proposedAmount, '亿元')) as c, '。' d from sk_stock where eventdate is not NULL ), final_df as (select *, concat_ws('', a,b,c,d) as desc1 from next_df)  insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000205004' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000205004' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
 map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
