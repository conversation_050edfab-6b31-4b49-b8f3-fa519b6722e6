# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/5/31
from collections import defaultdict

from core.excel_base import ExcelBase


class IPOArea(ExcelBase):
    def __init__(self):
        super(IPOArea, self).__init__()
        self.market = {
            "101": "主板",
            "201": "中小板",
            "301": "创业板",
            "302": "创业板",
            "401": "科创板"}
        self.stat = {
            "101": "启动上市辅导",
            "102": "完成辅导验收",
            "103": "终止上市辅导",
            "201": "已受理",
            "202": "已问询",
            "203": "已反馈",
            "204": "预披露",
            "205": "更新预披露",
            "206": "中止",
            "207": "终止（审核不通过）",
            "208": "终止（撤回）",
            "209": "发审会通过",
            "210": "发审会未通过",
            "211": "暂缓表决",
            "212": "上市委会议通过",
            "213": "上市委会议未通过",
            "214": "暂缓审议",
            "215": "复审委会议通过",
            "216": "复审委会议未通过",
            "217": "提交注册",
            "218": "注册生效",
            "219": "不予注册",
            "220": "终止注册",
            "221": "证监会核准",
            "222": "证监会不予核准",
            "223": "补充审核",
            "301": "已发行上市",
            "302": "发行失败",
            "303": "发行暂缓"}
        self.exchange_map = {
            "101": "上交所",
            "201": "深交所"}
        self.list_fetch = [
            "id",
            "projId",
            "compName",
            "shortName",
            "finaAmount",
            "reguInstitution",
            "preName",
            "brokName",
            "acctName",
            "lawName",
            ("exchange", "exchange", lambda x: self.exchange_map[str(x)]),
            ("market", "market", lambda x: self.market[str(x)]),
            "acceDate",
            ("currStat", "currStat", lambda x: self.stat[str(x)]),
            "statDate",
            "fingerId",
            "dataStatus",
            "createTime",
            "modifyTime"]

    def process(self):
        result = list()
        area = self.query_cmp_area()
        ipo_item = self.query_ipo_data()
        for name, items in ipo_item.items():
            if name not in area:
                continue
            result.extend(items)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            "compName": ("公司名称", 2),
            "shortName": ("公司简称", 3), "finaAmount": ("募集资金(亿元)", 4),
            "reguInstitution": ("证监局", 5), "preName": ("辅导机构", 6), "brokName": ("保荐机构", 7),
            "acctName": ("会计师事务所", 8), "lawName": ("律师事务所", 9),
            "exchange": ("交易所", 10), "market": ("板块", 11), "acceDate": ("受理日期", 12),
            "currStat": ("最新状态", 13), "statDate": ("最新状态时间", 14)}
        self._excel_name = self.name_add_date("天津市IPO数据.xlsx")
        self.save_to_excel(field_cfg, {"Sheet": result})

    def query_cmp_area(self):
        result = set()
        sql_statement = """SELECT compName from sy_cd_ms_base_comp_geo 
        where provinceCode='120000' and dataStatus !=3;"""
        query_schema = dict(db_key="tidb_135", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result.add(item["compName"])
        return result

    def query_ipo_data(self):
        result = defaultdict(list)
        sql_statement = """SELECT * from dws_me_trad_ipo_base where dataStatus !=3"""
        query_schema = dict(db_key="seeyii_db", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            item_result = self.fetch_dict(item, self.list_fetch)
            result[item_result["compName"]].append(item_result)
        return result


if __name__ == '__main__':
    p = IPOArea()
    p.process()
