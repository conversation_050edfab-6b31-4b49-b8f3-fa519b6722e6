# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/11
import json
import http.client
import traceback

from collections import defaultdict
from core.excel_base import ExcelBase
from moduler.base_info_api_thread import BaseInfoMaster
from moduler.company_cyl_and_industry import ChinaToExcel
from moduler.company_gb_industry import CompanyGBIndustry


class CYLBaseInfo(ExcelBase):
    def __init__(self):
        super(CYLBaseInfo, self).__init__()
        self.cyl = ChinaToExcel()
        self.base = BaseInfoMaster()
        self.gb_ind = CompanyGBIndustry()
        self.ind_list = [
            "IT教育培训", "K12基础教育", "K12特色教育", "安全生产培训", "报考升学服务", "出国留学", "创客教育", "儿童艺术教育", "儿童早教机构",
            "儿童早教内容服务", "儿童早教信息系统", "高等教育机构服务", "高等教育应试辅导", "家庭培训", "家校互动平台系统", "教育机构", "教育机构信息系统",
            "教育考试测评", "金融行业培训", "考试题库学习系统", "科普教育", "培训招生信息平台", "汽车职业培训", "亲子娱乐服务", "时尚艺术培训", "特色教育内容服务",
            "医学交流培训", "游学旅行服务", "语言学习", "职业培训服务", "在线教育系统"]

    def process(self, *args, **kwargs):
        result = list()
        tmp_dict = defaultdict(list)
        name_items = self.cyl.run(["教育产业链", "网络教育产业链"])
        for item in name_items:
            ind = item["second_industry_name"]
            if ind not in self.ind_list:
                continue
            tmp_dict[item["company_name"]].append(item)
        name_list = list(tmp_dict.keys())
        base_info_dict = self.base.run(name_list)
        gb_ind_dict = self.gb_ind.run(name_list)
        for name, cyl_items in tmp_dict.items():
            base_info = base_info_dict[name]
            mp_data = self.query_pp_info(name)
            mp = mp_data.get("content")
            # 添加科技型企业字段
            self.base_info_process(base_info)
            for cyl in cyl_items:
                cyl.update(base_info)
                cyl.update(gb_ind_dict.get(name, dict()))
                if mp:
                    cyl["mp"] = ",".join(mp[:12])
                result.append(cyl)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'c_name': ('产业链名称', 0),
            'second_industry_name': ('视野行业名称', 1),
            'companyName': ('公司名称', 2),
            'market': ('资本市场', 3),
            'reg_province': ('所在地省', 4),
            'reg_city': ('所在地市', 5),
            'reg_district': ('所在地县区', 6),
            'reg_location': ('注册地址', 7),
            'estiblish_time': ('注册时间', 8),
            'reg_capital': ('注册资本', 9),
            'legal_person_name': ('法定代表人', 10),
            'ind_m': ('国民经济行业（门）', 11),
            'ind_d': ('国民经济行业（大）', 12),
            'ind_z': ('国民经济行业（中）', 13),
            'licence': ('持有证照情况', 14),
            'kjx': ('科技认定情况', 15),
            'mp': ('品牌名称', 16),
            'reg_status': ('公司状态', 17),
        }
        self._excel_name = self.name_add_date("教育产业链POC.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    @staticmethod
    def base_info_process(base_info):
        kj = list()
        company_business = base_info.get('company_business', "")
        bus_list = company_business.split(",")
        for item in bus_list:
            if item == "科技型中小企业":
                kj.append("科技型中小企业")
            if item == "高新技术企业":
                kj.append("高新技术企业")
        if kj:
            base_info["kjx"] = ",".join(kj)

    @staticmethod
    def query_pp_info(raw_name):
        result_list = list()
        headers = {"Content-type": "application/x-www-form-urlencoded",
                   "Accept": "text/plain"}
        httpClient = http.client.HTTPConnection("10.10.128.185", 9102, timeout=30)
        # httpClient = http.client.HTTPConnection("60.205.212.21", 9102, timeout=30)
        cookie, limit_num = None, 20
        while True:
            try:
                request_1_1 = {
                    "user_id": "lqp",
                    # "limit_num": limit_num,
                    "company": raw_name}
                if cookie is not None:
                    request_1_1["cookie"] = cookie
                params = json.dumps(request_1_1)
                httpClient.request(
                    "POST", "/v2.0/companies/trademark_name_list", params, headers)
                response = httpClient.getresponse()
                result = response.read().decode("utf-8")
                result = json.loads(result)
                if result["response_state"] != 1:
                    raise Exception("request err.param={}".format(params))
                response_content = result["response_content"]
                return response_content
            except Exception as exc:
                err_msg = traceback.format_exc()
                err_msg = " ## ".join(err_msg.split("\n"))
                err_msg = " ## ".join([err_msg, str(exc)])
                print(err_msg)
                break
        if httpClient is not None:
            httpClient.close()
        return result_list


if __name__ == '__main__':
    p = CYLBaseInfo()
    p.process()
