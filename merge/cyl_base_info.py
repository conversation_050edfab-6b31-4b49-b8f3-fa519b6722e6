# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/11
import json
import http.client
import traceback
import os
import json
import decimal
import xlsxwriter
from datetime import datetime, date

from cfg.config import out_file_path

from collections import defaultdict
from core.excel_base import ExcelBase
from moduler.base_info_api_thread import BaseInfoMaster
from moduler.company_cur_name import CurName
from moduler.company_cyl_and_industry import ChinaToExcel
from moduler.company_gb_industry import CompanyGBIndustry
from utils.datetime_util import DatetimeUtil


class CYLBaseInfo(ExcelBase):
    def __init__(self):
        super(CYLBaseInfo, self).__init__()
        self.cyl = ChinaToExcel()
        self.base = BaseInfoMaster()
        self.gb_ind = CompanyGBIndustry()
        self.alias = CurName()

    def process(self, *args, **kwargs):
        result = list()
        tmp_dict = defaultdict(list)
        name_items = self.cyl.run(["汽车制造产业链", "新能源汽车产业链"])
        # name_items = self.cyl.run(["医疗产业链", "医药产业链"])
        for cyl_item in name_items:
            company_name = cyl_item["company_name"]
            cyl_alias = self.alias.run(company_name)
            if cyl_alias:
                cur_n = cyl_alias[0]["cur_name"]
            else:
                cur_n = company_name
            tmp_dict[cur_n].append(cyl_item)

        name_list = list(tmp_dict.keys())
        base_info_dict = self.base.run(name_list)
        for name, cyl_items in tmp_dict.items():
            base_info = base_info_dict[name]
            for cyl in cyl_items:
                cyl.update(base_info)
                result.append(cyl)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'cyl_name': ('产业链名称', 0),
            'second_industry_name': ('视野行业名称', 1),
            'companyName': ('公司名称', 2),
            'reg_location': ('注册地址', 3),
            'estiblish_time': ('注册时间', 4),
            'reg_capital': ('注册资本', 5),
            'phone_number': ('联系电话', 6),
            'company_nature': ('性质标签', 7),
            'company_business': ('业务亮点', 8),
            'business_scope': ('经营范围', 9)}
        self._excel_name = self.name_add_date("汽车产业链POC.xlsx")
        output_file_path = os.path.join(out_file_path, self._excel_name)
        xls_file = xlsxwriter.Workbook(output_file_path)
        num = 0
        for idx in range(0, len(result), 1000000):
            sheet_list = result[idx:idx + 1000000]
            num += 1
            xls_sheet = xls_file.add_worksheet(name="sheet{}".format(num))
            self._save_to_excel(field_cfg, xls_sheet, sheet_list)
        xls_file.close()

    @staticmethod
    def _save_to_excel(field_dict, xls_sheet, item_list):
        for title in field_dict.values():
            xls_sheet.write(0, title[1], title[0])
        row_no = 1
        for result_item in item_list:
            for field_name, title in field_dict.items():
                field_value = result_item.get(field_name, "")
                if isinstance(field_value, decimal.Decimal):
                    field_value = float(field_value)
                if isinstance(field_value, datetime):
                    field_value = DatetimeUtil.date_to_str(field_value)
                if isinstance(field_value, date):
                    field_value = field_value.strftime("%Y-%m-%d")
                if not isinstance(field_value, str):
                    field_value = json.dumps(field_value, ensure_ascii=False)
                if field_value == "null":
                    continue
                xls_sheet.write(row_no, title[1], field_value)
            row_no += 1
        print("finished.")


if __name__ == '__main__':
    p = CYLBaseInfo()
    p.process()
