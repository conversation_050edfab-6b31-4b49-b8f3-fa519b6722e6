# -*- encoding:utf-8 -*-
import numpy as np
import pandas as pd
import os
from sklearn import preprocessing
import matplotlib.pyplot as plt
import sys
import argparse
import math
from glob import glob
from scipy.interpolate import make_interp_spline
from mpl_toolkits.axisartist.parasite_axes import HostAxes, ParasiteAxes
import mpl_toolkits.axisartist as AA

class CalcData():  # 数据清洗函数类
    """
    数据读取与筛选计算
    file: (str)------读取的excel和csv文件的路径，例：'F:\lry-exe\注册资本规模(1).csv'
    column_name：(str)------读取文件列名
    formula:(str)------函数名，mad, log, zhengtai, precent
    number：(int)------参数信息，n,  底数, μ倍数 ,   百分比数值
    ------
    结果返回：以list数据类型返回
    """
    def __init__(self):
        pass

    # 单步清洗函数，可循环套用
    def process(self,file,column_name,formula,number):
        dataFrame_data = self.read_data(file)
        yuan_data = np.array(dataFrame_data.get(column_name))
        data = self.filter_data(yuan_data)
        # data = list(filter(None, [i for i in data]))  # 去除数据中存在的空值和零情况,容易去除部分需要的零值，注意
        if formula == 'log':
            before_list, middle_list, after_list = self.log_data(number, data)
        elif formula == 'mad':
            before_list, middle_list, after_list = self.mad_calc(number, data)
        elif formula == 'precent':
            before_list, middle_list, after_list = self.percent_calc(number, data)
        elif formula == 'zhengtai':
            before_list, middle_list, after_list = self.zheng_calc(number, data)
        else:
            before_list, middle_list, after_list = [], list(data), []
        return before_list, middle_list, after_list

    # 文件读取，仅支持excel和csv
    def read_data(self, file):
        file_type = file.split('.')[-1]
        if file_type == 'csv':
            fn = pd.read_csv(file)
        else:
            fn = pd.read_excel(file)
        return fn

    # log 函数数据处理
    def log_data(self, number, data):
        data = list(data)
        if number == 1:
            return []
        elif (type(number) is str) & ('e' in str(number)):
            if number == 'e':
                number = math.e
            else:
                if int(number.split('e')[0]) < 0:  # 防止取对数时取到负数报错
                    return []
                number = int(number.split('e')[0]) * math.e

        floor_value = np.log(number)
        result_list = [np.log(i+1)/floor_value for i in data]
        return [], result_list, []

    # 百分比数据清晰法
    def percent_calc(self, percent_value, data):
        list_len = len(data)
        number = math.floor(list_len * percent_value / 100)
        sort_data = np.array(sorted(data))
        before_list = list(sort_data[:number])
        middle_list = list(sort_data[number:list_len - number - 1])
        after_list = list(sort_data[list_len - number:])
        return before_list, middle_list, after_list

    # mad函数裁剪
    def mad_calc(self, n_value, data):
        median_value = np.median(data)
        trans_data = list(map(lambda x: abs(x-median_value), data))
        trans_median = np.median(trans_data)
        uper_value = median_value + int(n_value) * abs(trans_median)
        down_value = median_value - int(n_value) * abs(trans_median)
        sort_data = np.array(sorted(data))
        before_list = list(sort_data[sort_data < down_value])
        middle_list = list(sort_data[(sort_data >= down_value) & (sort_data < uper_value)])
        after_list = list(sort_data[sort_data >= uper_value])
        return before_list, middle_list, after_list

    # 正态数据裁剪
    def zheng_calc(self, double_value, data):
        data = np.array(data)  # 防止传入列表非ndarray类型无法处理
        u = np.mean(data)  # 均值μ
        sig = np.std(data)  # 标准差δ

        down_value = u - int(double_value) * sig
        uper_value = u + int(double_value) * sig
        sort_data = np.array(sorted(data))
        before_list = list(sort_data[sort_data < down_value])
        middle_list = list(sort_data[(sort_data >= down_value) & (sort_data < uper_value)])
        after_list = list(sort_data[sort_data >= uper_value])

        # result_list = list(np.exp(-(crop_data - u) ** 2 / (2 * sig ** 2)) / (math.sqrt(2 * math.pi) * sig))  #正态函数
        return before_list, middle_list, after_list

    # 数据标准化
    def standard_calc(self, data):
        standard_data = np.array(data).reshape(-1, 1)
        Standard = preprocessing.StandardScaler()
        biaozhun_list = list(Standard.fit_transform(standard_data).reshape(1, -1)[0])
        return [], biaozhun_list, []

    # 数据归一化
    def minmax_calc(self, data):
        try:
            minmax_data = np.array(data).reshape(-1, 1)
            MinMax = preprocessing.MinMaxScaler()
            guiyi_list = list(MinMax.fit_transform(minmax_data).reshape(1, -1)[0])
            return [], guiyi_list, []
        except ValueError:
            return [], list(np.ones(len(data))), []

    # logistic评分归一
    def logistic_score(self, logistic_value, biaozhun_data):
        logistic = [1 / (1 + np.power(math.e, -logistic_value * x)) for x in biaozhun_data]
        return [], logistic, []

    # 立方投射
    def t_calc(self, logistic_data):
        t_list = [np.power(i, 3) for i in logistic_data]
        return [], t_list, []

    # 线性区间映射[4-10]
    def z_calc(self, t_data):
        z_list = [i * 6 + 4 for i in t_data]
        return [], z_list, []

    # 线性区间映射[value-10]
    def z_di_calc(self, t_data, value):
        z_list = [i * (10 - value) + value for i in t_data]
        return [], z_list, []

    # fenduan区间映射[value,value+0.99]
    def z_fenduan_calc(self, t_data, value):
        z_list = [(i-value)*0.99/(10-value)+value for i in t_data]
        return [], z_list, []

    # 线性区间映射[4-7]
    def z_four_calc(self, t_data):
        z_list = [i * 3 + 4 for i in t_data]
        return [], z_list, []

    # 线性区间映射 min_value = （1-公司覆盖度）*6+4
    def z_min_calc(self, t_data, fugai_value):
        min_value = (1 - fugai_value) * 6 + 4
        z_list = [i * (10 - min_value) + min_value for i in t_data]
        return [], z_list, []

    # 特殊数值分数反转
    def z_fan(self, z_data):
        z_list = [10 - i + 4 for i in z_data]
        return [], z_list, []

    # 去掉nan值
    def filter_data(self,data):
        '''
        替换读取excel的脏数据为空
        :param data_list:
        :return:
        '''
        data = list(data)
        result_list = list()
        for value in data:
            if value is None:
                value = ""
            if value is np.nan:
                value = ""
            if value is pd.NaT:
                value = ""
            if str(value) == "nan":
                value = ""
            if value != "":
                result_list.append(value)
        return result_list


# plt绘图类
class HuiTu():
    """
    data_list：(list)------数据列表
    bins：(int)------横轴的列数
    title:(str)------产品标题
    xlabel:(str)------横轴标签
    ylabel:(str)------纵轴标签
    file_dir=None:(str)------文件保存目录
    is_output_coverage=False:(bool)------是否进行图片保存
    ------
    进行图片开发环境展示，并可进行自己指定路径的图片保存，默认为当前文件夹
    """

    def __init__(self):
        pass

    def process(self, data_list, bins, title, xlabel, ylabel, file_dir=None, is_output_coverage=False):
        self.drawing(data_list, bins)

        self.setting_text(title, xlabel, ylabel, file_dir, is_output_coverage)

    # 绘图
    def drawing(self, data_list, bins):
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
        # plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
        plt.figure(figsize=(35, 18))
        max_value = math.ceil(np.max(data_list))
        min_value = math.floor(np.min(data_list))

        # 处理同值数据
        if len(set(data_list)) == 1:
            plt.figure(figsize=(35, 18))
            x = [list(set(data_list))[0]]  # 横坐标，分组,
            y = [len(data_list)]

            for i in range(1,bins):
                if 10 in set(data_list):
                    x.append(list(set(data_list))[0] - i)
                else:
                    x.append(list(set(data_list))[0]+i)
                y.append(0)

            dd_value = (np.max(y) / 1.1) - (np.max(y) / 1.2)
            for x1, y1 in zip(x, y):  # 绘制条形数值
                plt.text(x1, y1 + 0.05, y1, fontsize=25, ha='center', va='bottom')
            plt.text(bins - 1.5, np.max(y) / 1.1, f'平均值:{format(np.round(np.mean(data_list), 2), ",")}', fontsize=30,
                     ha='left', va='bottom')
            plt.text(bins - 1.5, np.max(y) / 1.1 - dd_value, f'标准差:{format(np.round(np.std(data_list), 2), ",")}',
                     fontsize=30,
                     ha='left', va='bottom')
            plt.text(bins - 1.5, np.max(y) / 1.1 - 2 * dd_value,
                     f'中位数:{format(np.round(np.median(data_list), 2), ",")}', fontsize=30,
                     ha='left', va='bottom')
            plt.text(bins - 1.5, np.max(y) / 1.1 - 4 * dd_value, f'企业数:{format(len(data_list), ",")}', fontsize=30,
                     ha='left', va='bottom')
            plt.text(bins - 1.5, np.max(y) / 1.1 - 3 * dd_value,
                     f'值域:[{format(np.round(np.min(data_list), 2), ",")}, {format(np.round(np.max(data_list), 2), ",")}]',
                     fontsize=30, ha='left', va='bottom')

            if max_value > 10000000:
                if bins > 8:
                    plt.xticks(rotation=350)
                plt.text(bins, -50, '(千万)', fontsize=25, ha='center', va='bottom')
            elif max_value > 100000:
                if bins > 8:
                    plt.xticks(rotation=350)
                plt.text(bins, -50, '(十万)', fontsize=25, ha='center', va='bottom')
            elif max_value > 10000:
                if bins > 8:
                    plt.xticks(rotation=350)
                plt.text(bins, -50, '(万)', fontsize=25, ha='center', va='bottom')
            plt.bar(x, y)



            # plt.rcParams['font.size'] = 50
            # plt.pie(list(set(data_list)),explode=[0.02],autopct='%1.1f%%', labels=[f'{list(set(data_list))[0]}'],
            #         shadow=True, startangle=90,colors = ("c", "r", "y"))
            # # plt.ticklabel_format(axis="y", style="sci", scilimits=(0, 0))  # 科学计数法设置y轴
            # plt.tick_params(labelsize=23)

            return

        div = [math.floor(np.min(data_list))]  # 为防止开始没数值报错
        # div = [np.min(data_list)]  # 为防止开始没数值报错,查看起始最小值


        x = []  # 横坐标，分组,
        y = []  # 纵坐标，频率，初始化为与分组同等数量的0
        j = 1
        cha_value = (max_value - min_value) / bins
        # i = max_value / bins
        i = math.floor(np.min(data_list)) + cha_value

        while i <= (max_value+cha_value/3) and cha_value != 0:
            div.append(round(i, 2))
            if max_value > 10000000:
                x.append("[" + '%.2f'%(div[j - 1]/10000000) + "," + '%.2f'%(div[j]/10000000) + ")")
            elif max_value > 100000:
                x.append("[" + '%.2f'%(div[j - 1]/100000) + "," + '%.2f'%(div[j]/100000) + ")")
            elif max_value > 10000:
                x.append("[" + '%.2f'%(div[j - 1]/10000) + "," + '%.2f'%(div[j]/10000) + ")")
            else:
                x.append("[" + '%.2f'%(div[j - 1]) + "," + '%.2f'%(div[j]) + ")")
            y.append(0)
            i += cha_value
            j += 1


        # 分拣数据
        for item in data_list:
            for i in range(0, len(div) - 1):
                if i == len(div) - 2:
                    if item >= div[i] and item <= div[i + 1]:
                        y[i] += 1
                        break
                if item >= div[i] and item < div[i+1]:
                    y[i] += 1
                    break


        # # 转换为频率
        # for i in range(len(y)):
        #     y[i] = round(y[i] / 1000, 3) * 100  # 化为百分比

        plt.bar(x, y)
        if max_value > 10000000:
            self.set_right_label(x, y, data_list, 10000000,bins)
        elif max_value > 100000:
            self.set_right_label(x, y, data_list, 100000,bins)
        elif max_value > 10000:
            self.set_right_label(x, y, data_list, 10000,bins)
        else:
            self.set_right_label(x, y, data_list, 1,bins)
        if max_value > 10000000:
            if bins > 8:
                plt.xticks(rotation=350)
            plt.text(bins, -50, '(千万)', fontsize=25, ha='center', va='bottom')
        elif max_value > 100000:
            if bins > 8:
                plt.xticks(rotation=350)
            plt.text(bins, -50, '(十万)', fontsize=25, ha='center', va='bottom')
        elif max_value > 10000:
            if bins > 8:
                plt.xticks(rotation=350)
            plt.text(bins, -50, '(万)', fontsize=25, ha='center', va='bottom')

    def set_right_label(self, x, y, data_list, number_value, bins):
        dd_value = (np.max(y) / 1.1) - (np.max(y) / 1.2)
        for x1, y1 in zip(x, y):  # 绘制条形数值
            plt.text(x1, y1 + 0.05, int(y1), fontsize=25, ha='center', va='bottom')
        plt.text(bins - 1.5, np.max(y) / 1.1, f'平均值:{format(np.round(np.mean(data_list)/number_value, 2), ",")}', fontsize=30,
                 ha='left', va='bottom')
        plt.text(bins - 1.5, np.max(y) / 1.1 - dd_value, f'标准差:{format(np.round(np.std(data_list)/number_value, 2), ",")}',
                 fontsize=30,
                 ha='left', va='bottom')
        plt.text(bins - 1.5, np.max(y) / 1.1 - 2 * dd_value, f'中位数:{format(np.round(np.median(data_list)/number_value, 2), ",")}',
                 fontsize=30,
                 ha='left', va='bottom')
        plt.text(bins - 1.5, np.max(y) / 1.1 - 4 * dd_value, f'企业数:{format(len(data_list), ",")}', fontsize=30,
                 ha='left', va='bottom')
        plt.text(bins - 1.5, np.max(y) / 1.1 - 3 * dd_value,
                 f'值域:[{format(np.round(np.min(data_list)/number_value, 2), ",")}, {format(np.round(np.max(data_list)/number_value, 2), ",")}]',
                 fontsize=30, ha='left', va='bottom')
    # 设置标签，因考虑是否展示问题，将保存与此穿插
    def setting_text(self, title_name, xlabel_name, ylabel_name,file_dir,is_output_coverage):
        plt.title(title_name, fontsize=50)
        # plt.ticklabel_format(axis="y", style="sci", scilimits=(0, 0))  # 科学计数法设置y轴
        plt.xlabel(xlabel_name, fontsize=35)
        plt.ylabel(ylabel_name, fontsize=35)
        plt.legend([ylabel_name, "Experiment"],fontsize=35)
        plt.tick_params(labelsize=23)
        if is_output_coverage:
            if file_dir is None:
                file_dir = r'./img'
            title_name1 = title_name + xlabel_name
            self.save_picture(file_dir,title_name1)
        plt.show()  # python中展示会创建空白图，注意保存放之前

    # 保存精度图片
    def save_picture(self,file_dir,title_name):
        try:
            plt.savefig(f'{file_dir}/{title_name}.jpg', dpi=500, bbox_inches='tight')
        except ValueError:
            plt.savefig(f'{file_dir}/{title_name}.jpg', dpi=250, bbox_inches='tight')
        print(f'{file_dir}/{title_name}.png')

class HuiTu_L():
    """
    data_list：(list)------数据列表
    bins：(int)------横轴的列数
    title:(str)------产品标题
    xlabel:(str)------横轴标签
    ylabel:(str)------纵轴标签
    file_dir=None:(str)------文件保存目录
    is_output_coverage=False:(bool)------是否进行图片保存
    ------
    进行图片开发环境展示，并可进行自己指定路径的图片保存，默认为当前文件夹
    """

    def __init__(self):
        pass

    def process(self, data_list, bins, title, xlabel, ylabel, file_dir=None, is_output_coverage=False):
        self.drawing(data_list, bins)
        self.setting_text(title, xlabel, ylabel, file_dir, is_output_coverage)

    # 绘图
    def drawing(self, data_list, bins):
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
        # plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
        plt.figure(figsize=(35, 18))
        max_value = np.max(data_list)
        min_value = np.min(data_list)

        # 处理同值数据
        if len(set(data_list)) == 1:
            plt.figure(figsize=(35, 18))
            x = [list(set(data_list))[0]]  # 横坐标，分组,
            y = [len(data_list)]

            for i in range(1, bins):
                if 10 in set(data_list):
                    x.append(list(set(data_list))[0] - i)
                else:
                    x.append(list(set(data_list))[0] + i)
                y.append(0)

            for x1, y1 in zip(x, y):  # 绘制条形数值
                plt.text(x1, y1 + 0.05, y1, fontsize=25, ha='center', va='bottom')
            if max_value > 10000000:
                self.set_right_label(x, y, data_list, 10000000, bins)
            elif max_value > 100000:
                self.set_right_label(x, y, data_list, 100000, bins)
            else:
                self.set_right_label(x, y, data_list, 1, bins)

            if max_value > 10000000:
                if bins > 8:
                    plt.xticks(rotation=350)
                plt.text(bins, -50, '(千万)', fontsize=25, ha='center', va='bottom')
            elif max_value > 100000:
                if bins > 8:
                    plt.xticks(rotation=350)
                plt.text(bins, -50, '(十万)', fontsize=25, ha='center', va='bottom')
            elif max_value > 10000:
                if bins > 8:
                    plt.xticks(rotation=350)
                plt.text(bins, -50, '(万)', fontsize=20, ha='center', va='bottom')

            plt.bar(x, y)
            return 0

        # 获得是否取整数的情况
        qu_zheng = 0
        if len(data_list) > 10:
            for idx in range(10):
                if int(data_list[idx]) == data_list[idx]:
                    qu_zheng = 1
                    break
        else:
            for i in data_list:
                if int(i) == i:
                    qu_zheng = 1
                    break


        if qu_zheng > 0:
            x, y = self.get_xy_zhenglist(data_list, max_value, min_value, bins)
        else:
            x, y = self.get_xy_list(data_list, max_value, min_value, bins)


        plt.bar(x, y)
        if max_value > 10000000:
            self.set_right_label(x, y, data_list, 10000000,bins)

        else:
            self.set_right_label(x, y, data_list, 1,bins)
        if max_value > 10000000:
            if bins > 8:
                plt.xticks(rotation=350)
            plt.text(bins, -50, '(千万)', fontsize=25, ha='center', va='bottom')

        return 1

    def get_xy_zhenglist(self, data_list, max_value, min_value, bins):
        div = [math.floor(np.min(data_list))]  # 为防止开始没数值报错
        x = []  # 横坐标，分组,
        y = []  # 纵坐标，频率，初始化为与分组同等数量的0
        j = 1
        cha_value = math.ceil((max_value - min_value) / bins)
        i = math.floor(np.min(data_list)) + cha_value

        for number in range(bins):
            div.append(i)
            if max_value > 10000000:
                x.append("[" + '%i' % (div[j - 1] / 10000000) + "," + '%i' % (div[j] / 10000000) + ")")

            else:
                x.append("[" + '%i' % (div[j - 1]) + "," + '%i' % (div[j]) + ")")
            y.append(0)
            i += cha_value
            j += 1

        # 分拣数据
        for item in data_list:
            for i in range(0, len(div) - 1):
                if i == len(div) - 2:
                    if item >= div[i] and item <= div[i + 1]:
                        y[i] += 1
                        break
                if item >= div[i] and item < div[i + 1]:
                    y[i] += 1
                    break
        return x, y

    def get_xy_list(self, data_list, max_value, min_value, bins):
        div = [np.min(data_list)]  # 为防止开始没数值报错
        x = []  # 横坐标，分组,
        y = []  # 纵坐标，频率，初始化为与分组同等数量的0
        j = 1
        cha_value = (max_value - min_value) / bins
        i = np.min(data_list) + cha_value

        for number in range(bins):
            div.append(i)
            if max_value > 10000000:
                x.append("[" + '%.2f' % (div[j - 1] / 10000000) + "," + '%.2f' % (div[j] / 10000000) + ")")

            else:
                x.append("[" + '%.2f' % (div[j - 1]) + "," + '%.2f' % (div[j]) + ")")
            y.append(0)
            i += cha_value
            j += 1

        # 分拣数据
        for item in data_list:
            for i in range(0, len(div) - 1):
                if i == len(div) - 2:
                    if item >= div[i] and item <= div[i + 1]:
                        y[i] += 1
                        break
                if item >= div[i] and item < div[i + 1]:
                    y[i] += 1
                    break
        return x, y

    def set_right_label(self, x, y, data_list, number_value, bins):
        dd_value = (np.max(y) / 1.1) - (np.max(y) / 1.2)
        for x1, y1 in zip(x, y):  # 绘制条形数值
            plt.text(x1, y1 + 0.05, int(y1), fontsize=25, ha='center', va='bottom')
        plt.text(bins - 1.5, np.max(y) / 1.1, f'平均值:{format(np.round(np.mean(data_list)/number_value, 2), ",")}', fontsize=30,
                 ha='left', va='bottom')
        plt.text(bins - 1.5, np.max(y) / 1.1 - dd_value, f'标准差:{format(np.round(np.std(data_list)/number_value, 2), ",")}',
                 fontsize=30,
                 ha='left', va='bottom')
        plt.text(bins - 1.5, np.max(y) / 1.1 - 2 * dd_value, f'中位数:{format(np.round(np.median(data_list)/number_value, 2), ",")}',
                 fontsize=30,
                 ha='left', va='bottom')
        plt.text(bins - 1.5, np.max(y) / 1.1 - 4 * dd_value, f'企业数:{format(len(data_list), ",")}', fontsize=30,
                 ha='left', va='bottom')
        plt.text(bins - 1.5, np.max(y) / 1.1 - 3 * dd_value,
                 f'值域:[{format(np.round(np.min(data_list)/number_value, 2), ",")}, {format(np.round(np.max(data_list)/number_value, 2), ",")}]',
                 fontsize=30, ha='left', va='bottom')
    # 设置标签，因考虑是否展示问题，将保存与此穿插
    def setting_text(self, title_name, xlabel_name, ylabel_name,file_dir,is_output_coverage):
        plt.title(title_name, fontsize=50)
        # plt.ticklabel_format(axis="y", style="sci", scilimits=(0, 0))  # 科学计数法设置y轴
        plt.xlabel(xlabel_name, fontsize=35)
        plt.ylabel(ylabel_name, fontsize=35)
        plt.legend([ylabel_name, "Experiment"],fontsize=35)
        plt.tick_params(labelsize=23)
        if is_output_coverage:
            if file_dir is None:
                file_dir = r'./img'
            title_name1 = title_name + xlabel_name
            self.save_picture(file_dir,title_name1)
        # plt.show()  # python中展示会创建空白图，注意保存放之前

    # 保存精度图片
    def save_picture(self,file_dir,title_name):
        try:
            plt.savefig(f'{file_dir}/{title_name}.jpg', dpi=500, bbox_inches='tight')
        except ValueError:
            plt.savefig(f'{file_dir}/{title_name}.jpg', dpi=250, bbox_inches='tight')
        print(f'{file_dir}/{title_name}.png')

# 按照传入列进行绘图
class HuiTu_div():
    """
    data_list：(list)------数据列表
    div_list：(int)------横轴的列表
    title:(str)------产品标题
    xlabel:(str)------横轴标签
    ylabel:(str)------纵轴标签
    file_dir=None:(str)------文件保存目录
    is_output_coverage=False:(bool)------是否进行图片保存
    ------
    进行图片开发环境展示，并可进行自己指定路径的图片保存，默认为当前文件夹
    """

    def __init__(self):
        pass

    def process(self, data_list, div_list, title, xlabel, ylabel, file_dir=None, is_output_coverage=False):
        value = self.drawing(data_list, div_list)
        if value > 0:
            self.setting_text(title, xlabel, ylabel, file_dir, is_output_coverage)

    # 绘图
    def drawing(self, data_list, div_list):
        data_list = list(np.array(data_list))
        if len(set(data_list)) == 1:
            return 0
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
        # plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
        plt.figure(figsize=(35, 18))
        max_value = math.ceil(np.max(data_list))

        # 处理同值数据

        x = []  # 横坐标，分组,
        y = np.zeros(len(div_list) - 1)
        for i in range(len(div_list) - 1):
            if max_value > 10000000:
                x.append(f'{div_list[i] / 10000000}-{div_list[i + 1] / 10000000}')
            elif max_value > 100000:
                x.append(f'{div_list[i]/100000}-{div_list[i + 1]/100000}')
            else:
                x.append(f'{div_list[i]}-{div_list[i + 1]}')

        # 分拣数据
        for item in data_list:
            for i in range(0, len(div_list) - 1):
                if i == len(div_list) - 2:
                    if item >= div_list[i] and item <= div_list[i + 1]:
                        y[i] += 1
                        break
                if item >= div_list[i] and item < div_list[i + 1]:
                    y[i] += 1
                    break

        # # 转换为频率
        # for i in range(len(y)):
        #     y[i] = round(y[i] / 1000, 3) * 100  # 化为百分比

        plt.bar(x, y)

        bins = len(div_list) - 1
        for x1, y1 in zip(x, y):  # 绘制条形数值
            plt.text(x1, y1 + 0.05, int(y1), fontsize=25, ha='center', va='bottom')
        if max_value > 10000000:
            HuiTu().set_right_label(x, y, data_list, 10000000, bins)
        elif max_value > 100000:
            HuiTu().set_right_label(x, y, data_list, 100000, bins)
        else:
            HuiTu().set_right_label(x, y, data_list, 1, bins)
        if max_value > 10000000:
            if bins > 8:
                plt.xticks(rotation=350)
            plt.text(bins, -50, '(千万)', fontsize=25, ha='center', va='bottom')
        elif max_value > 100000:
            if bins > 8:
                plt.xticks(rotation=350)
            plt.text(bins, -50, '(十万)', fontsize=25, ha='center', va='bottom')
        return 1

    # 设置标签，因考虑是否展示问题，将保存与此穿插
    def setting_text(self, title_name, xlabel_name, ylabel_name, file_dir, is_output_coverage):
        plt.title(title_name, fontsize=50)
        # plt.ticklabel_format(axis="y", style="sci", scilimits=(0, 0))  # 科学计数法设置y轴
        plt.xlabel(xlabel_name, fontsize=35)
        plt.ylabel(ylabel_name, fontsize=35)
        plt.legend([ylabel_name, "Experiment"], fontsize=35)
        plt.tick_params(labelsize=23)
        if is_output_coverage:
            if file_dir is None:
                file_dir = r'./img'
            title_name1 = title_name + xlabel_name
            self.save_picture(file_dir, title_name1)
        plt.show()  # python中展示会创建空白图，注意保存放之前

    # 保存精度图片
    def save_picture(self, file_dir, title_name):
        try:
            plt.savefig(f'{file_dir}/{title_name}.jpg', dpi=500, bbox_inches='tight')
        except ValueError:
            plt.savefig(f'{file_dir}/{title_name}.jpg', dpi=250, bbox_inches='tight')
        print(f'{file_dir}/{title_name}.png')

# 绘制多列对照图，研究曲线可多列，最好两列
class HuiTu_some_div():
    """
    data_list：(list)------数据列表的列表
    bins：(int)------横轴的区间列表
    title:(str)------产品标题
    xlabel:(str)------横轴标签
    ylabel:(str)------纵轴标签
    file_dir=None:(str)------文件保存目录
    is_output_coverage=False:(bool)------是否进行图片保存
    ------
    进行图片开发环境展示，并可进行自己指定路径的图片保存，默认为当前文件夹
    """

    def __init__(self):
        pass

    def process(self, data_list, div_list, title, xlabel, ylabel, file_dir=None, is_output_coverage=False):
        value = self.drawing(data_list, div_list)
        if value > 0:
            self.setting_text(title, xlabel, ylabel, file_dir, is_output_coverage)

    # 绘图
    def drawing(self, some_data_list, div_list):
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
        # plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
        plt.figure(figsize=(35, 18))

        total_width, n = 0.8, len(some_data_list)
        width = total_width / n
        number = -1 * math.floor(n/2)
        for key in some_data_list.keys():
            data_list = some_data_list[key]
            data_list = list(np.array(data_list))
            if len(set(data_list)) == 1:
                return 0
            max_value = math.ceil(np.max(data_list))
            # 处理同值数据

            x = []  # 横坐标，分组,
            y = np.zeros(len(div_list) - 1)
            for i in range(len(div_list) - 1):
                if max_value > 10000000:
                    x.append(f'{div_list[i] / 10000000}-{div_list[i + 1] / 10000000}')
                elif max_value > 100000:
                    x.append(f'{div_list[i]/100000}-{div_list[i + 1]/100000}')
                else:
                    x.append(f'{div_list[i]}-{div_list[i + 1]}')

            # 分拣数据
            for item in data_list:
                for i in range(0, len(div_list) - 1):
                    if i == len(div_list) - 2:
                        if item >= div_list[i] and item <= div_list[i + 1]:
                            y[i] += 1
                            break
                    if item >= div_list[i] and item < div_list[i + 1]:
                        y[i] += 1
                        break
            labels = x
            x1 = np.arange(len(labels))
            plt.bar(x1 + number * width + 1/n * width, y, width=width, label=key)

            # # 转换为频率
            # for i in range(len(y)):
            #     y[i] = round(y[i] / 1000, 3) * 100  # 化为百分比

            bins = len(div_list) - 1
            for x0, y0 in zip(x1, y):  # 绘制条形数值
                plt.text(x0 + number * width + 1/n * width, y0 + 0.05, int(y0), fontsize=25, ha='center', va='bottom')
            number += 1
            plt.xticks(x1, labels)

            if max_value > 10000000:
                if bins > 8:
                    plt.xticks(rotation=350)
                plt.text(bins, -50, '(千万)', fontsize=25, ha='center', va='bottom')
            elif max_value > 100000:
                if bins > 8:
                    plt.xticks(rotation=350)
                plt.text(bins, -50, '(十万)', fontsize=25, ha='center', va='bottom')
        return 1

    # 设置标签，因考虑是否展示问题，将保存与此穿插
    def setting_text(self, title_name, xlabel_name, ylabel_name, file_dir, is_output_coverage):
        plt.title(title_name, fontsize=50)
        # plt.ticklabel_format(axis="y", style="sci", scilimits=(0, 0))  # 科学计数法设置y轴
        plt.xlabel(xlabel_name, fontsize=35)
        plt.ylabel(ylabel_name, fontsize=35)
        plt.legend(fontsize=35)
        plt.tick_params(labelsize=23)
        if is_output_coverage:
            if file_dir is None:
                file_dir = r'./img'
            title_name1 = title_name + xlabel_name
            self.save_picture(file_dir, title_name1)
        # plt.show()  # python中展示会创建空白图，注意保存放之前

    # 保存精度图片
    def save_picture(self, file_dir, title_name):
        try:
            plt.savefig(f'{file_dir}\{title_name}.jpg', dpi=500, bbox_inches='tight')
        except ValueError:
            plt.savefig(f'{file_dir}\{title_name}.jpg', dpi=250, bbox_inches='tight')
        print(f'{file_dir}\{title_name}.png')


# 绘制多列对照图，研究曲线可多列，最好两列,加趋势线百分比
class HuiTu_line_div():
    """
    data_list：(list)------数据列表
    div_list：(int)------横轴的列表
    title:(str)------产品标题
    xlabel:(str)------横轴标签
    ylabel:(str)------纵轴标签
    file_dir=None:(str)------文件保存目录
    is_output_coverage=False:(bool)------是否进行图片保存
    ------
    进行图片开发环境展示，并可进行自己指定路径的图片保存，默认为当前文件夹
    """

    def __init__(self):
        pass

    def process(self, data_list, div_list, title, xlabel, ylabel, file_dir=None, is_output_coverage=False):
        value = self.drawing(data_list, div_list)
        if value > 0:
            self.setting_text(title, xlabel, ylabel, file_dir, is_output_coverage)


    # 绘图
    def drawing(self, data_list, div_list):
        data_list = list(np.array(data_list))

        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
        # plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
        max_value = math.ceil(np.max(data_list))

        # 单柱数据
        # if len(set(data_list)) == 1:
        #     value_h = list(set(data_list))[0]
        #     plt.bar(value_h, len(data_list), width=0.3)
        #
        #     return 1
        # 处理同值数据

        x = []  # 横坐标，分组,
        y = np.zeros(len(div_list) - 1)
        for i in range(len(div_list) - 1):
            if max_value > 10000000:
                x.append(f'{div_list[i] / 10000000}')
            elif max_value > 100000:
                x.append(f'{div_list[i]/100000}')
            else:
                x.append(f'{div_list[i]}')

        # 分拣数据
        for item in data_list:
            for i in range(0, len(div_list) - 1):
                if i == len(div_list) - 2:
                    if item >= div_list[i] and item <= div_list[i + 1]:
                        y[i] += 1
                        break
                if item >= div_list[i] and item < div_list[i + 1]:
                    y[i] += 1
                    break

        # 转换为频率
        yy = y.copy()
        plt.bar(x, y,width=0.3)
    #     x_smooth = np.linspace(0, len(x)-1, 50)
    #     y1_smooth = make_interp_spline(range(len(x)), yy)(x_smooth)
    #     plt.plot(x_smooth,y1_smooth,'-',color='black',linewidth=3)
        plt.plot(x, yy, '-', color='black', linewidth=3)

        bins = len(div_list) - 1
        for x1, y1 in zip(x, y):  # 绘制条形数值
            bf_value = f'{round(y1/sum(y), 3) * 100}%'
            plt.text(x1, y1 + 0.05, int(y1), fontsize=10, ha='center', va='bottom')
            if y1 >= 10000:
                dd_value = y1/50
            elif (y1 >=1000) & (y1<10000):
                dd_value = y1/10
            else:
                dd_value = y1 + 500
            # plt.text(x1, y1 + dd_value, bf_value, fontsize=10, ha='center', va='bottom')
        if max_value > 10000000:
            if bins > 8:
                plt.xticks(rotation=350)
            plt.text(bins, -50, '(千万)', fontsize=10, ha='center', va='bottom')
        elif max_value > 100000:
            if bins > 8:
                plt.xticks(rotation=350)
            plt.text(bins, -50, '(十万)', fontsize=10, ha='center', va='bottom')
        return 1

    # 设置标签，因考虑是否展示问题，将保存与此穿插
    def setting_text(self, title_name, xlabel_name, ylabel_name, file_dir, is_output_coverage):
        plt.title(title_name, fontsize=20)
        # plt.ticklabel_format(axis="y", style="sci", scilimits=(0, 0))  # 科学计数法设置y轴
        plt.xlabel(xlabel_name, fontsize=15)
        plt.ylabel(ylabel_name, fontsize=15)
        plt.tick_params(labelsize=10)
        if is_output_coverage:
            if file_dir is None:
                file_dir = r'./img'
            title_name1 = title_name + xlabel_name
            self.save_picture(file_dir, title_name1)
        # plt.show()  # python中展示会创建空白图，注意保存放之前

    # 保存精度图片
    def save_picture(self, file_dir, title_name):
        try:
            plt.savefig(f'{file_dir}/{title_name}.jpg', dpi=500, bbox_inches='tight')
            if os.path.getsize(f'{file_dir}/{title_name}.jpg') > 10000000:
                plt.savefig(f'{file_dir}/{title_name}.jpg', dpi=100, bbox_inches='tight')
            print(f'{file_dir}/{title_name}.png')
        except:
            pass
            print(f'{file_dir}/{title_name}.png is failure')
            # plt.savefig(f'{file_dir}/{title_name}.jpg', dpi=250, bbox_inches='tight')


# 绘制多列对照图，研究曲线可多列，最好两列,加趋势线百分比和百分比轴（结合，慎用）
class HuiTu_line_div_ce():
    """
    data_list：(list)------数据列表
    div_list：(int)------横轴的列表
    title:(str)------产品标题
    xlabel:(str)------横轴标签
    ylabel:(str)------纵轴标签
    file_dir=None:(str)------文件保存目录
    is_output_coverage=False:(bool)------是否进行图片保存
    ------
    进行图片开发环境展示，并可进行自己指定路径的图片保存，默认为当前文件夹
    """

    def __init__(self):
        pass

    def process(self,ax, data_list, div_list, title, xlabel, ylabel, file_dir=None, is_output_coverage=False):
        value = self.drawing(ax,data_list, div_list)
        if value > 0:
            self.setting_text(title, xlabel, ylabel, file_dir, is_output_coverage)


    # 绘图
    def drawing(self, ax,data_list, div_list):
        data_list = list(np.array(data_list))
        if len(set(data_list)) == 1:
            return 0
        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
        # plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
        max_value = math.ceil(np.max(data_list))

        # 处理同值数据

        x = []  # 横坐标，分组,
        y = np.zeros(len(div_list) - 1)
        for i in range(len(div_list) - 1):
            if max_value > 10000000:
                x.append(f'{div_list[i] / 10000000}')
            elif max_value > 100000:
                x.append(f'{div_list[i]/100000}')
            else:
                x.append(f'{div_list[i]}')

        # 分拣数据
        for item in data_list:
            for i in range(0, len(div_list) - 1):
                if i == len(div_list) - 2:
                    if item >= div_list[i] and item <= div_list[i + 1]:
                        y[i] += 1
                        break
                if item >= div_list[i] and item < div_list[i + 1]:
                    y[i] += 1
                    break

        # 转换为频率
        yy = y.copy()
        add_value = np.sum(yy)
        for i in range(len(yy)):
            yy[i] = round(yy[i] / add_value, 4) * 100  # 化为百分比

        ax.bar(x, y,width=0.3)
    #     x_smooth = np.linspace(0, len(x)-1, 50)
    #     y1_smooth = make_interp_spline(range(len(x)), yy)(x_smooth)
    #     plt.plot(x_smooth,y1_smooth,'-',color='black',linewidth=3)
        ax2 = ax.twinx()
        plt.plot(x, yy, '-', color='black', linewidth=3,label='百分比')
        ax2.set_ylabel('百分比',fontsize=15)

        bins = len(div_list) - 1
        for x1, y1 in zip(x, y):  # 绘制条形数值
            bf_value = f'{round(y1/sum(y) * 100, 2) }%'
            ax.text(x1, y1 + 0.05, int(y1), fontsize=25, ha='center', va='bottom')
            dd_value = np.max(y)/20
            ax.text(x1, y1 + dd_value, bf_value, fontsize=25, ha='center', va='bottom')
        if max_value > 10000000:
            if bins > 8:
                plt.xticks(rotation=350)
            ax.text(bins, -50, '(千万)', fontsize=25, ha='center', va='bottom')
        elif max_value > 100000:
            if bins > 8:
                ax.xticks(rotation=350)
            ax.text(bins, -50, '(十万)', fontsize=25, ha='center', va='bottom')
        return 1

    # 设置标签，因考虑是否展示问题，将保存与此穿插
    def setting_text(self, title_name, xlabel_name, ylabel_name, file_dir, is_output_coverage):
        plt.title(title_name, fontsize=15)
        # plt.ticklabel_format(axis="y", style="sci", scilimits=(0, 0))  # 科学计数法设置y轴
        ax.set_xlabel(xlabel_name, fontsize=15)
        ax.set_ylabel(ylabel_name, fontsize=15)
        plt.tick_params(labelsize=23)
        # if is_output_coverage:
        #     if file_dir is None:
        #         file_dir = r'./img'
        #     title_name1 = title_name + xlabel_name
        #     self.save_picture(file_dir, title_name1)
        # plt.show()  # python中展示会创建空白图，注意保存放之前

    # 保存精度图片
    def save_picture(self, file_dir, title_name):
        try:
            plt.savefig(f'{file_dir}/{title_name}.jpg', dpi=500, bbox_inches='tight')
        except ValueError:
            plt.savefig(f'{file_dir}/{title_name}.jpg', dpi=250, bbox_inches='tight')
        print(f'{file_dir}/{title_name}.png')

if __name__ == '__main__':
    number = '1'  # 指定执行哪个测试

    # 指定执行函数绘图
    # 不指定分割绘图
    if int(number) == 1:
        is_output_coverage = False
        # is_output_coverage = True
        file = 'F:\lry-exe\中标政府采购_0.xlsx'
        baocun_dir = 'F:\lry-exe\pyvalus'
        column_name = 'indicationvalue'  # 列名

        formula = 'precent'  # mad, log, zhengtai, precent
        number = 3      # n,   底数,    u ,    百分比
        bins = 10
        title = '注册资本1'
        xlabel = 'number'
        ylabel = '频数'


        calc = CalcData()
        before_list, middle_list, after_list = calc.process(file,column_name,formula,number)
        huitu_data = middle_list
        if len(huitu_data) == 0:
            print('数据为空，不具有画图必要')
            exit()
        huatu = HuiTu_L()
        huatu.process(middle_list, bins, title, xlabel, ylabel,baocun_dir,is_output_coverage)
        print('sucess')

    # 指定分值段绘图
    elif int(number) == 3:
        def calc_zheng(calc_list):
            result_list = []
            for i in calc_list:
                if '.5' in str(i):
                    result_list.append(math.ceil(float(i)))
                else:
                    result_list.append(round(float(i)))
            return result_list
        # ---是否进行保存---
        is_output_coverage = False
        # is_output_coverage = True
        ################################################################################################################
        calc = CalcData()
        huatu = HuiTu_div()

        file = r'E:\下载使用文件\专利数据\企业专利总价值表_20230303.xlsx'  # 读取文件路径
        fn = pd.read_excel(file)
        title = '专利'  # 图片表头
        bins = [0,100,200,300,400,500,600,1000,2000,3000,4000]  # 图形柱数
        baocun_dir = ''  # 保存路径

        calc_data = fn['企业专利总价值']
        data_list = list(map(lambda x: float(x), calc_data))

        huatu.process(data_list, bins, title, '企业专利总价值', '频数', baocun_dir, is_output_coverage)

    # 绘制柱状折线图
    elif int(number) == 4:
        # ---是否进行保存---
        is_output_coverage = False
        # is_output_coverage = True
        ################################################################################################################
        calc = CalcData()
        huatu = HuiTu_line_div()

        file = r'E:\下载使用文件\etl建行数据\etl_data\excel\jh_assess\测试\已完成操作\*.csv'
        file_list = glob(file)
        plt.figure(figsize=(40, 100))
        for file in file_list:
            fn = pd.read_csv(file)
            column_list = list(fn.columns)
            print(column_list)
            number = 0
            for column in column_list:
                if number >= 9:
                    plt.show()
                    break

                if column in ['compName','customerGroupName']:
                    continue
                if column in file.split('\\')[-1].split('.')[0]:
                    title = file.split('\\')[-1].split('.')[0]
                else:
                    title = file.split('\\')[-1].split('.')[0] + f'_{column}'
                bins = [4,5,6,7,8,9,np.inf]  # 图形柱数
                baocun_dir = 'E:\下载使用文件\etl建行数据\etl_data\excel\jh_assess\测试\图片1'  # 保存路径

                calc_data = fn[column]
                data_list = list(map(lambda x: float(x), calc_data))

                number += 1
                ax = plt.subplot(5, 2, number)

                try:
                    huatu.process(data_list, bins, '分值统计', title, '频数', baocun_dir, is_output_coverage)
                except:
                    data_list = []
                    for i in calc_data:
                        if float(i) >= 4:
                            data_list.append(float(i))
                        else:
                            data_list.append(4.0)
                    # huitu_list = {title: data_list}
                    huatu.process(data_list, bins, '分值统计', title, '频数', baocun_dir, is_output_coverage)

            exit()

    # 自定义绘图
    elif int(number) == 5:
        # ---是否进行保存---
        is_output_coverage = False
        # is_output_coverage = True
        ################################################################################################################
        # file = r'E:\下载使用文件\专利数据\企业专利总价值表_20230303.xlsx'  # 读取文件路径
        file = r'E:\下载使用文件\专利数据\企业专利总价值级预测授信额度表_20230308.xlsx'
        fn = pd.read_excel(file)
        title = '20230308授信额度预测分类求和'  # 图片表头
        baocun_dir = ''  # 保存路径

        # calc_data = fn['企业专利总价值']
        # data_list = list(map(lambda x: float(x), calc_data))
        # fn['企业专利总价值'] = pd.Series(data_list)

        lei_list = list(set(fn['国民经济行业门类']))
        x_list = []
        y_list = []
        for lei in lei_list:
            calc_df = fn[fn['国民经济行业门类']==lei]
            sum_value = np.sum(calc_df['授信额度预测'])
            x_list.append(lei[:2])
            y_list.append(sum_value)

        plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
        # plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
        plt.figure(figsize=(35, 18))
        plt.bar(x_list, y_list,label='授信额度预测')

        for x1, y1 in zip(x_list, y_list):  # 绘制条形数值
            plt.text(x1, y1 + 0.05, int(y1), fontsize=25, ha='center', va='bottom')
        plt.title(title, fontsize=50)
        # plt.ticklabel_format(axis="y", style="sci", scilimits=(0, 0))  # 科学计数法设置y轴
        plt.xlabel('国标行业-门', fontsize=35)
        plt.ylabel('和', fontsize=35)
        plt.legend(fontsize=35)
        plt.tick_params(labelsize=23)
        plt.xticks(rotation=15)
        plt.ticklabel_format(axis="y", style="sci", scilimits=(0, 0))  # 科学计数法设置y轴
        plt.show()

    # 绘制多列对照图，研究曲线可多列，最好两列, 加趋势线百分比和百分比轴（结合，慎用）
    elif int(number) == 6:
        # ---是否进行保存---
        # is_output_coverage = False
        is_output_coverage = True
        ################################################################################################################
        bins = [4, 5, 6, 7, 8, 9, np.inf]  # 图形柱数
        baocun_dir = 'E:\下载使用文件\etl建行数据\etl_data\excel\jh_assess\测试\图片1'  # 保存路径
        calc = CalcData()
        huatu = HuiTu_line_div_ce()
        file_dir = r'E:\下载使用文件\etl建行数据\etl_data\excel\jh_assess\测试\已完成操作'


        kq_list = ['数字创意产业','新一代信息技术产业','新材料产业','新能源产业','新能源汽车产业','生物产业','相关服务业','节能环保产业','高端装备制造产业']
        lei_list = ['_负综合分','_综合分','_一级指标','_二级指标','_叶']
        data_dic = {}

        for lei in lei_list:
            file_list = glob(file_dir + f'\*{lei}.csv')
            kq_dic = {}
            for file in file_list:
                customer = file.split('\\')[-1].split(f"{lei}.csv")[0]
                fn0 = pd.read_csv(file)
                kq_dic.update({customer: fn0})
            data_dic.update({lei: kq_dic})

        for lei in lei_list:
            calc_dic = data_dic[lei]
            column_list = calc_dic[kq_list[0]].columns
            for column in column_list:
                if column in ['compName', 'customerGroupName']:
                    continue
                number = 0
                plt.figure(figsize=(40, 100))
                for kq in kq_list:
                    print(number)
                    calc_data = calc_dic[kq][column]
                    title = f"{kq}_{column}"
                    data_list = list(map(lambda x: float(x), calc_data))
                    number += 1
                    ax = plt.subplot(3, 3, number)
                    try:
                        huatu.process(ax,data_list, bins, '分值统计', title, '频数', baocun_dir, is_output_coverage)
                    except:
                        data_list = []
                        for i in calc_data:
                            if float(i) >= 4:
                                data_list.append(float(i))
                            else:
                                data_list.append(4.0)
                        huatu.process(ax,data_list, bins, '分值统计', title, '频数', baocun_dir, is_output_coverage)
                if is_output_coverage:
                    try:
                        plt.savefig(f'{baocun_dir}\{column}.jpg', dpi=500, bbox_inches='tight')
                    except ValueError:
                        plt.savefig(f'{baocun_dir}\{column}.jpg', dpi=250, bbox_inches='tight')
                    plt.close()
                    print(f'{baocun_dir}\{column}.jpg')
                    # plt.show()

    # 绘制多列对照图，研究曲线可多列，最好两列,加趋势线百分比
    elif int(number) == 7:
        # ---是否进行保存---
        is_output_coverage = False
        # is_output_coverage = True
        ################################################################################################################
        calc = CalcData()
        huatu = HuiTu_line_div()

        file = f'E:\下载使用文件\光大评分数据\输出\excel\光大评分新材料产业链测试2023-10-08最终版.csv'
        file_list = glob(file)
        plt.figure(figsize=(40, 100))
        for file in file_list:
            kq_name = file.split('光大评分')[-1].split('最终版')[0]
            fn = pd.read_csv(file)
            column_list = [i for i in list(fn.columns) if '_' not in i]
            number = 0
            for column in column_list:
                if column in ['企业编号', 'customerGroupId','企业名称']:
                    continue
                title = column

                bins = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100, np.inf]  # 图形柱数
                baocun_dir = rf'E:\下载使用文件\光大评分数据\20230927得分\图片\{kq_name}'  # 保存路径
                if not os.path.exists(baocun_dir):
                    os.system('mkdir -p ' + baocun_dir)
                    print('mkdir -p ' + baocun_dir)


                calc_data = fn[column]
                data_list = list([math.ceil(x/10)*10 for x in calc_data if x > 0])

                try:
                    huatu.process(data_list, bins, '分值统计', title, '频数', baocun_dir, is_output_coverage)
                except:
                    data_list = []
                    for i in calc_data:
                        if float(i) >= 0:
                            data_list.append(float(i))
                        else:
                            data_list.append(0)
                    huatu.process(data_list, bins, '分值统计', title, '频数', baocun_dir, is_output_coverage)
                plt.show()

    # 测试单列分段图
    elif int(number) == 8:
        # ---是否进行保存---
        # is_output_coverage = False
        is_output_coverage = True
        ################################################################################################################
        calc = CalcData()
        huatu = HuiTu_div()

        file = f'E:\python程序\danji\jiaohang\商机评分-2023-11-21（验证版）.xlsx'
        sheet_list = ['获得科技创新专项资金支持','获得文化产业发展专项资金','矿业权抵押即将到期']
        plt.figure(figsize=(40, 100))
        for sheet in sheet_list:
            fn = pd.read_excel(file, sheet_name=sheet)
            title = sheet

            bins = [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, np.inf]  # 图形柱数
            baocun_dir = rf'E:\下载使用文件\接收人文件\黄佳琪\sy_cd_me_buss_oppo_param\图片'  # 保存路径
            if not os.path.exists(baocun_dir):
                os.system('mkdir -p ' + baocun_dir)
                print('mkdir -p ' + baocun_dir)


            calc_data = fn['平均分']
            data_list = list([round(x, 2) for x in calc_data if x > 0])

            try:
                huatu.process(data_list, bins, '分值统计', title, '频数', baocun_dir, is_output_coverage)
            except:
                data_list = []
                for i in calc_data:
                    if float(i) >= 0:
                        data_list.append(float(i))
                    else:
                        data_list.append(0)
                huatu.process(data_list, bins, '分值统计', title, '频数', baocun_dir, is_output_coverage)
                plt.show()

    # 单柱测试
    else:
        z_list = np.ones(2200) * 10
        huatu = HuiTu()
        huatu.process(z_list, 10, 'aa', 'aa', f'{len(z_list)}', 'baocun_dir', False)
        print('sucess')







