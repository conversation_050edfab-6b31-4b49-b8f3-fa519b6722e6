version: '3.4'
services:
  confluence:
    image: haxqer/confluence:8.6.1
    container_name: confluence-srv
    environment:
      - TZ=Asia/Shanghai
    #      - JVM_MINIMUM_MEMORY=1g
    #      - JVM_MAXIMUM_MEMORY=12g
    #      - JVM_CODE_CACHE_ARGS='-XX:InitialCodeCacheSize=1g -XX:ReservedCodeCacheSize=8g'
    depends_on:
      - mysql
    ports:
      - "8111:8090"
    volumes:
      - /data/confluenceV861/confluence_data:/var/confluence
    restart: always
    networks:
      - network-bridge

  mysql:
    image: mysql:8.0
    container_name: mysql-confluence
    environment:
      - TZ=Asia/Shanghai
      - MYSQL_DATABASE=confluence
      - MYSQL_ROOT_PASSWORD=shiye
      - MYSQL_USER=confluence
      - MYSQL_PASSWORD=shiye
    command: ['mysqld', '--character-set-server=utf8mb4', '--collation-server=utf8mb4_bin', '--transaction-isolation=READ-COMMITTED', '--innodb_log_file_size=256M', '--max_allowed_packet=256M','--log_bin_trust_function_creators=1']
    volumes:
      - /data/confluenceV861/confluence_mysql_data:/var/lib/mysql
    restart: always
    ports:
      - "3306:3306"
    networks:
      - network-bridge

networks:
  network-bridge:
    driver: bridge
