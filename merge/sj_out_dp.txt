
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000301001',
     " with source_dat as ( SELECT tb2.eventsubject, tb2.subjectcode, tb1.eventdate, tb1.projectname, concat( concat( '100002', '&#&', if(tb1.amount is null, '', tb1.amount), '&#&', '2' ) , '@@' , concat( '100003', '&#&', if( tb1.progress is null, '', tb1.progress ), '&#&', '3' ) ) as eigenvalue ,concat_ws('&#&','拟在建工程项目表', 'sy_cd_me_buss_oth_plan_project', 'id', string(tb1.id)) as retrovalue FROM ( select id, eventdate, projectname, amount, progress from ( select id, date_format(publishdate, 'yyyy-MM-dd') as eventdate, projectname, amount, progress, datastatus, row_number() over ( partition by id order by filedate desc ) num from seeyii_data_house.dwd_me_buss_oth_plan_project ) t where t.num = 1 and t.eventdate is not null AND t.datastatus != 3 ) AS tb1 JOIN ( select pkid, eventsubject, subjectcode from ( select pkid, compcode AS subjectcode, datastatus, regexp_replace( compname, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as eventsubject, row_number() over ( partition by fingerid order by filedate desc ) num2 from seeyii_data_house.dwd_me_buss_oth_plan_project_se ) t where t.num2 = 1 and t.subjectcode is not null AND t.datastatus != 3 ) AS tb2 ON tb1.id = tb2.pkid ), des_dat as ( select eventsubject, subjectcode, eventdate, eigenvalue, retrovalue, CAST(null AS STRING) as url, 'SJ000301001' as eventtype, CAST(null AS STRING) as expiredate, concat( '本单位于', eventdate, '获得发改委审批' , if( (projectname is null) or (projectname = ''), '', concat( '，重大固定资产投资项目“', projectname, '”获批通过' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, eigenvalue, retrovalue, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000301002',
     " with source_dat AS ( select eventsubject, subjectcode, eventdate, url ,concat_ws('&#&','环评事件审批表', 'sy_cd_ms_cn_comp_eia_sp', 'sourceId', string(id)) as retrovalue from ( select compname AS eventsubject, compcode AS subjectcode, ansurl as url, isvalid, datastatus, docname, date_format(ansdate, 'yyyy-MM-dd') as eventdate, id, row_number() over ( partition by id order by filedate desc ) num from seeyii_data_house.dwd_ms_cn_comp_eia_sp ) t where t.num = 1 and t.subjectcode is not null AND t.eventdate is not null AND t.isvalid != 0 AND t.datastatus != 3 and docname not regexp('不予|撤销|撤回') ), des_dat as ( select eventsubject, subjectcode, eventdate, url, retrovalue, 'SJ000301002' as eventtype, CAST(null AS STRING) as expiredate, concat( '本单位于', eventdate, '有重大项目获得环评通过', '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, retrovalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, NULL as eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000304001',
     " with sour_df AS ( select * from ( select *, row_number() over( partition by id order by filedate desc ) as rnumber from seeyii_data_house.dwd_ms_base_comp_addr_chg ) as tb where rnumber = 1 and datastatus != 3 AND beProName != afProName AND beCityNm != afCityNm AND beDistrict != afDistrict ), next_df AS ( SELECT date_format(changetime, 'yyyy-MM-dd') as eventdate, compcode as subjectcode, '' as eventsubject, 'SJ000304001' as eventtype, beproname, becitynm, bedistrict, afproname, afcitynm, afdistrict ,concat_ws('&#&','注册地址变更记录表', 'sy_cd_ms_base_comp_addr_chg', 'sourceId', string(sourceId)) as retrovalue from sour_df where changetime is not NULL and compcode is not NULL and changetime >= '2021-06-01' and bedistrict != afdistrict ), final_df as ( select *, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate, concat( '', '公司于', eventdate, '发生迁址，从', if(beproname is NULL, '', beproname), if(becitynm is NULL, '', becitynm), if(bedistrict is NULL, '', bedistrict), '迁移至', if(afproname is NULL, '', afproname), if(afcitynm is NULL, '', afcitynm), if(afdistrict is NULL, '', afdistrict), '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = {fileDate}) select fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, NULL as eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000202001',
     " with base_df AS ( select compname as eventsubject, case WHEN finatype = '101' THEN '定向增发' WHEN finatype = '102' THEN '公开增发' WHEN finatype = '201' THEN '配股' WHEN finatype = '202' THEN '优先股' WHEN finatype = '301' THEN '可转债' end as finatype, regexp_replace( issueobject, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as issueobject, finaamount, regexp_replace( leadbroker, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as leadbroker, regexp_replace( brokname, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as brokname, date_format(accedate, 'yyyy-MM-dd') as eventdate, compcode as subjectcode, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate ,concat_ws('&#&','再融资发行上市审核概况表', 'sy_cd_me_trad_spo_base', 'sourceId', string(id)) as retrovalue from ( select *, row_number() over( partition by id order by filedate desc ) as rnumber from seeyii_data_house.dwd_me_trad_spo_base ) as tb where rnumber = 1 and datastatus != 3 and accedate is not null and accedate != '' ), mid_df AS ( select eventsubject, finatype, if ( issueobject regexp '。$', substr( issueobject, 0, length(issueobject)-1 ), issueobject ) as issueobject, finaamount, leadbroker, brokname, eventdate, subjectcode, url, expiredate, concat( concat( '100005', '&#&', if(finaamount is null, '', finaamount), '&#&', '3' ), '@@', concat( '100006', '&#&', if(finatype is null, '', finatype), '&#&', '7' ) ) as eigenvalue ,retrovalue from base_df ), add_desc_df AS ( select *, concat( '公司于', eventdate, '启动再融资' ) as a, if( (finatype is null) or (finatype = ''), '', concat( '，再融资类型为', finatype ) ) as b, if( (issueobject is null) or (issueobject = ''), '', concat( '，发行对象为', issueobject ) ) as c, if( (finaamount is null) or (finaamount = ''), '。', concat( '，预计融资金额', finaamount, '亿元。' ) ) as d, if( (leadbroker is null) or (leadbroker = ''), '', concat( '本次再融资主承销商为', leadbroker ) ) as e, if( (brokname is null) or (brokname = ''), '', concat( '保荐机构为', brokname, '。' ) ) as f from mid_df ), next_df as ( select *, if( e = '', concat_ws('', a, b, c, d, e, f), if( f = '', concat_ws('', a, b, c, d, e, '。'), concat_ws('', a, b, c, d, e, '，', f) ) ) as desc1 from add_desc_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000202001', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000202001' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from next_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000204005',
     " with sour_df as ( select * from seeyii_data_house.dwd_me_buss_per_smjj where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_per_smjj where modifytime is not null ) ), next_df AS ( SELECT date_format(filingtime, 'yyyy-MM-dd') as eventdate, compcode as subjectcode, compname as eventsubject, 'SJ000204005' as eventtype, fundname, fundtype ,concat_ws('&#&','私募基金', 'sy_cd_me_buss_per_smjj', 'sourceId', string(id)) as retrovalue from sour_df where filingtime is not NULL and compcode is not null ), add_desc_df as ( select *, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate, concat( '基金管理人于', eventdate, '登记设立私募基金' ) as a, if( (fundname is null) or (fundname = ''), '', concat('，名称为', fundname) ) as b, if( (fundtype is null) or (fundtype = ''), '', concat( '，业务类型为：', fundtype ) ) as c, '。' d from next_df ), final_df as ( select *, concat_ws('', a, b, c, d) as desc1 from add_desc_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), if(eventtype is NULL, '#', eventtype), if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, NULL as eigenvalue,retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000203001',
     " with base_sk_df AS ( select distinct secucode, regexp_replace( compname, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as compname from ( select secucode, compname, datastatus, row_number() over( partition by id order by filedate desc ) as rnumber from seeyii_data_house.dwd_ms_base_sk_stock ) as tb where rnumber = 1 and datastatus != 3 ), base_issuenew_df as ( select regexp_replace( targetname, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as targetname, tradeequityr, mergeamt, cur, if( string(mergetype) == '1', '对外投资', '对外收购' ) as mergetype, regexp_replace( rivalsorother, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as rivalsorother, if( string(domorcross) == '1', '不', '' ) as domorcross, if( string(isconnectedt) == '0', '不', '' ) as isconnectedt, if( string(ispevc) == '0', '不', '' ) as ispevc, isvalid, b.compname as eventsubject, DATE( from_unixtime( unix_timestamp(declaredate, 'yyyymmdd'), 'yyyy-mm-dd' ) ) as eventdate, datastatus, compcode as subjectcode, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate, concat( concat( '100011', '&#&', mergeamt, '&#&', '3' ) ) as eigenvalue ,concat_ws('&#&','并购事件表', 'sy_cd_me_trad_pevc_merge_events', 'sourceId', string(id)) as retrovalue from ( select *, row_number() over( partition by id order by filedate desc ) as rnumber from seeyii_data_house.dwd_me_trad_pevc_merge_events ) as a left join base_sk_df as b on a.secucode = b.secucode where rnumber = 1 and datastatus != 3 and isvalid = 1 and domorcross is not null and declaredate is not null and compcode is not null ), next_df AS ( select *, concat( '公司于', eventdate, '公告宣布发生对外并购' ) as a1, if( targetname is null, '', concat( '，并购标的为', targetname, '' ) ) as b, if( tradeequityr is null, '', concat( '，交易股权比例为', tradeequityr, '%' ) ) as c, if( mergeamt is null, '', concat( '，涉及金额', string( round(mergeamt, 2) ), '万元' ) ) as d, if( cur is null, '。', concat('，币种为', cur, '。') ) as e, if( mergetype is null, '', concat( '此次并购类型为', mergetype, '' ) ) as f, if( rivalsorother is null, '。', concat( '，交易对手或其他出资方为', rivalsorother, '。' ) ) as g, if( domorcross is null, '', concat( '本次交易', domorcross, '涉及海外并购' ) ) as h, if( isconnectedt is null, '', concat( '、', isconnectedt, '涉及关联交易' ) ) as i, if( domorcross is null, '。', concat( '、', domorcross, '涉及PE/VC支持。' ) ) as j from base_issuenew_df ), final_df as ( select *, concat_ws('', a1, b, c, d, e, f, g, h, i, j) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000203001', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000203001' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000205004',
     " with sk_stock as ( select compcode as subjectcode, compname as eventsubject, date_format(publishDate, 'yyyy-MM-dd') as eventdate, bondName, proposedAmount ,concat_ws('&#&','债券再审项目进度', 'sy_cd_mm_cn_bond_audit_prj', 'fingerId', fingerId) as retrovalue from seeyii_data_tert.dwd_mm_cn_bond_audit_prj where filedate in ( select max(filedate) from seeyii_data_tert.dwd_mm_cn_bond_audit_prj ) AND datastatus != 3 and isvalid = 1 ), next_df as ( select *, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate, concat( '100012', '&#&', proposedAmount, '&#&', '3' ) as eigenvalue, concat( eventdate, '公司新增债券发行审核申请' ) as a, if( (bondName is null) or (bondName = ''), '', concat('，债券名称为', bondName) ) as b, if( (proposedAmount is null) or (proposedAmount = ''), '', concat( '，拟发行金额', proposedAmount, '亿元' ) ) as c, '。' d from sk_stock where eventdate is not NULL ), final_df as ( select *, concat_ws('', a, b, c, d) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000205004', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000205004' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000402001',
     " with base_df AS ( select issuercode as subjectcode, issuer as eventsubject, bondtype, if( cname is null or cname = '', '', cname ) as cname, if( sname is null or sname = '', '', concat('债券简称', sname, '、') ) as sname, secucode, date_format(enddate, 'yyyy-MM-dd') as expiredate, date_sub( date_format(enddate, 'yyyy-MM-dd'), 60 ) as eventdate, CAST(null AS STRING) as url, innerCode ,concat_ws('&#&','债券基本信息', 'sy_cd_mm_cn_bond_base', 'sourceId', string(id)) as retrovalue from ( select *, row_number() over( partition by id order by filedate desc ) as rnumber from seeyii_data_house.dwd_mm_cn_bond_base ) as tb where rnumber = 1 and datastatus != 3 and enddate is not null and enddate != '' and issuercode is not null and bondtype is not null and secucode is not null and secucode != '' ), mid_df AS ( select subjectcode, eventsubject, b.ms as bondtype, regexp_replace( cname, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as cname, regexp_replace( sname, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as sname, secucode, eventdate, expiredate, url, concat( '100013', '&#&', c.latestIssueSize, '&#&', '2' ) as eigenvalue ,retrovalue from base_df as a left join seeyii_data_house.dwd_mt_ct_sysconst as b on a.bondtype = b.dm left join seeyii_data_house.dwd_mm_cn_bond_basicinfon c on a.innerCode = c.innerCode where b.lb = 1243 and b.dm is not null and c.filedate in ( select max(filedate) from seeyii_data_house.dwd_mm_cn_bond_basicinfon ) and c.datastatus != 3 ), final_df AS ( select *, concat( '公司已发行的', bondtype, '类债券', cname, '（', sname, '债券代码', secucode, '）将于两个月后到期，到期日为', expiredate, '。' ) as desc1 from mid_df ) insert into {ku}.{tb} partition (filedate = {fileDate}) select fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , 'SJ000402001' , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000402001' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000402002',
     " with base_df AS ( select fpshname as eventsubject, receivername, involvedsum, date_sub( date_format( date_format(enddate, 'yyyy-MM-dd'), 'yyyy-MM-dd' ), 60 ) as eventdate, compcode as subjectcode, date_format(enddate, 'yyyy-MM-dd') as expiredate, CAST(null AS STRING) as url, concat( concat( '100014', '&#&', pctoftotalshares, '&#&', '2' ) ) as eigenvalue ,concat_ws('&#&','股东股权冻结和质押', 'sy_cd_ms_sh_lc_sharefp', 'sourceId', string(id)) as retrovalue from ( select *, row_number() over( partition by id order by filedate desc ) as rnumber from seeyii_data_house.dwd_ms_sh_lc_sharefp ) as tb where rnumber = 1 and datastatus != 3 and typeselect = 3 and enddate is not null and receivername is not null and involvedsum is not null ), check_df AS ( select * from base_df where date_format( current_timestamp(), 'yyyy-MM-dd' ) >= eventdate ), next_df as ( select *, concat( '', '公司质押于', receivername, '的', involvedSum, '股股权将于两个月后到期', '，冻结质押期限截止日为', expiredate, '。' ) as desc1 from check_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000402002', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000402002' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from next_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');

    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000402003',
     " with sour_df as ( select * from seeyii_data_house.dwd_me_trad_nq_srmloan where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_trad_nq_srmloan where modifytime is not null ) ), next_df AS ( SELECT date_format(enddate, 'yyyy-MM-dd') as enddate, compcode as subjectcode, '' as eventsubject, 'SJ000402003' as eventtype, int( float(loanterm) * 365 ) as loanterm, bankpledge, other, rate, mortgagerate, concat( concat( '100015', '&#&', loanamount, '&#&', '3' ) ) as eigenvalue ,concat_ws('&#&','三板公司股权质押贷款表', 'sy_cd_me_trad_nq_srmloan', 'sourceId', string(id)) as retrovalue from sour_df where compcode is not NULL and enddate > '2021-01-01' and loanterm is not NULL and bankpledge is not NULL ), tmp_df as ( select *, date_add(enddate, loanterm) as expiredate from next_df ), add_desc_df as ( select *, CAST(null AS STRING) as url, date_sub(expiredate, 60) as eventdate, concat( '公司质押于', bankpledge, '的股权将于两个月后到期' ) as a, if( (mortgagerate is null) or (mortgagerate = ''), '', concat( '。本次质押股比', string( round(mortgagerate, 4) ), '%' ) ) as b, if( (rate is null) or (rate = ''), '', concat('，利率为', rate) ) as c, if( (other is null) or (other = ''), '', concat( '，存在其他情况：', other ) ) as d, '。' e from tmp_df ), final_df as ( select *, concat_ws('', a, b, c, d, e) as desc1 from add_desc_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), if(eventtype is NULL, '#', eventtype), if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000402004',
     " with sour_df as ( select * from seeyii_data_house.dwd_me_trad_nq_loan where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_trad_nq_loan where modifytime is not null ) ), next_df AS ( SELECT date_format(enddate, 'yyyy-MM-dd') as enddate, compcode as subjectcode, '' as eventsubject, 'SJ000402004' as eventtype, regexp_replace( loantype, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as loantype_r, regexp_replace( loanrate, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as loanrate_r, regexp_replace( loanpeople, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as loanpeople_r, regexp_replace( loanprice, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as loanprice_r, regexp_replace( fctype, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as fctype_r, regexp_replace( guaranteepeople, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as guaranteepeople_r, case WHEN isglation = '1' THEN '是' WHEN isglation = '0' THEN '不是' end as isglation, pawn, int( float(loanterm) * 365 ) as loanterm ,concat_ws('&#&','三板公司贷款表', 'sy_cd_me_trad_nq_loan', 'sourceId', string(id)) as retrovalue from sour_df where compcode is not NULL and enddate is not NULL and loanterm is not NULL ), tmp_df as ( select *, date_add(enddate, loanterm) as expiredate, concat( concat( '100016', '&#&', loanprice_r, '&#&', '2' ) ) as eigenvalue from next_df ), add_desc_df as ( select *, CAST(null AS STRING) as url, date_sub(expiredate, 60) as eventdate, concat('公司于', enddate) as a, if( (loanpeople_r is null) or (loanpeople_r = ''), '', concat( '公告向', loanpeople_r, '贷款' ) ) as b, if( (loanprice_r is null) or (loanprice_r = ''), '', concat( string( round(loanprice_r, 2) ), '万元' ) ) as c, if( (expiredate is null) or (expiredate = ''), '', concat( '，贷款期限', expiredate, '，将于两个月后到期' ) ) as d, if( (loantype_r is null) or (loantype_r = ''), '', concat( '。此笔贷款类型为', loantype_r ) ) as e, if( (fctype_r is null) or (fctype_r = ''), '', concat('，币种为', fctype_r) ) as f, if( (loanrate_r is null) or (loanrate_r = ''), '', concat( '，贷款利率', string( round(loanrate_r, 4) ), '%' ) ) as g, if( (pawn is null) or (pawn = ''), '', concat('，抵押物为', pawn) ) as h, if( (guaranteepeople_r is null) or (guaranteepeople_r = ''), '', concat( '，担保方为', guaranteepeople_r ) ) as i, if( (isglation is null) or (isglation = ''), '', concat( '，担保方', isglation, '公司关联方' ) ) as j, '。' k from tmp_df ), final_df as ( select *, concat_ws('', a, b, c, d, e, f, g, h, i, j, k) as desc1 from add_desc_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), if(eventtype is NULL, '#', eventtype), if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000402005',
     " with source_df as ( SELECT tb1.subjectcode, tb1.infopubldate, tb1.eventdate, cast(tb1.firstloansum AS STRING) as firstloansum, cast(tb1.loanterm AS STRING) as loanterm, tb1.currencyunit, cast(tb1.yearrate AS STRING) as yearrate, tb1.lender, tb1.guarantormortgageasset, tb1.guarantor, tb1.actions, tb2.eventsubject, tb3.ms1, tb1.expiredate, tb4.ms2, concat( concat( '100017', '&#&', firstloansum, '&#&', '2' ) ) as eigenvalue ,tb1.retrovalue FROM ( select CAST( date_format(infopubldate, 'yyyy-MM-dd') as string ) as infopubldate, compcode as subjectcode, lender, CAST( DATE_SUB(loanenddate, 60) as string ) as eventdate, date_format(loanenddate, 'yyyy-MM-dd') as expiredate, round( (firstloansum / 10000), 2 ) as firstloansum, round(loanterm, 2) as loanterm, currencyunit, round(yearrate, 2) as yearrate, guarantormortgageasset, guarantor, guarantorassociation, case when `actionways` = 1005 then '银行授信' when `actionways` = 1007 then '借入计划额度' when `actionways` = 1099 then '其他借贷' else null end as actions ,concat_ws('&#&','A股公司借贷明细表', 'sy_cd_me_trad_lc_credit', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_trad_lc_credit where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_trad_lc_credit ) and loanenddate is not null AND datastatus != 3 and disclosuremethod = 1 and actionways in (1005, 1007, 1099) and lender is not null and firstloansum is not null and compcode is not null ) AS tb1 JOIN ( select compcode, chiname as eventsubject from seeyii_data_house.dwd_ms_base_secumain where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_secumain ) AND datastatus != 3 AND secucategory = 1 and compcode is not null ) AS tb2 ON tb1.subjectcode = tb2.compcode JOIN ( select ms as ms1, dm from seeyii_data_house.dwd_mt_ct_sysconst where filedate in ( select max(filedate) from seeyii_data_house.dwd_mt_ct_sysconst ) AND datastatus != 3 AND lb = 1036 ) AS tb3 ON tb1.guarantorassociation = tb3.dm JOIN ( select ms as ms2, dm from seeyii_data_house.dwd_mt_ct_sysconst where filedate in ( select max(filedate) from seeyii_data_house.dwd_mt_ct_sysconst ) AND datastatus != 3 AND lb = 1068 ) AS tb4 ON tb1.currencyunit = tb4.dm ), des_dat as ( select subjectcode, eventdate, eigenvalue, retrovalue, eventsubject, CAST(null AS STRING) as url, 'SJ000402005' as eventtype, expiredate, concat( '公司于', infopubldate, '公告向', lender, '贷款', firstloansum, '万元' , if( (loanterm is null) or (loanterm = ''), '', concat( '，贷款期限', loanterm, '月' ) ) , '。此笔贷款将于两个月后到期' , if( (actions is null) or (actions = ''), '', concat('，类型为', actions) ) , if( (ms2 is null) or (ms2 = ''), '', concat('，币种为', ms2) ) , if( (yearrate is null) or (yearrate = ''), '', concat('，贷款利率', yearrate) ) , if( (guarantormortgageasset is null) or (guarantormortgageasset = ''), '', concat( '，抵押物为', guarantormortgageasset ) ) , if( (guarantor is null) or (guarantor = ''), '', concat('，担保方为', guarantor) ) , if( (ms1 is null) or (ms1 = ''), '', concat( '，担保方与上市公司关联关系为', ms1 ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate from source_df ), ret_dat as ( select retrovalue, eigenvalue, eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000401001',
     " with main_df as ( select * from seeyii_data_house.dwd_ms_base_secumain where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_secumain where modifytime is not null ) AND datastatus != 3 AND compcode is not null AND secucategory = 1 and listedstate = 1 ), sour_df as ( select * from seeyii_data_house.dwd_me_trad_sk_trustinvestsitn where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_trad_sk_trustinvestsitn where modifytime is not null ) AND datastatus != 3 AND compcode is not null AND ( trusteename != '' or trusteename is not NULL ) AND ( trustinvestname != '' or trustinvestname is not NULL ) AND trustinvestenddate is not NULL ), next_df AS ( SELECT date_format( a.trustinvestenddate, 'yyyy-MM-dd' ) as expiredate, concat( concat( '100018', '&#&', string( abs( bigint(trustinvestsum) ) ), '&#&', '2' ) ) as eigenvalue, b.compcode as subjectcode, b.chiname as eventsubject, 'SJ000401001' as eventtype, trusteename, trustinvestname ,concat_ws('&#&','A股公司投资理财表', 'sy_cd_me_trad_sk_trustinvestsitn', 'sourceId', string(a.id)) as retrovalue from sour_df as a join main_df as b on a.compCode = b.compCode ), tmp_df as ( select *, date_sub(expiredate, 60) as eventdate from next_df ), final_df as ( select *, CAST(null AS STRING) as url, concat( '公司于', trusteename, '处办理的委托理财', trustinvestname, '将于两个月后到期，到期日为', expiredate, '。' ) as desc1 from tmp_df where eventdate <= current_date() ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), if(eventtype is NULL, '#', eventtype), if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000401004',
     " with normal_df AS ( select compcode, compname from ( select compcode, compname, datastatus, row_number() over ( partition by sourceid order by filedate desc ) rnumber from seeyii_data_house.dwd_ms_base_normal_comp_list ) as tb1 where rnumber = 1 and datastatus != 3 ), base_df AS ( select shholdername as eventsubject, totchgamt, totavgprice, date_format(begindate, 'yyyy-MM-dd') as eventdate, shholdercode as subjectcode, CAST(null AS STRING) as url, date_format(enddate, 'yyyy-MM-dd') as expiredate, concat( concat( '100019', '&#&', if(totchgamt is null, '', totchgamt), '&#&', '2' ), '@@', concat( '100020', '&#&', if( totavgprice is null, '', totavgprice ), '&#&', '2' ) ) as eigenvalue, compcode as ent_code ,concat_ws('&#&','A股股东增减持计划表', 'sy_cd_me_trad_sk_sharehdchg', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_trad_sk_sharehdchg where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_me_trad_sk_sharehdchg ) and datastatus != 3 and isvalid = 1 and shholdernature = 1 and changedire = 2 and compcode is not null and shholdercode is not null and begindate is not null and begindate != '' and begindate > '1970-01-01' and enddate is not null and enddate != '' and enddate > '1970-01-01' ), add_df AS ( select *, b.compname from base_df as a left join normal_df as b on a.ent_code = b.compcode where b.compcode is not null ), next_df AS ( select *, if( (totchgamt is null), '', concat( '，变动数量总计', totchgamt, '股' ) ) as a, if( (totavgprice is null), '', concat( '，交易均价总计', totavgprice, '元/股' ) ) as b from add_df ), final_df AS ( select *, concat( eventdate, '，公司减持上市公司', compname, '的股份', a, b, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000401004', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000401004' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000101003',
     " with base_df AS ( select bigint(lyholdercode) as subjectcode, layoutholder as eventsubject, regexp_replace( layoutname, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as layoutname, date_format(noticedate, 'yyyy-MM-dd') as eventdate, url, CAST(null AS STRING) as expiredate ,concat_ws('&#&','集成电路布图设计专有权', 'sy_cd_me_buss_per_jcdlbtsj', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_per_jcdlbtsj where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_per_jcdlbtsj ) and datastatus != 3 and noticedate is not null and noticedate != '' and lyholdercode is not null and lyholdercode != '' and isvalid = 1 ), next_df AS ( select *, if( (layoutname is null) or (layoutname = ''), '', concat( '，布图设计名称为', layoutname ) ) as a from base_df ), final_df AS ( select *, concat( '本单位于', eventdate, '获得集成电路布图设计专有权', a, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000101003', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000101003' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, null as eigenvalue,retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000101004',
     " with base_xyyf_df AS ( select compname as eventsubject, compcode AS subjectcode, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate, date_format(stsdatebegin, 'yyyy-MM-dd') as eventdate, case when teststatus = '1' then '招募中' when teststatus = '2' then '招募完成' when teststatus = '3' then '已完成' when teststatus = '4' then 'IEC/IRB暂停' when teststatus = '5' then '进行中' when teststatus = '6' then '尚未招募' when teststatus = '7' then '主动暂停' when teststatus = '8' then '主动终止' when teststatus = '9' then '-' else null end as teststatus, teststage ,concat_ws('&#&','新药研发CDE药物临床试验公示数据', 'sy_cd_me_buss_new_drugs_cde', 'fingerId', string(fingerId)) as retrovalue from seeyii_data_house.dwd_me_buss_new_drugs_cde where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_new_drugs_cde ) and datastatus != 3 AND stsdatebegin is not null and stsdatebegin != '' and compcode is not null ), final_df as ( select *, concat( concat( '100022', '&#&', if(teststatus is null, '', teststatus), '&#&', '7' ), '@@', concat( '100171', '&#&', if(teststage is null, '', teststage), '&#&', '3' ) ) as eigenvalue, concat( eventdate, '，本单位新药研发进入新阶段，试验状态为', teststatus, '。' ) as desc1 from base_xyyf_df where eventdate is not null ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000101004', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000101004' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103001',
     " with base_df AS ( select * from ( select *, row_number() over( partition by id order by filedate desc ) as rnumber from seeyii_data_house.dwd_ms_base_tech_inno_comp_list ) as tb where rnumber = 1 and datastatus != 3 and setype in (5, 13, 88) AND publishdate is not null ), base_jscx_df AS ( select compname as eventsubject, date_format(publishdate, 'yyyy-MM-dd') as eventdate, compcode as subjectcode, CAST(null AS STRING) as url, case when level = '1' then '国家级' when level = '2' then '省级' when `level` = '3' then '市级' else null end as level, case when setype = '5' then '认定' when setype = '13' then '拟认定' when `setype` = '88' then '确认' else null end as setype ,concat_ws('&#&','技术创新示范企业', 'sy_cd_ms_base_tech_inno_comp_list', 'sourceId', string(id)) as retrovalue from base_df ), next_df as ( select *, concat( concat('100023', '&#&', level, '&#&', '7') ) as eigenvalue, concat( '公司于', eventdate, '获评“技术创新示范企业”称号', '，级别为', level, '，类型为', setype, '。' ) as desc1, CAST(null AS STRING) as expiredate from base_jscx_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000103001', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000103001' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from next_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103002',
     " with base_df AS ( select * from seeyii_data_house.dwd_ms_base_xjr_comp_list where datastatus != 3 and filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_ms_base_xjr_comp_list ) AND publishdate is not null ), base_xjr_df AS ( select compname as eventsubject, date_format(publishdate, 'yyyy-MM-dd') as eventdate, publishdate, case WHEN batchnum == '首批' then 1 WHEN batchnum == '第二批' then 2 WHEN batchnum == '第三批' then 3 WHEN batchnum == '第四批' then 4 WHEN batchnum == '第五批' then 5 WHEN batchnum == '第六批' then 6 end as batchnum, modifytime, compcode as subjectcode, case when complevel = '1' then '国家级' when complevel = '2' then '省级' when `complevel` = '3' then '市级' else null end as complevel ,concat_ws('&#&','专精特新小巨人企业', 'sy_cd_ms_base_xjr_comp_list', 'sourceId', string(id)) as retrovalue from base_df ), gp_df AS ( select complevel, a.eventsubject, a.eventdate, c.num, a.batchnum, a.subjectcode , a.retrovalue from base_xjr_df as a join ( select b.eventsubject, max(b.modifytime) as modifytime, count(b.eventsubject) as num from base_xjr_df as b group by b.eventsubject ) as c on a.eventsubject = c.eventsubject AND a.modifytime = c.modifytime ), gp_md_df as ( select * from gp_df where num > 1 ), xfs_df as ( select a.retrovalue, a.complevel, a.eventsubject, a.eventdate, a.subjectcode from gp_md_df as a join ( select eventsubject, max(batchnum) as bnum from gp_md_df group by eventsubject ) as b on a.eventsubject = b.eventsubject and a.batchnum = b.bnum ), union_df as ( select eventsubject, eventdate, subjectcode, complevel , retrovalue from gp_df where num = 1 union select eventsubject, eventdate, subjectcode, complevel , retrovalue from xfs_df ), next_df as ( select *, concat( concat( '100024', '&#&', complevel, '&#&', '7' ) ) as eigenvalue, concat( '公司于', eventdate, '获评“专精特新/小巨人企业”称号，级别为', complevel, '。' ) as desc1, CAST(null AS STRING) as expiredate, CAST(null AS STRING) as url from union_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000103002', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000103002' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from next_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103003',
     " with base_df AS ( select * from ( select *, row_number() over( partition by id order by filedate desc ) as rnumber from seeyii_data_house.dwd_ms_base_serv_mfg_comp_list ) as tb where rnumber = 1 and datastatus != 3 AND publishdate is not null ), base_fwx_df AS ( select compname as eventsubject, date_format(publishdate, 'yyyy-MM-dd') as eventdate, modifytime, compcode as subjectcode, CAST(null AS STRING) as url, case when complevel = '1' then '国家级' when complevel = '2' then '省级' when `complevel` = '3' then '市级' else null end as complevel, case when demotype = '1' then '示范企业' when demotype = '2' then '示范项目' when demotype = '3' then '示范平台' else null end as demotype, case when rawtype = '1' then '认定' when rawtype = '2' then '培育' when `rawtype` = '3' then '拟认定' when `rawtype` = '4' then '拟遴选' else null end as rawtype ,concat_ws('&#&','服务型制造示范企业', 'sy_cd_ms_base_serv_mfg_comp_list', 'sourceId', string(id)) as retrovalue from base_df ), next_df as ( select *, concat( concat( '100025', '&#&', complevel, '&#&', '7' ) ) as eigenvalue, concat( '公司于', eventdate, '获评“服务型制造示范企业”称号，级别为', complevel, '，示范类型为', demotype, '，类型为', rawtype, '。' ) as desc1, CAST(null AS STRING) as expiredate from base_fwx_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000103003', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000103003' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from next_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103004',
     " with source_df as ( select compname AS eventsubject, compcode AS subjectcode, publishdate as eventdate, case when `compLevel` = '1' then '国家级' when `compLevel` = '2' then '省级' else null end as lev ,concat_ws('&#&','制造业单项冠军', 'sy_cd_ms_base_zzygj_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_zzygj_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_zzygj_list ) AND compcode is not NULL AND datastatus != 3 AND publishdate is not NULL ), des_dat as ( select eventsubject, subjectcode, eventdate, concat( concat('100026', '&#&', lev, '&#&', '7') ) as eigenvalue, CAST(null AS STRING) as url, 'SJ000103004' as eventtype, CAST(null AS STRING) as expiredate, concat( '本单位于', eventdate, '获评“制造业单项冠军”称号' , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_df ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103005',
     " with sou_dat as ( select compname AS eventsubject, compcode AS subjectcode, date_format(publishdate, 'yyyy-MM-dd') as eventdate, identificationyear, sourceurl as url, case when `level` = '1' then '国家级' when `level` = '2' then '省级' when `level` = '3' then '市级' else null end as lev, case when `setype` = '9' then '拟备案' when `setype` = '79' then '考评合格' when `setype` = '1' then '备案' when `setype` = '85' then '考评优秀' when `setype` = '81' then '考评不合格' when `setype` = '17' then '撤销' when `setype` = '82' then '待考评' when `setype` = '86' then '考评良好' when `setype` = '80' then '整改' when `setype` = '34' then '培育' when `setype` = '87' then '孵化器认定变更' else null end as setype ,concat_ws('&#&','众创空间', 'sy_cd_ms_base_gen_space_list', 'fingerId', string(fingerId)) as retrovalue from seeyii_data_house.dwd_ms_base_gen_space_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_gen_space_list ) and publishdate is not null AND compcode is not NULL AND datastatus != 3 and identificationyear is not null AND setype in ('9', '1', '34', '87') ), des_dat as ( select eventsubject, subjectcode, eventdate, url, concat( concat('100027', '&#&', lev, '&#&', '7') ) as eigenvalue, retrovalue, 'SJ000103005' as eventtype, CAST(null AS STRING) as expiredate, concat( '本单位于', eventdate, '获评“众创空间”称号' , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ) , if( (setype is null) or (setype = ''), '', concat('，类型为', setype) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate from sou_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103007',
     " with source_dat as ( select compname AS eventsubject, case when `projlevel` = '2' then '省级' when `projlevel` = '3' then '市级' else null end as lev, compcode AS subjectcode, publishdate as eventdate, url, `year`, projname as p1 ,concat_ws('&#&','科技成果转化项目', 'sy_cd_ms_base_sata_transform_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_sata_transform_list where publishdate is not null AND compcode is not NULL and projlevel = '2' AND datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_sata_transform_list ) ), des_dat as ( select eventsubject, concat( concat('100028', '&#&', lev, '&#&', '7') ) as eigenvalue, subjectcode, eventdate, url, 'SJ000103007' as eventtype, CAST(null AS STRING) as expiredate, concat( '本单位于', eventdate, '获得省级科技成果转化项目' , if( (p1 is null) or (p1 = ''), '', concat(',项目名称为：', p1) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103008',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, regexp_replace( achievedname, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as achievedname, regexp_replace( achievedtype, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as achievedtype, date_format(registdate, 'yyyy-MM-dd') as eventdate, url, CAST(null AS STRING) as expiredate ,concat_ws('&#&','国家地方科技成果', 'sy_cd_ms_cn_comp_kjcgln', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_cn_comp_kjcgln where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_ms_cn_comp_kjcgln ) and datastatus != 3 and registdate is not null and registdate != '' and compcode is not null and isvalid = 1 ), next_df AS ( select *, concat( concat( '100029', '&#&', achievedtype, '&#&', '3' ) ) as eigenvalue, if( (achievedname is null) or (achievedname = ''), '', concat( '，成果名称为', achievedname ) ) as a, if( (achievedtype is null) or (achievedtype = ''), '', concat( '，成果类型为', achievedtype ) ) as b from base_df ), final_df AS ( select *, concat( '本单位于', eventdate, '，获得国家地方科技成果奖励', a, b, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000103008', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000103008' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102001',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, certifyear, date_format(pubtime, 'yyyy-MM-dd') as eventdate, gspc, gstype, CAST(null AS STRING) as expiredate, CAST(null AS STRING) as url ,concat_ws('&#&','高新技术企业公示', 'sy_cd_ms_cn_comp_innocom_gs', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_cn_comp_innocom_gs where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_ms_cn_comp_innocom_gs ) and datastatus != 3 and isvalid = 1 and compcode is not null and pubtime is not null ), next_df as ( select *, concat( '本单位于', eventdate, '被公示为高新技术企业' ) as a, if( (gspc is null) or (gspc = ''), '', concat('，批次为', gspc) ) as b, if( (gstype is null) or (gstype = ''), '。', concat( '，公示类型为', gstype, '。' ) ) as c from base_df ), desc_df as ( select *, concat_ws('', a, b, c) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000102001', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000102001' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, null as eigenvalue,retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from desc_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102003',
     " with source_dat AS ( select compname AS eventsubject, compcode AS subjectcode, complevel as level, cetificationyear, labernm, url, date_format(publishdate, 'yyyy-MM-dd') as eventdate, CASE WHEN rawType = '1' THEN '备案' WHEN rawType = '3' THEN '批建' WHEN rawType = '5' THEN '认定' WHEN rawType = '6' THEN '拟批建' WHEN rawType = '9' THEN '拟备案' WHEN rawType = '13' THEN '拟认定' WHEN rawType = '14' THEN '拟立项' WHEN rawType = '15' THEN '立项' WHEN rawType = '23' THEN '拟建设' WHEN rawType = '29' THEN '验收' WHEN rawType = '31' THEN '绩效考评' WHEN rawType = '33' THEN '评估验收' WHEN rawType = '37' THEN '拟组建' WHEN rawType = '34' THEN '培育' WHEN rawType = '38' THEN '组建' WHEN rawType = '39' THEN '拟奖补' END AS raw_type, CASE WHEN compLevel = '1' THEN '国家级' WHEN compLevel = '2' THEN '省级' WHEN compLevel = '3' THEN '市级' END AS comp_level, case when labortype = '1' then '学科类国家重点实验室' when labortype = '2' then '企业国家重点实验室' when labortype = '3' then '省部共建国家重点实验室' when labortype = '4' then '省市共建重点实验室' else '' end as labt ,concat_ws('&#&','企业重点实验室', 'sy_cd_ms_base_key_labor_list', 'fingerId', string(fingerId)) as retrovalue from seeyii_data_house.dwd_ms_base_key_labor_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_key_labor_list ) and compcode is not null AND publishdate is not null AND datastatus != 3 AND complevel is not NULL AND cetificationyear is not NULL and labortype = '2' and rawType is not null ), base_dat AS ( select eventsubject, subjectcode, eventdate, level, cetificationyear, labt, url, labernm, raw_type, comp_level , retrovalue from ( select eventsubject, subjectcode, level, cetificationyear, eventdate, labt, url, labernm, raw_type, comp_level, retrovalue, row_number() over ( partition by subjectcode, level, cetificationyear order by eventdate desc ) num2 from source_dat ) t where t.num2 = 1 ), des_dat as ( select retrovalue, eventsubject, subjectcode, eventdate, url, 'SJ000102003' as eventtype, concat( concat( '100030', '&#&', comp_level, '&#&', '7' ), '@@', concat( '100031', '&#&', raw_type, '&#&', '7' ) ) as eigenvalue, CAST(null AS STRING) as expiredate, concat( '公司于', eventdate, '被认定为', cetificationyear, '年度的企业国家重点实验室', if( (labernm is null) or (labernm = ''), '', concat( '，实验室名称为', labernm ) ), if( (raw_type is null) or (raw_type = ''), '', concat('，类型为', raw_type) ), '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, '{fileDate}' as filedate from base_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, retrovalue, fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), if(eventtype is NULL, '#', eventtype), if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102004',
     " with source_df as ( select compname AS eventsubject, compcode AS subjectcode, publishdate as eventdate, publishyear, case when `level` = '1' then '国家级' when `level` = '2' then '省级' when `level` = '3' then '市级' when `level` = '4' then '直辖市级' else null end as lev, case when `setype` = '13' then '拟认定' when `setype` = '95' then '拟通过复核' when `setype` = '5' then '认定' when `setype` = '92' then '通过复核' when `setype` = '108' then '绩效考评优秀' when `setype` = '106' then '绩效考评良好' when `setype` = '93' then '复评优秀' when `setype` = '94' then '复评合格' when `setype` = '119' then '拟安排' when `setype` = '34' then '培育' when `setype` = '120' then '培育公示' when `setype` = '121' then '拟入选' when `setype` = '122' then '拟通过考核' when `setype` = '31' then '绩效考评' when `setype` = '123' then '通过考核' when `setype` = '101' then '绩效考评合格' when `setype` = '127' then '拟复核通过' when `setype` = '128' then '复核通过' else null end as sety ,concat_ws('&#&','工业设计中心', 'sy_cd_ms_base_dustry_des_center_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_dustry_des_center_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_dustry_des_center_list ) AND compcode is not NULL AND datastatus != 3 AND publishdate is not NULL ), base_dat AS ( select * from ( select retrovalue, eventsubject, subjectcode, eventdate, sety, lev, publishyear, concat( concat('100032', '&#&', lev, '&#&', '7') , '@@' , concat('100033', '&#&', sety, '&#&', '7') ) as eigenvalue, row_number() over ( partition by subjectcode, lev order by publishyear desc ) num2 from source_df ) t where t.num2 = 1 ), des_dat as ( select retrovalue, eventsubject, subjectcode, eventdate, eigenvalue, CAST(null AS STRING) as url, 'SJ000102004' as eventtype, CAST(null AS STRING) as expiredate, concat( '公司于近期被认定为', publishyear, '年度的', lev, '工业设计中心' , if( (sety is null) or (sety = ''), '', concat(',类型为：', sety) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate from base_dat ), ret_dat as ( select retrovalue, eigenvalue, eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102005',
     " with source_dat AS ( select compname AS eventsubject, case when `setype` = '13' then '拟认定' when `setype` = '5' then '认定' when `setype` = '101' then '绩效考评合格' when `setype` = '90' then '拟新建' when `setype` = '102' then '新创建' when `setype` = '31' then '绩效考评' when `setype` = '103' then '拟通过年度评价' when `setype` = '104' then '拟通过资格核查' when `setype` = '105' then '通过年度评价' when `setype` = '106' then '绩效考评良好' when `setype` = '107' then '绩效考评基本合格' when `setype` = '108' then '绩效考评优秀' when `setype` = '109' then '复评' when `setype` = '110' then '拟认定绩效考评合格' when `setype` = '111' then '通过资格核查' when `setype` = '94' then '复评合格' when `setype` = '112' then '绩效基本考评合格' when `setype` = '113' then '拟认定绩效考评优秀' when `setype` = '114' then '复评基本合格' when `setype` = '115' then '拟推荐申报' when `setype` = '116' then '绩效考评通过' when `setype` = '117' then '拟奖励' when `setype` = '118' then '拟增补认定' when `setype` = '125' then '拟绩效考评优秀' when `setype` = '124' then '拟绩效考评良好' when `setype` = '126' then '拟绩效考评合格' when `setype` = '130' then '拟绩效考评基本合格' when `setype` = '90' then '拟新建' when `setype` = '108' then '绩效考评优秀' when `setype` = '101' then '绩效考评合格' when `setype` = '106' then '绩效考评良好' when `setype` = '107' then '绩效考评基本合格' else null end as sety, compcode AS subjectcode, projectname, identificationyear, sourceurl as url, date_format(publishdate, 'yyyy-MM-dd') as eventdate, case when `level` = '1' then '国家级' when `level` = '2' then '省级' when `level` = '3' then '市级' when `level` = '4' then '直辖市级' else null end as lev ,concat_ws('&#&','企业技术中心', 'sy_cd_ms_base_tech_center_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_tech_center_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_tech_center_list ) and compcode is not null AND publishdate is not null AND datastatus != 3 AND level is not NULL AND identificationyear is not NULL ), des_dat as ( select retrovalue, eventsubject, concat( concat('100034', '&#&', lev, '&#&', '7'), '@@', concat('100035', '&#&', sety, '&#&', '7') ) as eigenvalue, subjectcode, eventdate, url, 'SJ000102005' as eventtype, CAST(null AS STRING) as expiredate, concat( '公司于', eventdate, '被认定为', identificationyear, '年度的', lev, '企业技术中心' , if( (projectname is null) or (projectname = ''), '', concat( '，项目名称为', projectname ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate from source_dat ), ret_dat as ( select retrovalue, eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');


    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102006',
     " with source_dat AS ( select compname AS eventsubject, compcode AS subjectcode, projname, url, cetificationtime, date_format(publishdate, 'yyyy-MM-dd') as eventdate, case when `complevel` = '1' then '国家级' when `complevel` = '2' then '省级' when `complevel` = '3' then '市级' else null end as lev, case when `rawtype` = '3' then '批建' when `rawtype` = '6' then '拟批建' when `rawtype` = '13' then '拟认定' when `rawtype` = '17' then '撤销' when `rawtype` = '18' then '认证' when `rawtype` = '25' then '保留院士专家工作站' when `rawtype` = '96' then '验收通过' when `rawtype` = '105' then '绩效考评合格' when `rawtype` = '106' then '限期整改' when `rawtype` = '107' then '拟认证' when `rawtype` = '108' then '需整改' when `rawtype` = '109' then '绩效考评优秀' when `rawtype` = '110' then '更名' when `rawtype` = '111' then '绩效考评不合格' when `rawtype` = '112' then '绩效考核良好' when `rawtype` = '119' then '绩效考评优合格' else null end as rawty ,concat_ws('&#&','院士专家工作站(科协评定)', 'sy_cd_ms_base_acade_expert_station_list', 'fingerId', string(fingerId)) as retrovalue from seeyii_data_house.dwd_ms_base_acade_expert_station_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_acade_expert_station_list ) and compcode is not null AND publishdate is not null AND datastatus != 3 AND complevel is not NULL AND cetificationtime is not NULL and rawtype in ( '3', '6', '18', '96', '105', '107', '109', '112', '119' ) ), des_dat as ( select eventsubject, subjectcode, eventdate, url, concat( concat('100036', '&#&', lev, '&#&', '7'), '@@', concat('100037', '&#&', rawty, '&#&', '7') ) as eigenvalue, 'SJ000102006' as eventtype, CAST(null AS STRING) as expiredate, concat( '公司于', eventdate, '被认定为', cetificationtime, '年度的', lev, '院士专家工作站', if( (rawty is null) or (rawty = ''), '', concat('，类型为：', rawty) ), if( (projname is null) or (projname = ''), '', concat( '，涉及项目为：', projname ) ), '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, '{fileDate}' as filedate , retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), if(eventtype is NULL, '#', eventtype), if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102007',
     " with source_dat AS ( select compname AS eventsubject, compcode AS subjectcode, `year`, url, projname, date_format(publishdate, 'yyyy-MM-dd') as eventdate, case when `complevel` = '1' then '国家级' when `complevel` = '2' then '省级' when `complevel` = '3' then '市级' else null end as lev, case when `rawtype` = '5' then '认定' when `rawtype` = '13' then '拟认定' when `rawtype` = '23' then '拟建设' when `rawtype` = '64' then '批准建设' when `rawtype` = '65' then '拟验收' when `rawtype` = '66' then '验收通过' when `rawtype` = '105' then '绩效考评合格' when `rawtype` = '118' then '拟奖励' else null end as rawty ,concat_ws('&#&','工程研究中心&工程实验室发改委评定', 'sy_cd_ms_base_engin_res_labor_list', 'fingerId', string(fingerId)) as retrovalue from seeyii_data_house.dwd_ms_base_engin_res_labor_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_engin_res_labor_list ) and compcode is not null AND publishdate is not null AND datastatus != 3 AND complevel is not NULL AND `year` is not NULL ), des_dat as ( select eventsubject, subjectcode, eventdate, url, concat( concat('100038', '&#&', lev, '&#&', '7') , '@@' , concat('100039', '&#&', rawty, '&#&', '7') ) as eigenvalue, 'SJ000102007' as eventtype, CAST(null AS STRING) as expiredate, concat( '公司于', eventdate, '被认定为', `year`, '年度的', lev, '工程研究中心&工程实验室' , if( (projname is null) or (projname = ''), '', concat( '，涉及项目为：', projname ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), if( eventtype is NULL, '#', eventtype ), if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if( eventdate is NULL, '#', eventdate ), if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102008',
     " with source_dat AS ( select compname AS eventsubject, compcode AS subjectcode, cetificationyear, url, date_format(publishdate, 'yyyy-MM-dd') as eventdate, case when `complevel` = '2' then '省级' when `complevel` = '3' then '市级' else null end as lev, case when `rawtype` = '1' then '备案' when `rawtype` = '5' then '认定' when `rawtype` = '9' then '拟备案' when `rawtype` = '13' then '拟认定' when `rawtype` = '14' then '拟立项' when `rawtype` = '34' then '培育' when `rawtype` = '43' then '试点培育' when `rawtype` = '45' then '拟培育' when `rawtype` = '46' then '拟支持' when `rawtype` = '47' then '评估' when `rawtype` = '48' then '拟命名' when `rawtype` = '67' then '拟补助' when `rawtype` = '68' then '补助' when `rawtype` = '102' then '绩效测评' when `rawtype` = '103' then '试点建设' else null end as rawty ,concat_ws('&#&','新型研发机构', 'sy_cd_ms_base_new_res_deve_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_new_res_deve_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_new_res_deve_list ) and compcode is not null AND publishdate is not null AND datastatus != 3 AND complevel is not NULL AND cetificationyear is not NULL ), des_dat as ( select eventsubject, concat( concat('100040', '&#&', lev, '&#&', '7'), '@@', concat('100041', '&#&', rawty, '&#&', '7') ) as eigenvalue, subjectcode, eventdate, url, 'SJ000102008' as eventtype, CAST(null AS STRING) as expiredate, concat( '公司于', eventdate, '被认定为', cetificationyear, '年度的', lev, '新型研发机构', '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102009',
     " with source_df as ( select publishdate, compcode, compname, `year`, projname, case when `compLevel` = '2' then '省级' else null end as level, case when `rawtype` = '1' then '备案' when `rawtype` = '5' then '认定' when `rawtype` = '13' then '拟认定' when `rawtype` = '34' then '培育' when `rawtype` = '97' then '复检合格' else null end as rawtype ,concat_ws('&#&','企业研发中心', 'sy_cd_ms_base_enterprise_rdc_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_enterprise_rdc_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_enterprise_rdc_list ) and datastatus != 3 ), next_df AS ( SELECT date_format(publishdate, 'yyyy-MM-dd') as eventdate, concat( concat('100042', '&#&', level, '&#&', '7') , '@@' , concat( '100043', '&#&', rawtype, '&#&', '7' ) ) as eigenvalue, compcode as subjectcode, compname as eventsubject, 'SJ000102009' as eventtype, level, rawtype, `year`, projname ,retrovalue from source_df where publishdate is not NULL and compcode is not NULL AND level is not NULL AND `year` is not NULL ), add_desc_df as ( select *, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate, concat( '公司于', eventdate, '被认定为' ) as a, if( (year is null) or (year = ''), '', concat(year, '年度的') ) as b, if( (level is null) or (level = ''), '', level ) as c, '企业研发中心' as d, if( (rawtype is null) or (rawtype = ''), '', concat('，类型为：', rawtype) ) as e, if( (projname is null) or (projname = ''), '', concat('，项目名称为', projname) ) as f, '。' as g from next_df ), final_df as ( select *, concat_ws('', a, b, c, d, e, f, g) as desc1 from add_desc_df ) insert into {ku}.{tb} partition (filedate = {fileDate}) select fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000301003',
     " with source_dat AS ( select eventdate, eventsubject, subjectcode, proname, protype, url, approvaldep, extractDigit(totalinvest) as totalinvest ,retrovalue from ( select date_format(approvaltime, 'yyyy-MM-dd') as eventdate, protype, url, approvalresult, totalinvest, regexp_replace( proname, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as proname, compname AS eventsubject, compcode AS subjectcode, datastatus, isvalid, approvaldep, row_number() over ( partition by id order by filedate desc ) num ,concat_ws('&#&','备案核准投资项目', 'sy_cd_ms_cn_comp_invest', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_cn_comp_invest ) t where t.num = 1 and t.eventdate is not null and t.subjectcode is not NULL AND t.datastatus != 3 AND isvalid = 1 AND approvalresult = '通过' and proname is not NULL and totalinvest is not null ), des_dat as ( select eventsubject, subjectcode, eventdate, url, concat( concat( '100044', '&#&', totalinvest, '&#&', '3' ) ) as eigenvalue, 'SJ000301003' as eventtype, CAST(null AS STRING) as expiredate, concat( '公司', protype, '项目', proname, if( (approvaldep is null) or (approvaldep = ''), '', concat( '，于', eventdate, '获得审批部门', approvaldep, '审批' ) ) , '，审批结果为通过' , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000104003',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, sourceurl as url, regexp_replace( projectname, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as projectname, compname AS eventsubject, compcode AS subjectcode, specificname, sumprice, case when `level` = '1' then '省级' when `level` = '2' then '市级' else null end as lev ,concat_ws('&#&','工业与信息化发展专项资金支持项目', 'sy_cd_ms_base_deve_supp_proj_list', 'fingerId', string(fingerId)) as retrovalue from seeyii_data_house.dwd_ms_base_deve_supp_proj_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_deve_supp_proj_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 ), des_dat as ( select eventsubject, concat( concat( '100045', '&#&', if(sumprice is null, '', sumprice), '&#&', '3' ), '@@', concat( '100046', '&#&', if(lev is null, '', lev), '&#&', '7' ) ) as eigenvalue, subjectcode, eventdate, url, 'SJ000104003' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目获得工业与信息化发展专项资金支持' , if( (projectname is null) or (projectname = ''), '', concat( '，项目名称为', projectname ) ) , if( (specificname is null) or (specificname = ''), '', concat('，名目为', specificname) ) , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ) , if( (sumprice is null) or (sumprice = ''), '', concat( '，补助金额', sumprice, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000104004',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, sourceurl as url, projectname, compname AS eventsubject, compcode AS subjectcode, specificname, sumprice, case when `level` = '1' then '国家级' when `level` = '2' then '省级' when `level` = '3' then '市级' else null end as lev ,concat_ws('&#&','工业转型升级专项资金支持项目', 'sy_cd_ms_base_tran_up_proj_list', 'fingerId', string(fingerId)) as retrovalue from seeyii_data_house.dwd_ms_base_tran_up_proj_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_tran_up_proj_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 ), des_dat as ( select eventsubject, subjectcode, eventdate, url, concat( concat( '100047', '&#&', if(sumprice is null, '', sumprice), '&#&', '3' ) , '@@' , concat( '100048', '&#&', if(lev is null, '', lev), '&#&', '7' ) ) as eigenvalue, 'SJ000104004' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目获得工业转型升级专项资金支持' , if( (projectname is null) or (projectname = ''), '', concat('，项目名称', projectname) ) , if( (specificname is null) or (specificname = ''), '', concat('，名目为', specificname) ) , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ) , if( (sumprice is null) or (sumprice = ''), '', concat( '，补助金额', sumprice, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000104005',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, sourceurl as url, projectname, compname AS eventsubject, compcode AS subjectcode, specificname, sumprice, case when `grade` = '1' then '省级' else null end as lev ,concat_ws('&#&','产业结构调整专项资金支持项目（国家级+省级）', 'sy_cd_ms_base_ind_adj_spefu_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_ind_adj_spefu_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_ind_adj_spefu_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 ), des_dat as ( select eventsubject, concat( concat( '100049', '&#&', if(sumprice is null, '', sumprice), '&#&', '3' ), '@@', concat( '100050', '&#&', if(lev is null, '', lev), '&#&', '7' ) ) as eigenvalue, subjectcode, eventdate, url, 'SJ000104005' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目获得产业结构调整专项资金支持项目' , if( (projectname is null) or (projectname = ''), '', concat( '，项目名称为', projectname ) ) , if( (specificname is null) or (specificname = ''), '', concat('，名目为', specificname) ) , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ) , if( (sumprice is null) or (sumprice = ''), '', concat( '，补助金额', sumprice, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000104006',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, projectname, compname AS eventsubject, compcode AS subjectcode, specificname, sumprice, case when `level` = '1' then '省级' when `level` = '2' then '市级' else null end as lev ,concat_ws('&#&','技术改造专项资金支持项目', 'sy_cd_ms_base_tech_tran_proj_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_tech_tran_proj_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_tech_tran_proj_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 ), des_dat as ( select eventsubject, subjectcode, eventdate, concat( concat( '100051', '&#&', if(sumprice is null, '', sumprice), '&#&', '3' ) , '@@' , concat( '100052', '&#&', if(lev is null, '', lev), '&#&', '7' ) ) as eigenvalue, 'SJ000104006' as eventtype, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目获得技术改造专项资金支持项目' , if( (projectname is null) or (projectname = ''), '', concat( '，项目名称为', projectname ) ) , if( (specificname is null) or (specificname = ''), '', concat('，名目为', specificname) ) , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ) , if( (sumprice is null) or (sumprice = ''), '', concat( '，补助金额', sumprice, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000104007',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, projectname, compname AS eventsubject, compcode AS subjectcode, specificname, sumprice, case when `grade` = '2' then '省级' when `grade` = '1' then '国家级' else null end as lev ,concat_ws('&#&','中小企业发展专项资金支持项目（国家级+省级）', 'sy_cd_ms_base_cg_sme_spefu_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_cg_sme_spefu_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_cg_sme_spefu_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 ), des_dat as ( select eventsubject, concat( concat( '100053', '&#&', if(sumprice is null, '', sumprice), '&#&', '3' ), '@@', concat( '100054', '&#&', if(lev is null, '', lev), '&#&', '7' ) ) as eigenvalue, subjectcode, eventdate, 'SJ000104007' as eventtype, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目获得中小企业发展专项资金支持项目' , if( (projectname is null) or (projectname = ''), '', concat( '，项目名称为', projectname ) ) , if( (specificname is null) or (specificname = ''), '', concat('，名目为', specificname) ) , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ) , if( (sumprice is null) or (sumprice = ''), '', concat( '，补助金额', sumprice, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000104008',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, projectname, compname AS eventsubject, compcode AS subjectcode, specificname, sumprice, case when `level` = '1' then '国家级' when `level` = '2' then '省级' when `level` = '3' then '市级' else null end as lev ,concat_ws('&#&','中央引导地方科技发展资金支持项目（国家级+省级）', 'sy_cd_ms_base_cg_std_fund_list', 'fingerId', string(fingerId)) as retrovalue from seeyii_data_house.dwd_ms_base_cg_std_fund_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_cg_std_fund_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 ), des_dat as ( select eventsubject, subjectcode, eventdate, concat( concat( '100055', '&#&', if(sumprice is null, '', sumprice), '&#&', '3' ) , '@@' , concat( '100056', '&#&', if(lev is null, '', lev), '&#&', '7' ) ) as eigenvalue, 'SJ000104008' as eventtype, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目获得中央引导地方科技发展资金支持项目' , if( (projectname is null) or (projectname = ''), '', concat( '，项目名称为', projectname ) ) , if( (specificname is null) or (specificname = ''), '', concat('，名目为', specificname) ) , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ) , if( (sumprice is null) or (sumprice = ''), '', concat( '，补助金额', sumprice, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000104009',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, projectname, `year` , compname AS eventsubject, compcode AS subjectcode, specificname, sumprice, case when `level` = '1' then '省级' else null end as lev ,concat_ws('&#&','科技创新券补贴企业（国家级+省级）', 'sy_cd_ms_base_tech_inn_comp_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_tech_inn_comp_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_tech_inn_comp_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 ), des_dat as ( select eventsubject, subjectcode, eventdate, concat( concat( '100057', '&#&', if(sumprice is null, '', sumprice), '&#&', '3' ) , '@@' , concat( '100058', '&#&', if(lev is null, '', lev), '&#&', '7' ) ) as eigenvalue, 'SJ000104009' as eventtype, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate, concat( eventdate, if( (`year` is null) or (`year` = ''), '', concat( '，公司项目获得', `year`, '年度科技创新券补贴' ) ) , if( (projectname is null) or (projectname = ''), '', concat( '，项目名称为', projectname ) ) , if( (specificname is null) or (specificname = ''), '', concat('，名目为', specificname) ) , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ) , if( (sumprice is null) or (sumprice = ''), '', concat( '，补助金额', sumprice, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000107001',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, projectname, identificationyear, sourceurl as url, compname AS eventsubject, compcode AS subjectcode, specificname, sumprice, case when grade = '1' then '省级' when grade = '2' then '国家级' else null end as lev ,concat_ws('&#&','地方政府专项资金奖补项目（国家级+省级）', 'sy_cd_ms_base_loc_gov_poj_list', 'fingerId', string(fingerId)) as retrovalue from seeyii_data_house.dwd_ms_base_loc_gov_poj_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_loc_gov_poj_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 AND identificationyear is not NULL ), des_dat as ( select eventsubject, concat( concat( '100059', '&#&', if(sumprice is null, '', sumprice), '&#&', '3' ), '@@', concat( '100060', '&#&', if(lev is null, '', lev), '&#&', '7' ) ) as eigenvalue, subjectcode, eventdate, url, 'SJ000107001' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, if( (identificationyear is null) or (identificationyear = ''), '', concat( '，公司项目获得', identificationyear, '年度地方政府专项资金奖补' ) ) , if( (projectname is null) or (projectname = ''), '', concat( '，项目名称为', projectname ) ) , if( (specificname is null) or (specificname = ''), '', concat('，名目为', specificname) ) , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ) , if( (sumprice is null) or (sumprice = ''), '', concat( '，补助金额', sumprice, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000107002',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, projectname, identificationyear, sourceurl as url, compname AS eventsubject, compcode AS subjectcode, specificname, sumprice, case when grade = '1' then '省级' else null end as lev ,concat_ws('&#&','地方政府专项资金奖补企业（国家级+省级）', 'sy_cd_ms_base_loc_gov_comp_list', 'fingerId', string(fingerId)) as retrovalue from seeyii_data_house.dwd_ms_base_loc_gov_comp_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_loc_gov_comp_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 AND identificationyear is not NULL ), des_dat as ( select eventsubject, subjectcode, eventdate, url, concat( concat( '100061', '&#&', if(sumprice is null, '', sumprice), '&#&', '3' ) , '@@' , concat( '100062', '&#&', if(lev is null, '', lev), '&#&', '7' ) ) as eigenvalue, 'SJ000107002' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, if( (identificationyear is null) or (identificationyear = ''), '', concat( '，公司获评', identificationyear, '年度地方政府专项资金奖补企业' ) ) , if( (projectname is null) or (projectname = ''), '', concat( '，项目名称为', projectname ) ) , if( (specificname is null) or (specificname = ''), '', concat('，名目为', specificname) ) , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ) , if( (sumprice is null) or (sumprice = ''), '', concat( '，补助金额', sumprice, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102011',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, url, compname AS eventsubject, compcode AS subjectcode, specificname, case when complevel = '1' then '国家级' when complevel = '2' then '省级' else null end as lev ,concat_ws('&#&','知识产权优势和示范企业', 'sy_cd_ms_base_ipr_devdemo_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_ipr_devdemo_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_ipr_devdemo_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 AND certname = 1 ), des_dat as ( select eventsubject, concat( concat('100063', '&#&', lev, '&#&', '7') ) as eigenvalue, subjectcode, eventdate, url, 'SJ000102011' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司获评知识产权优势企业' , if( (specificname is null) or (specificname = ''), '', concat('，名目为', specificname) ) , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ), '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102012',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, url, compname AS eventsubject, compcode AS subjectcode, specificname, case when complevel = '1' then '国家级' when complevel = '2' then '省级' else null end as lev ,concat_ws('&#&','知识产权优势和示范企业', 'sy_cd_ms_base_ipr_devdemo_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_ipr_devdemo_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_ipr_devdemo_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 AND certname = 2 ), des_dat as ( select eventsubject, concat( concat('100064', '&#&', lev, '&#&', '7') ) as eigenvalue, subjectcode, eventdate, url, 'SJ000102012' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司获评知识产权示范企业' , if( (specificname is null) or (specificname = ''), '', concat('，名目为', specificname) ) , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ), '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102013',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, url, compname AS eventsubject, compcode AS subjectcode, specificname, case when complevel = '1' then '国家级' when complevel = '2' then '省级' else null end as lev ,concat_ws('&#&','绿色工厂', 'sy_cd_ms_base_green_fty_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_green_fty_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_green_fty_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 ), des_dat as ( select eventsubject, concat( concat('100065', '&#&', lev, '&#&', '7') ) as eigenvalue, subjectcode, eventdate, url, 'SJ000102013' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司获评绿色工厂认定' , if( (specificname is null) or (specificname = ''), '', concat('，名目为', specificname) ) , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102014',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, url, proname, compname AS eventsubject, compcode AS subjectcode, specificname, case when complevel = '1' then '国家级' when complevel = '2' then '省级' else null end as lev ,concat_ws('&#&','绿色设计示范企业', 'sy_cd_ms_base_green_demonprise_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_green_demonprise_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_green_demonprise_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 AND proname is not NULL ), des_dat as ( select eventsubject, concat( concat('100066', '&#&', lev, '&#&', '7') ) as eigenvalue, subjectcode, eventdate, url, 'SJ000102014' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, if( (proname is null) or (proname = ''), '', concat( '，公司因', proname, '项目/产品获评绿色设计示范企业' ) ) , if( (specificname is null) or (specificname = ''), '', concat('，名目为', specificname) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102015',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, url, compname AS eventsubject, compcode AS subjectcode, specificname, case when complevel = '1' then '国家级' when complevel = '2' then '省级' else null end as lev ,concat_ws('&#&','绿色供应链管理企业', 'sy_cd_ms_base_green_supl_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_green_supl_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_green_supl_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 ), des_dat as ( select eventsubject, concat( concat('100067', '&#&', lev, '&#&', '7') ) as eigenvalue, subjectcode, eventdate, url, 'SJ000102015' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司获评绿色供应链管理企业' , if( (specificname is null) or (specificname = ''), '', concat('，名目为', specificname) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    




    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102023',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, url, specificname, compname AS eventsubject, compcode AS subjectcode, proname, case when complevel = '1' then '国家级' when complevel = '2' then '省级' when complevel = '3' then '市级' else null end as lev ,concat_ws('&#&','首台（套）重大技术装备', 'sy_cd_ms_base_major_techpment_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_major_techpment_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_major_techpment_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 ), des_dat as ( select eventsubject, subjectcode, eventdate, url, concat( concat('100068', '&#&', lev, '&#&', '7') ) as eigenvalue, 'SJ000102023' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司获得首台（套）重大技术装备认定' , if( (proname is null) or (proname = ''), '', concat( '，获评项目/装备为', proname ) ) , if( (specificname is null) or (specificname = ''), '', concat('，名目为', specificname) ) , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106003',
     " with source_dat AS ( select date_format(appdate, 'yyyy-MM-dd') as eventdate, url, productname, concat( concat( '100069', '&#&', if( exportstate is null, '', exportstate ), '&#&', '3' ), '@@', concat( '100070', '&#&', if(cropname is null, '', cropname), '&#&', '3' ) ) as eigenvalue, compname AS eventsubject, compcode AS subjectcode, cropname ,concat_ws('&#&','种子进口审批证表', 'sy_cd_me_buss_per_zzjkpz', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_per_zzjkpz where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_per_zzjkpz ) and appdate is not null and compcode is not NULL AND datastatus != 3 AND isvalid = 1 ), des_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, url, 'SJ000106003' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司获得种子进口审批' , if( (cropname is null) or (cropname = ''), '', concat('，作物为', cropname) ) , if( (productname is null) or (productname = ''), '', concat( '，品种名称为', productname ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106004',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, regexp_replace( cropname, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as cropname, regexp_replace( productname, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as productname, date_format(appdate, 'yyyy-MM-dd') as eventdate, url, CAST(null AS STRING) as expiredate, concat( concat( '100071', '&#&', if( exportstate is null, '', exportstate ), '&#&', '3' ), '@@', concat( '100072', '&#&', if(cropname is null, '', cropname), '&#&', '3' ) ) as eigenvalue ,concat_ws('&#&','种子出口审批证表', 'sy_cd_me_buss_per_zzckpz', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_per_zzckpz where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_per_zzckpz ) and datastatus != 3 and appdate is not null and appdate != '' and compcode is not null and isvalid = 1 ), next_df AS ( select *, if( (cropname is null) or (cropname = ''), '', concat('，作物为', cropname) ) as a, if( (productname is null) or (productname = ''), '', concat( '，品种名称为', productname ) ) as b from base_df ), final_df AS ( select *, concat( eventdate, '，公司获得种子出口审批', a, b, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000106004', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000106004' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000105004',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, regexp_replace( approvedproduct, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as approvedproduct, date_format(noticedate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate ,concat_ws('&#&','绿色食品获证企业', 'sy_cd_me_buss_per_lssphz', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_per_lssphz where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_per_lssphz ) and datastatus != 3 and noticedate is not null and noticedate != '' and compcode is not null and isvalid = 1 ), next_df AS ( select *, if( (approvedproduct is null) or (approvedproduct = ''), '', concat( '，获评产品为', approvedproduct ) ) as a from base_df ), final_df AS ( select *, concat( eventdate, '，公司获得绿色食品认证', a, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000105004', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000105004' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, null as eigenvalue,retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000105008',
     " with source_dat AS ( select date_format(issuedate, 'yyyy-MM-dd') as eventdate, endtime as expiredate, url, compname AS eventsubject, compcode AS subjectcode, productname ,concat_ws('&#&','环保产品认证', 'sy_cd_me_buss_per_hbcprz', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_per_hbcprz where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_per_hbcprz ) and issuedate is not null and compcode is not NULL AND datastatus != 3 AND isvalid = 1 ), des_dat as ( select eventsubject, subjectcode, eventdate, url, expiredate, 'SJ000105008' as eventtype, concat( eventdate, '，公司获得环保产品认证' , if( (productname is null) or (productname = ''), '', concat( '，获评产品名称/服务类别为', productname ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, null as eigenvalue,retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000105009',
     " with source_dat AS ( select date_format(issuedate, 'yyyy-MM-dd') as eventdate, certdate as expiredate, url, compname AS eventsubject, compcode AS subjectcode, proname ,concat_ws('&#&','绿色建材认证', 'sy_cd_me_buss_per_lsjcrz', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_per_lsjcrz where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_per_lsjcrz ) and issuedate is not null and compcode is not NULL AND datastatus != 3 AND isvalid = 1 ), des_dat as ( select eventsubject, subjectcode, eventdate, url, expiredate, 'SJ000105009' as eventtype, concat( eventdate, '，公司获得绿色建材认证' , if( (proname is null) or (proname = ''), '', concat( '，获评产品名称为', proname ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, null as eigenvalue,retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106008',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, url, compname AS eventsubject, compcode AS subjectcode, batch, techprod, case when `complevel` = '1' then '国家级' when `complevel` = '2' then '省级' when `complevel` = '3' then '市级' else null end as lev, case when rawtype = '0' then '取消资格' when rawtype = '1' then '认定' else null end as tp ,concat_ws('&#&','备案节能服务公司', 'sy_cd_ms_base_nrg_save_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_nrg_save_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_nrg_save_list ) and publishdate is not null and compcode is not NULL AND datastatus != 3 and rawtype = '1' ), des_dat as ( select eventsubject, subjectcode, eventdate, url, 'SJ000106008' as eventtype, concat('100073', '&#&', lev, '&#&', '7') as eigenvalue, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司获评备案节能服务公司', if( (techprod is null) or (techprod = ''), '', concat( '，主要节能业务及技术产品为', techprod ) ), if( (tp is null) or (tp = ''), '', concat('，类型为', tp) ), if( (batch is null) or (batch = ''), '', concat('，批次为', batch) ), '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, '{fileDate}' as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), if(eventtype is NULL, '#', eventtype), if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106012',
     " with source_dat AS ( select date_format(approvaltime, 'yyyy-MM-dd') as eventdate, url, compname AS eventsubject, compcode AS subjectcode, declarecategory, gamename ,concat_ws('&#&','进口网络游戏许可', 'sy_cd_me_buss_per_jkwlyxxk', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_per_jkwlyxxk where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_per_jkwlyxxk ) and approvaltime is not null and compcode is not NULL AND datastatus != 3 AND isvalid = 1 ), des_dat as ( select eventsubject, subjectcode, eventdate, 'SJ000106012' as eventtype, url, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司获得进口网络游戏许可' , if( (gamename is null) or (gamename = ''), '', concat('，获评名称为', gamename) ) , if( (declarecategory is null) or (declarecategory = ''), '', concat( '，申报类别为', declarecategory ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, null as eigenvalue,retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106013',
     " with source_dat AS ( select date_format(passtime, 'yyyy-MM-dd') as eventdate, url, compname AS eventsubject, compcode AS subjectcode ,concat_ws('&#&','国际船舶代理企业备案', 'sy_cd_me_buss_per_gjcbdlqyba', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_per_gjcbdlqyba where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_per_gjcbdlqyba ) and passtime is not null and compcode is not NULL AND datastatus != 3 AND isvalid = 1 ), des_dat as ( select eventsubject, subjectcode, eventdate, 'SJ000106013' as eventtype, url, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司获得国际船舶代理企业备案资质' , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, null as eigenvalue,retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106014',
     " with source_dat AS ( select date_format(pubtime, 'yyyy-MM-dd') as eventdate, puburl as url, compname AS eventsubject, compcode AS subjectcode, case when grade = '1' then '省级' when grade = '2' then '市级' else null end as grade, case when certtype = '1' then '撤销' when certtype = '2' then '拟撤销' when certtype = '3' then '拟认定' when certtype = '4' then '拟认定后补' when certtype = '5' then '评估合格' when certtype = '6' then '认定' else null end as tp ,concat_ws('&#&','扶贫龙头企业', 'sy_cd_ms_base_poverty_leader_comp_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_poverty_leader_comp_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_poverty_leader_comp_list ) and pubtime is not null and compcode is not NULL AND datastatus != 3 and certtype in ('3', '4', '5', '6') ), des_dat as ( select eventsubject, subjectcode, eventdate, 'SJ000106014' as eventtype, concat('100074', '&#&', grade, '&#&', '7') as eigenvalue, url, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司获评扶贫龙头企业', if( (tp is null) or (tp = ''), '', concat('，认定类型为', tp) ), '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, '{fileDate}' as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), if(eventtype is NULL, '#', eventtype), if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106019',
     " with source_dat AS ( select date_format(pubtime, 'yyyy-MM-dd') as eventdate, sourceurl as url, compname AS eventsubject, compcode AS subjectcode, case when grade = '1' then '省级' when grade = '2' then '国家级' else null end as grade ,concat_ws('&#&','农业产业化龙头企业', 'sy_cd_ms_base_farm_leader_comp_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_farm_leader_comp_list where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_farm_leader_comp_list ) and pubtime is not null and compcode is not NULL AND datastatus != 3 and rawType in ('9', '3', '1', '6', '2', '12') ), des_dat as ( select eventsubject, subjectcode, eventdate, 'SJ000106019' as eventtype, concat('100075', '&#&', grade, '&#&', '7') as eigenvalue, url, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司获评农业产业化龙头企业', '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, '{fileDate}' as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), if(eventtype is NULL, '#', eventtype), if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106028',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, proname, date_format(issuedate, 'yyyy-MM-dd') as eventdate, url, CAST(null AS STRING) as expiredate ,concat_ws('&#&','绿色之星认证', 'sy_cd_me_buss_per_lszxrz', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_per_lszxrz where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_per_lszxrz ) and datastatus != 3 and isvalid = 1 and issuedate is not null and issuedate != '' and compcode is not null ), next_df AS ( select *, if( (proname is null) or (proname = ''), '', concat('，产品名称为', proname) ) as a from base_df ), final_df AS ( select *, concat( eventdate, '，公司获得绿色之星认证', a, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000106028', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000106028' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, null as eigenvalue,retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106035',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, agprodquotanm, prodname, pocapind, indval, date_format(publishdate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, url ,concat_ws('&#&','粮食进口关税配额企业', 'sy_cd_ms_base_fi_trq_comp_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_fi_trq_comp_list where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_ms_base_fi_trq_comp_list ) and datastatus != 3 and publishdate is not null and publishdate != '' and compcode is not null ), next_df AS ( select *, if( (agprodquotanm is null) or (agprodquotanm = ''), '', concat( '，申请农产品配额名称为', agprodquotanm ) ) as a, if( (prodname is null) or (prodname = ''), '', concat('，产品名称为', prodname) ) as b, if( (pocapind is null) or (pocapind = ''), '', concat( '，生产经营能力指标为', pocapind ) ) as c, if( (indval is null) or (indval = ''), '', concat('，指标值为', indval) ) as d from base_df ), final_df AS ( select *, concat( eventdate, '，公司获评粮食进口关税配额企业', a, b, c, d, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select distinct fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000106035', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000106035' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4,null as eigenvalue,retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106036',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, capindex, indicatorvalue, date_format(publishdate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, url ,concat_ws('&#&','棉花进口关税配额企业', 'sy_cd_ms_base_cot_tar_comp_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_cot_tar_comp_list where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_ms_base_cot_tar_comp_list ) and datastatus != 3 and publishdate is not null and publishdate != '' and compcode is not null ), next_df AS ( select *, if( (capindex is null) or (capindex = ''), '', concat( '，生产经营能力指标为', capindex ) ) as a, if( (indicatorvalue is null) or (indicatorvalue = ''), '', concat( '，指标值为', indicatorvalue ) ) as b from base_df ), final_df AS ( select *, concat( eventdate, '，公司获评棉花进口关税配额企业', a, b, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select distinct fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000106036', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000106036' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, null as eigenvalue,retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');






    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106065',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, level, date_format(pubtime, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, url, concat( concat('100076', '&#&', level, '&#&', '3') ) as eigenvalue ,concat_ws('&#&','展览工程企业资质-三级', 'sy_cd_me_buss_per_zlgcqyzz_third', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_per_zlgcqyzz_third where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_per_zlgcqyzz_third ) and datastatus != 3 and isvalid = 1 and pubtime is not null and pubtime != '' and compcode is not null ), next_df AS ( select *, if( (level is null) or (level = ''), '', concat('，水平等级为', level) ) as a0 from base_df ), final_df AS ( select *, concat( eventdate, '，公司获得展览工程企业资质-三级', a0, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000106065', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000106065' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106066',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, level, date_format(pubtime, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, url, concat( concat('100077', '&#&', level, '&#&', '3') ) as eigenvalue ,concat_ws('&#&','展览工程企业资质-二级', 'sy_cd_me_buss_per_zlgcqyzz_second', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_per_zlgcqyzz_second where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_per_zlgcqyzz_second ) and datastatus != 3 and isvalid = 1 and pubtime is not null and pubtime != '' and compcode is not null ), next_df AS ( select *, if( (level is null) or (level = ''), '', concat('，水平等级为', level) ) as a0 from base_df ), final_df AS ( select *, concat( eventdate, '，公司获得展览工程企业资质-二级', a0, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000106066', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000106066' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106067',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, level, date_format(pubtime, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, url, concat( concat('100078', '&#&', level, '&#&', '3') ) as eigenvalue ,concat_ws('&#&','展览工程企业资质-一级', 'sy_cd_me_buss_per_zlgcqyzz_first', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_per_zlgcqyzz_first where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_per_zlgcqyzz_first ) and datastatus != 3 and isvalid = 1 and pubtime is not null and pubtime != '' and compcode is not null ), next_df AS ( select *, if( (level is null) or (level = ''), '', concat('，水平等级为', level) ) as a0 from base_df ), final_df AS ( select *, concat( eventdate, '，公司获得展览工程企业资质-一级', a0, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000106067', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000106067' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106088',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, protype, to_date( cast(publishdate as timestamp) ) as eventdate, CAST(null AS STRING) as expiredate, url ,concat_ws('&#&','援外成套和物资项目实施企业', 'sy_cd_ms_base_foreign_material_comp_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_foreign_material_comp_list where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_ms_base_foreign_material_comp_list where modifytime is not null ) and datastatus != 3 and publishdate is not null and publishdate != '' and compcode is not null ), next_df AS ( select *, if( (protype is null) or (protype = ''), '', concat('，项目类别为', protype) ) as a0 from base_df ), final_df AS ( select *, concat( eventdate, '，公司获评援外成套和物资项目实施企业', a0, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000106088', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000106088' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, null as eigenvalue,retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106089',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, date_format(publishdate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, url ,concat_ws('&#&','对外劳务合作企业', 'sy_cd_ms_base_foreign_labor_comp_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_foreign_labor_comp_list where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_ms_base_foreign_labor_comp_list where modifytime is not null ) and datastatus != 3 and publishdate is not null and publishdate != '' and compcode is not null ), final_df AS ( select *, concat( eventdate, '，公司获评对外劳务合作企业', '。' ) as desc1 from base_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000106089', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000106089' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, null as eigenvalue,retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106090',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, projectname, projecttype, explorcompany, explortype, date_format(noticedate, 'yyyy-MM-dd') as eventdate, date_format(invaliddate, 'yyyy-MM-dd') as expiredate, url, concat( concat( '100079', '&#&', if(explortype is null, '', explortype), '&#&', '3' ), '@@', concat( '100080', '&#&', if(area is null, '', area), '&#&', '3' ) ) as eigenvalue ,concat_ws('&#&','探矿权登记', 'sy_cd_me_buss_per_tkqdj', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_per_tkqdj where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_per_tkqdj ) and datastatus != 3 and isvalid = 1 and noticedate is not null and noticedate != '' and compcode is not null and projecttype not like '%注销%' ), next_df AS ( select *, if( (projectname is null) or (projectname = ''), '', concat( '，项目名称为', projectname ) ) as a0, if( (projecttype is null) or (projecttype = ''), '', concat( '，项目类型为', projecttype ) ) as a1, if( (explorcompany is null) or (explorcompany = ''), '', concat( '，勘查单位为', explorcompany ) ) as a2, if( (explortype is null) or (explortype = ''), '', concat( '，勘查矿种为', explortype ) ) as a3 from base_df ), final_df AS ( select *, concat( eventdate, '，公司获得探矿权登记', a0, a1, a2, a3, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000106090', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000106090' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000106091',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, minename, projecttype, miningtype, miningway, date_format(noticedate, 'yyyy-MM-dd') as eventdate, date_format(invaliddate, 'yyyy-MM-dd') as expiredate, url ,concat_ws('&#&','采矿权登记', 'sy_cd_me_buss_per_ckqdj', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_per_ckqdj where filedate in ( select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_per_ckqdj ) and datastatus != 3 and isvalid = 1 and noticedate is not null and noticedate != '' and compcode is not null and projectType not like '%注销%' ), next_df AS ( select *, if( (minename is null) or (minename = ''), '', concat('，矿山名称为', minename) ) as a0, if( (projecttype is null) or (projecttype = ''), '', concat( '，项目类型为', projecttype ) ) as a1, if( (miningtype is null) or (miningtype = ''), '', concat( '，开采主矿种为', miningtype ) ) as a2, if( (miningway is null) or (miningway = ''), '', concat('，开采方式为', miningway) ) as a3 from base_df ), final_df AS ( select *, concat( eventdate, '，公司获得采矿权登记', a0, a1, a2, a3, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000106091', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000106091' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, null as eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102027',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, announcementDate as eventdate, case when rawType = 1 then '拟认定' when rawType = 2 then '认定' end as rawType, batch, case when complevel = '1' then '国家级' when complevel = '2' then '省级' when `complevel` = '3' then '市级' else null end as lev ,concat_ws('&#&','创新型中小企业', 'sy_cd_ms_cn_comp_inno_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_cn_comp_inno_list where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_cn_comp_inno_list ) and announcementDate is not NULL ), des_dat as ( select eventsubject, concat( concat('100081', '&#&', lev, '&#&', '7') ) as eigenvalue, subjectcode, eventdate, CAST(null AS STRING) as url, 'SJ000102027' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司被认定为创新型中小企业' , if( (rawType is null) or (rawType = ''), '', concat('，认定类型为', rawType) ) , if( (batch is null) or (batch = ''), '', concat('，批次为', batch) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102030',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, publishdate as eventdate, case when rawType = 5 then '认定' when rawType = 13 then '拟认定' when rawType = 34 then '培育' when rawType = 45 then '拟培育' when rawType = 53 then '复审通过' when rawType = 104 then '拟复审通过' end as rawType, case when complevel = 2 then '省级' when complevel = 3 then '市级' end as complevel ,concat_ws('&#&','技术先进型服务企业', 'sy_cd_ms_base_advanc_tech_serv_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_advanc_tech_serv_list where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_advanc_tech_serv_list ) and publishdate is not NULL ), des_dat as ( select eventsubject, concat( concat( '100082', '&#&', complevel, '&#&', '7' ) ) as eigenvalue, subjectcode, eventdate, CAST(null AS STRING) as url, 'SJ000102030' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司被认定为技术先进型服务企业' , if( (compLevel is null) or (compLevel = ''), '', concat('，级别为', compLevel) ) , if( (rawType is null) or (rawType = ''), '', concat('，类型为', rawType) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103022',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, publishDate as eventdate, setype, case when level = '1' then '国家级' when level = '2' then '省级' when `level` = '3' then '市级' else null end as lev ,concat_ws('&#&','小巨人企业', 'sy_cd_ms_base_little_giant_comp_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_little_giant_comp_list where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_little_giant_comp_list ) and publishDate is not NULL ), des_dat as ( select eventsubject, concat( concat( '100083', '&#&', if(lev is null, '', lev), '&#&', '7' ), '@@', concat( '100084', '&#&', if(setype is null, '', setype), '&#&', '3' ) ) as eigenvalue, subjectcode, eventdate, CAST(null AS STRING) as url, 'SJ000103022' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司获评“小巨人企业”称号' , if( (setype is null) or (setype = ''), '', concat('，类型为', setype) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102031',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, publishDate as eventdate, regexp_replace( projName, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as projName, case when compLevel = 1 then '国家级' when compLevel = 2 then '省级' when compLevel = 3 then '市级' when compLevel = 6 then '自治区级' end as compLevel, case when rawType = 5 then '认定' when rawType = 13 then '拟认定' when rawType = 14 then '拟立项' when rawType = 15 then '立项' when rawType = 30 then '组建备案' when rawType = 31 then '绩效考评' when rawType = 32 then '拟组建备案' when rawType = 33 then '评估验收' when rawType = 34 then '培育' when rawType = 35 then '其他' end as rawType ,concat_ws('&#&','工程技术研究中心（科技部评定）', 'sy_cd_ms_base_tech_search_cent_list', 'fingerId', string(fingerId)) as retrovalue from seeyii_data_house.dwd_ms_base_tech_search_cent_list where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_tech_search_cent_list ) and publishDate is not NULL ), des_dat as ( select eventsubject, subjectcode, eventdate, CAST(null AS STRING) as url, 'SJ000102031' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司被认定为工程技术研究中心（科技部评定）' , if( (projName is null) or (projName = ''), '', concat('，项目名称为', projName) ) , if( (compLevel is null) or (compLevel = ''), '', concat('，级别为', compLevel) ) , if( (rawType is null) or (rawType = ''), '', concat('，类型为', rawType) ) , '。' ) as desc1, concat( concat( '100085', '&#&', if(compLevel is null, '', compLevel), '&#&', '7' ), '@@', concat( '100086', '&#&', if(rawType is null, '', rawType), '&#&', '7' ) ) as eigenvalue, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat where rawType in ( '绩效考评', '立项', '拟立项', '拟认定', '拟组建备案', '培育', '评估验收', '其他', '认定', '组建备案' ) ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, eigenvalue, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102033',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, publishDate as eventdate, projName, case when rawType = 3 then '批建' when rawType = 5 then '认定' when rawType = 13 then '拟认定' when rawType = 37 then '拟组建' when rawType = 96 then '验收通过' when rawType = 97 then '复检合格' end as rawType, case when complevel = '1' then '国家级' when complevel = '2' then '省级' when `complevel` = '3' then '市级' else null end as lev ,concat_ws('&#&','技术创新中心', 'sy_cd_ms_base_tech_inn_cen_list', 'fingerId', string(fingerId)) as retrovalue from seeyii_data_house.dwd_ms_base_tech_inn_cen_list where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_tech_inn_cen_list ) and publishDate is not NULL ), des_dat as ( select eventsubject, concat( concat( '100087', '&#&', if(lev is null, '', lev), '&#&', '7' ), '@@', concat( '100088', '&#&', if(rawType is null, '', rawType), '&#&', '3' ) ) as eigenvalue, subjectcode, eventdate, CAST(null AS STRING) as url, 'SJ000102033' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司被认定为创新型中小企业' , if( (projName is null) or (projName = ''), '', concat('，项目名称为', projName) ) , if( (rawType is null) or (rawType = ''), '', concat('，类型为', rawType) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102034',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, publishDate as eventdate, regexp_replace( projName, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '' ) as projName, case when compLevel = 2 then '省级' when compLevel = 3 then '市级' end as compLevel, case when rawType = 1 then '备案' when rawType = 2 then '备案（存量）' when rawType = 3 then '批建' when rawType = 4 then '评估通过' when rawType = 5 then '认定' when rawType = 6 then '拟批建' when rawType = 7 then '设立' when rawType = 8 then '拟设立' when rawType = 9 then '拟备案' when rawType = 10 then '新建' when rawType = 11 then '验收结果' when rawType = 12 then '已建成' when rawType = 13 then '拟认定' when rawType = 14 then '拟立项' when rawType = 15 then '立项' when rawType = 16 then '拟推荐' when rawType = 17 then '撤销' end as rawType ,concat_ws('&#&','院士工作站（科技部评定）', 'sy_cd_ms_base_acade_station_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_acade_station_list where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_acade_station_list ) and publishDate is not NULL ), des_dat as ( select eventsubject, subjectcode, eventdate, CAST(null AS STRING) as url, 'SJ000102034' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司被认定为院士工作站（科技部评定）' , if( (projName is null) or (projName = ''), '', concat('，项目名称为', projName) ) , if( (compLevel is null) or (compLevel = ''), '', concat('，级别为', compLevel) ) , if( (rawType is null) or (rawType = ''), '', concat('，类型为', rawType) ) , '。' ) as desc1, concat( concat( '100089', '&#&', if(compLevel is null, '', compLevel), '&#&', '7' ), '@@', concat( '100090', '&#&', if(rawType is null, '', rawType), '&#&', '7' ) ) as eigenvalue, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat where rawType in ( '保留', '备案', '绩效考评A级', '绩效考评B级', '绩效考评合格', '绩效考评良好', '绩效考评优秀', '立项', '拟备案', '拟立项', '拟批建', '拟认定', '批建', '认定' ) ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, eigenvalue, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102035',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, publishDate as eventdate, projName, case when compLevel = 2 then '省级' end as compLevel, case when rawType = 5 then '认定' when rawType = 13 then '拟认定' when rawType = 34 then '培育' end as rawType ,concat_ws('&#&','制造业创新中心', 'sy_cd_ms_base_zzy_inn_cen_list', 'fingerId', string(fingerId)) as retrovalue from seeyii_data_house.dwd_ms_base_zzy_inn_cen_list where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_zzy_inn_cen_list ) and publishDate is not NULL ), des_dat as ( select eventsubject, subjectcode, eventdate, concat( concat( '100091', '&#&', if(compLevel is null, '', compLevel), '&#&', '7' ) , '@@' , concat( '100092', '&#&', if(rawType is null, '', rawType), '&#&', '7' ) ) as eigenvalue, CAST(null AS STRING) as url, 'SJ000102035' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司被认定为制造业创新中心' , if( (projName is null) or (projName = ''), '', concat('，项目名称为', projName) ) , if( (compLevel is null) or (compLevel = ''), '', concat('，级别为', compLevel) ) , if( (rawType is null) or (rawType = ''), '', concat('，类型为', rawType) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000102039',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, publishDate as eventdate, specificName, case when compLevel = 1 then '国家级' when compLevel = 2 then '省级' end as compLevel, case when rawType = 1 then '拟入围' when rawType = 2 then '认定' end as rawType ,concat_ws('&#&','智慧健康养老示范项目', 'sy_cd_ms_base_wit_aged_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_wit_aged_list where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_wit_aged_list ) and publishDate is not NULL ), des_dat as ( select eventsubject, concat( concat( '100093', '&#&', complevel, '&#&', '7' ) ) as eigenvalue, subjectcode, eventdate, CAST(null AS STRING) as url, 'SJ000102039' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目被认定为智慧健康养老示范项目' , if( (specificName is null) or (specificName = ''), '', concat('，名目为', specificName) ) , if( (compLevel is null) or (compLevel = ''), '', concat('，级别为', compLevel) ) , if( (rawType is null) or (rawType = ''), '', concat('，类型为', rawType) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103023',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, pubTime as eventdate, case when seType = 1 then '代表上市后备企业' when seType = 2 then '代表上市培育企业' end as seType, batchNum ,concat_ws('&#&','上市后备和培育企业表', 'sy_cd_ms_cn_comp_backupcult_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_cn_comp_backupcult_list where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_cn_comp_backupcult_list ) and pubTime is not NULL ), des_dat as ( select eventsubject, subjectcode, eventdate, CAST(null AS STRING) as url, 'SJ000103023' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司获评上市后备和培育企业' , if( (seType is null) or (seType = ''), '', concat('，类型为', seType) ) , if( (batchNum is null) or (batchNum = ''), '', concat('，批次为', batchNum) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, null as eigenvalue,retrovalue , datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103024',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, publishDate as eventdate, projectName, specificname, case when level = 1 then '省级' when level = 2 then '市级' end as level ,concat_ws('&#&','隐形冠军', 'sy_cd_ms_base_hid_cham_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_hid_cham_list where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_hid_cham_list ) and publishDate is not NULL ), des_dat as ( select eventsubject, subjectcode, eventdate, concat( concat( '100094', '&#&', if(level is null, '', level), '&#&', '7' ) , '@@' , concat( '100095', '&#&', if( specificname is null, '', specificname ), '&#&', '3' ) ) as eigenvalue, CAST(null AS STRING) as url, 'SJ000103024' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司获评隐形冠军' , if( (projectName is null) or (projectName = ''), '', concat( '，项目名称为', projectName ) ) , if( (level is null) or (level = ''), '', concat('，级别为', level) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103032',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, publishDate as eventdate, proName, sumPrice, concat( concat( '100096', '&#&', sumPrice, '&#&', '2' ) ) as eigenvalue ,concat_ws('&#&','服务贸易发展专项资金支持项目', 'sy_cd_me_buss_ser_tra_spec_fund', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_ser_tra_spec_fund where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_ser_tra_spec_fund ) and publishDate is not NULL ), des_dat as ( select eventsubject, subjectcode, eventdate, eigenvalue, CAST(null AS STRING) as url, 'SJ000103032' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目获得服务贸易发展专项资金支持' , if( (proName is null) or (proName = ''), '', concat('，项目名称为', proName) ) , if( (sumPrice is null) or (sumPrice = ''), '', concat( '，补助金额为', sumPrice, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103033',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, publishDate as eventdate, projectName, sumPrice, case when complevel = '1' then '国家级' when complevel = '2' then '省级' when `complevel` = '3' then '市级' else null end as lev ,concat_ws('&#&','节能减排补助资金涉企项目', 'sy_cd_ms_base_ene_subsidy_fund_list', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_base_ene_subsidy_fund_list where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_base_ene_subsidy_fund_list ) and publishDate is not NULL ), des_dat as ( select eventsubject, concat( concat( '100097', '&#&', if(lev is null, '', lev), '&#&', '7' ), '@@', concat( '100098', '&#&', if( bigint(sumprice) < 0, '0', sumprice ), '&#&', '3' ) ) as eigenvalue, subjectcode, eventdate, CAST(null AS STRING) as url, 'SJ000103033' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目获得节能减排补助' , if( (projectName is null) or (projectName = ''), '', concat( '，项目名称为', projectName ) ) , if( (sumPrice is null) or (sumPrice = ''), '', concat( '，补助金额为', sumPrice, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103034',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, date_format(publishdate, 'yyyy-MM-dd') as eventdate, projectName, projectType, sumPrice ,concat_ws('&#&','中央外经贸发展专项资金拟支持项目', 'sy_cd_me_buss_foreign_projects', 'fingerid', string(fingerid)) as retrovalue from seeyii_data_house.dwd_me_buss_foreign_projects where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_foreign_projects ) and publishDate is not NULL ), des_dat as ( select eventsubject, subjectcode, eventdate, concat( concat( '100100', '&#&', sumPrice, '&#&', '3' ) ) as eigenvalue, CAST(null AS STRING) as url, 'SJ000103034' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目获得中央农业生产发展资金' , if( (projectName is null) or (projectName = ''), '', concat( '，项目名称为', projectName ) ) , if( (projectType is null) or (projectType = ''), '', concat( '，项目类型为', projectType ) ) , if( (sumPrice is null) or (sumPrice = ''), '', concat( '，补助金额为', sumPrice, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103035',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, publishDate as eventdate, projectName, compLevel ,concat_ws('&#&','服务业发展专项资金项目', 'sy_cd_me_buss_service_ind_prod', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_service_ind_prod where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_service_ind_prod ) and publishDate is not NULL ), des_dat as ( select eventsubject, subjectcode, eventdate, CAST(null AS STRING) as url, 'SJ000103035' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目获得服务业发展专项资金' , if( (projectName is null) or (projectName = ''), '', concat( '，项目名称为', projectName ) ) , if( (compLevel is null) or (compLevel = ''), '', concat('，级别为', compLevel) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, null as eigenvalue,retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103036',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, date_format(publishDate, 'yyyy-MM-dd') as eventdate, projectName, projType, level, sumPrice ,concat_ws('&#&','文化产业发展专项资金项目', 'sy_cd_me_buss_culture_ind_prod', 'fingerId', string(fingerId)) as retrovalue from seeyii_data_house.dwd_me_buss_culture_ind_prod where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_culture_ind_prod ) and publishDate is not NULL ), des_dat as ( select eventsubject, concat( concat( '100101', '&#&', if(level is null, '', level), '&#&', '3' ), '@@', concat( '100102', '&#&', if(sumprice is null, '', sumprice), '&#&', '3' ) ) as eigenvalue, subjectcode, eventdate, CAST(null AS STRING) as url, 'SJ000103036' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目获得文化产业发展专项资金' , if( (projectName is null) or (projectName = ''), '', concat( '，项目名称为', projectName ) ) , if( (projType is null) or (projType = ''), '', concat('，项目类别为', projType) ) , if( (level is null) or (level = ''), '', concat('，级别为', level) ) , if( (sumPrice is null) or (sumPrice = ''), '', concat( '，补助金额为', sumPrice, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103037',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, publishDate as eventdate, projectName, case when rawLevel = 1 then '国家级' when rawLevel = 2 then '省级' when rawLevel = 3 then '市级' when rawLevel = 4 then '区县级' end as rawLevel ,concat_ws('&#&','工业互联网专项资金支持项目', 'sy_cd_me_buss_c2p_project', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_c2p_project where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_c2p_project ) and publishDate is not NULL ), des_dat as ( select eventsubject, subjectcode, eventdate, concat( concat( '100103', '&#&', rawLevel, '&#&', '7' ) ) as eigenvalue, CAST(null AS STRING) as url, 'SJ000103037' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目获得工业互联网专项资金支持' , if( (projectName is null) or (projectName = ''), '', concat( '，项目名称为', projectName ) ) , if( (rawLevel is null) or (rawLevel = ''), '', concat('，级别为', rawLevel) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103038',
     " with source_dat AS ( select compname as eventsubject, compcode as subjectcode, publishDate as eventdate, projectName, case when rawLevel = 1 then '国家级' when rawLevel = 2 then '省级' end as rawLevel, bonusAmount ,concat_ws('&#&','智能制造专项资金支持项目', 'sy_cd_me_buss_im_project', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_im_project where datastatus != 3 and filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_im_project ) and publishDate is not NULL ), des_dat as ( select eventsubject, concat( concat( '100104', '&#&', if(rawlevel is null, '', rawlevel), '&#&', '7' ), '@@', concat( '100105', '&#&', if( bonusamount is null, '', bonusamount ), '&#&', '2' ) ) as eigenvalue, subjectcode, eventdate, CAST(null AS STRING) as url, 'SJ000103038' as eventtype, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目获得智能制造专项资金支持' , if( (projectName is null) or (projectName = ''), '', concat( '，项目名称为', projectName ) ) , if( (rawLevel is null) or (rawLevel = ''), '', concat('，级别为', rawLevel) ) , if( (bonusAmount is null) or (bonusAmount = ''), '', concat( '，奖补金额为', bonusAmount, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103039',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, url, compname AS eventsubject, compcode AS subjectcode, projectname, bonusamount, case when rawlevel = '1' then '国家级' when rawlevel = '2' then '省级' else null end as lev ,concat_ws('&#&','研发投入补贴补助', 'sy_cd_me_buss_rd_subsidy', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_me_buss_rd_subsidy where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_rd_subsidy ) and publishdate is not NULL and compcode is not NULL AND datastatus != 3 ), des_dat as ( select eventsubject, subjectcode, eventdate, concat( concat( '100106', '&#&', if(lev is null, '', lev), '&#&', '7' ) , '@@' , concat( '100107', '&#&', if( bonusamount is null, '', bonusamount ), '&#&', '3' ) ) as eigenvalue, 'SJ000103039' as eventtype, url, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目获得研发投入补贴补助' , if( (projectname is null) or (projectname = ''), '', concat( '，项目名称为', projectname ) ) , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ) , if( (bonusamount is null) or (bonusamount = ''), '', concat( '，奖补金额为', bonusamount, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000103040',
     " with source_dat AS ( select date_format(publishdate, 'yyyy-MM-dd') as eventdate, url, compname AS eventsubject, compcode AS subjectcode, projectname, bonusamount, case when rawlevel = '1' then '国家级' when rawlevel = '2' then '省级' else null end as lev ,concat_ws('&#&','科技创新专项资金支持项目', 'sy_cd_me_buss_tech_inn_project', 'fingerId', string(fingerId)) as retrovalue from seeyii_data_house.dwd_me_buss_tech_inn_project where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_tech_inn_project ) and publishdate is not NULL and compcode is not NULL AND datastatus != 3 ), des_dat as ( select eventsubject, concat( concat( '100108', '&#&', if(lev is null, '', lev), '&#&', '7' ), '@@', concat( '100109', '&#&', if( bonusamount is null, '', bonusamount ), '&#&', '2' ) ) as eigenvalue, subjectcode, eventdate, 'SJ000103040' as eventtype, url, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司项目获得科技创新专项资金支持' , if( (projectname is null) or (projectname = ''), '', concat( '，项目名称为', projectname ) ) , if( (lev is null) or (lev = ''), '', concat('，级别为', lev) ) , if( (bonusamount is null) or (bonusamount = ''), '', concat( '，奖补金额为', bonusamount, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, eigenvalue, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000301010',
     " with source_dat AS ( select date_format(verbstarttime, 'yyyy-MM-dd') as eventdate, url, date_format(verbdeadline, 'yyyy-MM-dd') as expiredate, compname AS eventsubject, compcode AS subjectcode, mineraltype, productscale, projectname ,concat_ws('&#&','矿业权抵押备案', 'sy_cd_mm_cn_prop_kyqdyba', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_mm_cn_prop_kyqdyba where filedate in ( select max(filedate) from seeyii_data_house.dwd_mm_cn_prop_kyqdyba ) and verbstarttime is not NULL and compcode is not NULL AND datastatus != 3 AND isvalid = 1 ), des_dat as ( select eventsubject, subjectcode, eventdate, concat( concat( '100110', '&#&', REGEXP_REPLACE(productscale, '[^0-9.]', ''), '&#&', '3' ) ) as eigenvalue, 'SJ000301010' as eventtype, url, expiredate, concat( eventdate, '，公司将矿业权抵押' , if( (projectname is null) or (projectname = ''), '', concat( '，项目名称为', projectname ) ) , if( (mineraltype is null) or (mineraltype = ''), '', concat( '，开采矿种为', mineraltype ) ) , if( (productscale is null) or (productscale = ''), '', concat( '，生产规模为', productscale ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000301018',
     " with sk_stock as ( select compcode as subjectcode, compname as eventsubject, verbdeadline as expiredate, productscale ,concat_ws('&#&','矿业权抵押备案', 'sy_cd_mm_cn_prop_kyqdyba', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_mm_cn_prop_kyqdyba where filedate in ( select max(filedate) from seeyii_data_house.dwd_mm_cn_prop_kyqdyba ) AND datastatus != 3 and isvalid = 1 ), next_df as ( select *, '公司的矿业权抵押即将到期。' as desc1, CAST(null AS STRING) as url, date_sub(expiredate, 60) as eventdate, concat( '100111', '&#&', REGEXP_REPLACE(productscale, '[^0-9.]', ''), '&#&', '3' ) as eigenvalue from sk_stock where expiredate is not null ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000301018', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000301018' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from next_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000301011',
     " with ms_gov_tddy_v2 AS ( select id, usemortgagedland, propertymortgagedlandandtypesright as usufructtype, url, mortgageamount, date_format( startingdatemortgageregistration, 'yyyy-MM-dd' ) as eventdate, date_format( enddatelandmortgageregistration, 'yyyy-MM-dd' ) as expiredate ,concat_ws('&#&','土地抵押表_v2', 'sy_cd_ms_gov_tddy_v2', 'uid', string(uid)) as retrovalue from seeyii_data_house.dwd_ms_gov_tddy_v2 where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_gov_tddy_v2 ) AND datastatus != 3 AND isvalid = 1 and startingdatemortgageregistration is not null ), gov_tddy_v2_se AS ( select id, compname AS eventsubject, compcode AS subjectcode from seeyii_data_house.dwd_ms_gov_tddy_v2_se where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_gov_tddy_v2_se ) AND datastatus != 3 and compcode is not NULL ), source_dat AS ( select a.eventdate, a.url, b.eventsubject, b.subjectcode, a.usufructtype, a.usemortgagedland, a.expiredate, a.mortgageamount , a.retrovalue from ms_gov_tddy_v2 as a join gov_tddy_v2_se as b on a.id = b.id ), des_dat as ( select eventsubject, concat( concat( '100112', '&#&', mortgageamount, '&#&', '3' ) ) as eigenvalue, subjectcode, eventdate, 'SJ000301011' as eventtype, url, expiredate, concat( eventdate, '，公司进行土地抵押' , if( (usemortgagedland is null) or (usemortgagedland = ''), '', concat( '，抵押土地用途为', usemortgagedland ) ) , if( (usufructtype is null) or (usufructtype = ''), '', concat( '，抵押土地权属性质与使用权类型为', usufructtype ) ) , if( (usufructtype is null) or (usufructtype = ''), '', concat( '，抵押金额为', mortgageamount, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eigenvalue, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000301017',
     " with ms_gov_tddy_v2 AS ( select id, usemortgagedland, url, mortgageamount, concat( concat( '100113', '&#&', mortgageAmount, '&#&', '3' ) ) as eigenvalue, date_format( DATE_SUB( enddatelandmortgageregistration, 90 ), 'yyyy-MM-dd' ) as eventdate, date_format( enddatelandmortgageregistration, 'yyyy-MM-dd' ) as expiredate ,concat_ws('&#&','土地抵押表_v2', 'sy_cd_ms_gov_tddy_v2', 'uid', string(uid)) as retrovalue from seeyii_data_house.dwd_ms_gov_tddy_v2 where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_gov_tddy_v2 ) AND datastatus != 3 AND isvalid = 1 and enddatelandmortgageregistration is not null ), gov_tddy_v2_se AS ( select id, compname AS eventsubject, compcode AS subjectcode from seeyii_data_house.dwd_ms_gov_tddy_v2_se where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_gov_tddy_v2_se ) AND datastatus != 3 and compcode is not NULL ), source_dat AS ( select a.eventdate, a.eigenvalue, a.url, b.eventsubject, b.subjectcode, a.expiredate ,retrovalue from ms_gov_tddy_v2 as a join gov_tddy_v2_se as b on a.id = b.id ), des_dat as ( select eventsubject, subjectcode, eventdate, eigenvalue, 'SJ000301017' as eventtype, url, expiredate, concat( '公司的土地抵押即将到期' , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000301012',
     " with source_dat AS ( select date_format( contractsigningdate, 'yyyy-MM-dd' ) as eventdate, url, landuser AS eventsubject, landusercode AS subjectcode, modeofsupply, landuse, landsource, landuselife, transactionprice ,concat_ws('&#&','土地供地结果表', 'sy_cd_ms_gov_tdgd_v2', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_gov_tdgd_v2 where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_gov_tdgd_v2 ) and contractsigningdate is not NULL and landusercode is not NULL AND datastatus != 3 AND isvalid = 1 ), des_dat as ( select eventsubject, subjectcode, eventdate, concat( concat( '100114', '&#&', if(landuse is null, '', landuse), '&#&', '3' ) , '@@' , concat( '100115', '&#&', if( transactionprice is null, '', transactionprice ), '&#&', '3' ) ) as eigenvalue, 'SJ000301012' as eventtype, url, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司签订供地合同' , if( (modeofsupply is null) or (modeofsupply = ''), '', concat( '，供应方式为', modeofsupply ) ) , if( (landuse is null) or (landuse = ''), '', concat('，土地用途为', landuse) ) , if( (landsource is null) or (landsource = ''), '', concat( '，土地来源为', landsource ) ) , if( (landuselife is null) or (landuselife = ''), '', concat( '，土地使用年限为', landuselife, '年' ) ) , if( (transactionprice is null) or (transactionprice = ''), '', concat( '，成交价格为', transactionprice, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000301013',
     " with source_dat AS ( select date_format(transactiondate, 'yyyy-MM-dd') as eventdate, url, originallanduser AS eventsubject, origcode AS subjectcode, landuse, landuselife, transferprice ,concat_ws('&#&','土地转让表_v2', 'sy_cd_ms_gov_tdzr_v2', 'sourceId', string(id)) as retrovalue from seeyii_data_house.dwd_ms_gov_tdzr_v2 where filedate in ( select max(filedate) from seeyii_data_house.dwd_ms_gov_tdzr_v2 ) and transactiondate is not NULL and origcode is not NULL AND datastatus != 3 AND isvalid = 1 ), des_dat as ( select eventsubject, subjectcode, eventdate, concat( concat( '100116', '&#&', if(landuse is null, '', landuse), '&#&', '3' ) , '@@' , concat( '100117', '&#&', if( transferprice is null, '', transferprice ), '&#&', '3' ) ) as eigenvalue, 'SJ000301013' as eventtype, url, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司进行土地转让' , if( (landuse is null) or (landuse = ''), '', concat('，土地用途为', landuse) ) , if( (landuselife is null) or (landuselife = ''), '', concat( '，土地使用年限为', landuselife, '年' ) ) , if( (transferprice is null) or (transferprice = ''), '', concat( '，成交价格为', transferprice, '万元' ) ) , '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_dat ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , if(eventtype is NULL, '#', eventtype) , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000309005',
     " with base_df AS ( select curcode as subjectcode, currentowneroflanduseright as eventsubject, '公司近3个月新增购地。' as desc1, concat( concat( '100118', '&#&', if(landuse is null, '', landuse), '&#&', '3' ) , '@@' , concat( '100119', '&#&', if( transferprice is null, '', transferprice ), '&#&', '3' ) ) as eigenvalue, transactiondate as eventdate, date_format( date_add(transactiondate, 90), 'yyyy-MM-dd' ) as expiredate, url ,concat_ws('&#&','土地转让表_v2', 'sy_cd_ms_gov_tdzr_v2', 'sourceId', string(id)) as retrovalue from ( select currentowneroflanduseright, landuse, curcode, date_format(transactiondate, 'yyyy-MM-dd') as transactiondate, transferprice, datastatus, isvalid, url, id, row_number() over( partition by id order by filedate desc ) as rnumber from seeyii_data_house.dwd_ms_gov_tdzr_v2 ) as a where rnumber = 1 and datastatus != 3 and isvalid = 1 and curcode is not null and transactiondate != '' and transactiondate is not null ) insert into {ku}.{tb} partition (filedate = {fileDate}) select fingerId_row( concat_ws( ',' , if( eventsubject is NULL, '#', eventsubject ) , if( subjectcode is NULL, '#', subjectcode ) , 'SJ000309005' , if(desc1 is NULL, '#', desc1) , if(url is NULL, '#', url) , if(eventdate is NULL, '#', eventdate) , if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000309005' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from base_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '********' )
    SELECT  'SJ000201001',
     " with main_df as ( select * from ( select *, row_number() over( partition by id order by filedate desc ) as rnumber from seeyii_data_house.dwd_me_trad_ipo_base ) as tb where rnumber = 1 and datastatus != 3 ), sour_df as ( select * from ( select *, row_number() over( partition by id order by filedate desc ) as rnumber from seeyii_data_house.dwd_me_trad_ipo_detail ) as tb where rnumber = 1 and datastatus != 3 ), all_df as ( select * from ( select *, lag(stat, 1) over( partition by compcode, projid order by statdate desc, stat desc ) as up_stat, lag(statdate, 1) over( partition by compcode, projid order by statdate desc, stat desc ) as up_statdate from ( select a.stat, a.statdate, a.projid, b.compname, b.compcode, b.brokname, b.prename, b.finaAmount, b.market, b.currstat, b.statdate as curdate ,concat_ws('&#&','IPO发行上市审核概况表', 'sy_cd_me_trad_ipo_base', 'sourceId', string(b.id)) as retrovalue from sour_df a join main_df b on a.projid = b.projid ) t ) t2 where stat in ('101', '102', '201') ), next_df AS ( SELECT date_format(statdate, 'yyyy-MM-dd') as eventdate, compcode as subjectcode, compname as eventsubject, case stat when '101' then 'SJ000201001' when '102' then 'SJ000201002' when '201' then 'SJ000201003' end as eventtype, CAST(up_statdate AS STRING) as expiredate, brokname, prename, finaAmount, case market when '101' then '上交所主板' when '201' then '深交所主板' when '301' then '创业板' when '302' then '创业板' when '401' then '科创板' when '501' then '北交所' end as market ,retrovalue from all_df where statdate is not NULL and compcode is not null ), add_desc_df as ( select *, CAST(null AS STRING) as url, CAST(null AS STRING) as eigenvalue, concat( '公司于', eventdate, '启动上市辅导' ) as a, if( (prename is null) or (prename = ''), '', concat( '，辅导机构为：', prename ) ) as b, '。' c from next_df where eventtype = 'SJ000201001' union select *, CAST(null AS STRING) as url, CAST(null AS STRING) as eigenvalue, concat( '公司于', eventdate, '完成上市辅导验收' ) as a, '，即将提交上市申请' as b, '。' c from next_df where eventtype = 'SJ000201002' union select *, CAST(null AS STRING) as url, concat( concat( '100007', '&#&', if(finaAmount is null, '', finaAmount), '&#&', '3' ), '@@', concat( '100008', '&#&', if(market is null, '', market), '&#&', '7' ) ) as eigenvalue, concat( '公司提交的上市申请已于', eventdate, '被证监会受理，已进入排队状态' ) as a, if( (brokname is null) or (brokname = ''), '', concat( '，保荐机构为：', brokname ) ) as b, '。' c from next_df where eventtype = 'SJ000201003' ), final_df as ( select *, concat_ws('', a, b, c) as desc1 from add_desc_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), if(eventtype is NULL, '#', eventtype), if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"8", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    

    