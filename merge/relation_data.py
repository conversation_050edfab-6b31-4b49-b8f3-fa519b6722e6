# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/05/21
from core.excel_base import ExcelBase
from moduler.company_branch_org import CompanyBranch
from moduler.company_invester import CompanyInvest


class RelationBranch(ExcelBase):
    def __init__(self):
        super(RelationBranch, self).__init__()
        self.branch = CompanyBranch()
        self.inv = CompanyInvest()

    def process(self):
        result = list()
        tmp_dict = dict()
        raw_items = self.read_excel_data(self._in_file_path + "/股权关联关系_20210604.xlsx", "Sheet")
        for item in raw_items:
            name = item["集团名称"]
            rel = item["关系类型"]
            if rel not in {"实际控制下属企业"}:
                continue
            tmp_dict.setdefault(name, list())
            tmp_dict[name].append(item)
        # 分支机构
        branch_dict = self.branch.run(list(tmp_dict.keys()))
        for cmp in list(tmp_dict.keys()):
            raw_branch = tmp_dict[cmp]
            result.extend(raw_branch)
            all_item = {self._gen_common_graph_id(i) for i in raw_branch}

            branch_items = branch_dict.get(cmp, list())
            for br in branch_items:
                fetch = [
                    ("companyName", "集团名称"),
                    ("branchName", "成员企业名称")]
                br_item = self.fetch_dict(br, fetch)
                br_item["关系类型"] = "实际控制下属企业"
                br_item["链路"] = f"{br_item['集团名称']}-分支机构->{br_item['成员企业名称']}"
                fin_id = self._gen_common_graph_id(br_item)
                if fin_id not in all_item:
                    result.append(br_item)

        # 一级子公司
        inv_dict = self.inv.run(list(tmp_dict.keys()))
        for cmp in list(tmp_dict.keys()):
            raw_kz = tmp_dict[cmp]
            all_item = {self._gen_common_graph_id(i) for i in raw_kz}

            inv_items = inv_dict.get(cmp, list())
            for inv_item in inv_items:
                ratio = inv_item.get("holdRatio") or 0.0
                if ratio <= 0.5:
                    continue
                fetch = [
                    ("cname", "集团名称"),
                    ("inv_name", "成员企业名称")]
                new_inv_item = self.fetch_dict(inv_item, fetch)
                new_inv_item["关系类型"] = "实际控制下属企业"
                new_inv_item["链路"] = f"{new_inv_item['集团名称']}-控制->{new_inv_item['成员企业名称']}"
                fin_id = self._gen_common_graph_id(new_inv_item)
                if fin_id not in all_item:
                    result.append(new_inv_item)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            '集团名称': ('集团名称', 0),
            '关系类型': ('关系类型', 1),
            '成员企业名称': ('成员企业名称', 2),
            '链路': ('链路', 3)}
        self._excel_name = self.name_add_date("农行V3POC.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def read_excel_data(self, file_name, sheet="Sheet1"):
        data_list = self._extract_data(file_name, sheet)
        return data_list


if __name__ == '__main__':
    p = RelationBranch()
    p.process()
