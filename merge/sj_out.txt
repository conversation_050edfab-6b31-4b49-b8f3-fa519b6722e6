
    INSERT INTO seeyii_emr_tert.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240303' )
    SELECT  'SJ000302001',
     " with source_df as ( select tb2.eventsubject, tb2.subjectcode, tb1.eventdate, tb1.targetname, tb3.custna, tb3.custcode, concat( concat( '100100', '&#&', tb1.tradingvalue, '&#&', '2' ) ) as eigenvalue ,concat_ws('&#&','上市公司供应商与客户表', 'sy_cd_ms_rela_listed_supp_cust', 'sourceId', string(tb1.id)) as retrovalue from ( select id, date_format(pubdate, 'yyyy-MM-dd') as eventdate, tradingvalue, targetname from seeyii_emr_bus.dwd_ms_rela_listed_supp_cust where filedate in ( select max(filedate) from seeyii_emr_bus.dwd_ms_rela_listed_supp_cust ) AND datastatus != 3 AND pubdate is not NULL and relatype = 2 ) as tb1 join( select pkid, compname AS eventsubject, compcode AS subjectcode from seeyii_emr_bus.dwd_ms_rela_listed_supp_cust_se where filedate in ( select max(filedate) from seeyii_emr_bus.dwd_ms_rela_listed_supp_cust_se ) AND datastatus != 3 AND typecode = 2 AND compcode is not NULL AND compname is not NULL ) as tb2 ON tb1.id = tb2.pkid join( select pkid, compname AS custna, compcode AS custcode from seeyii_emr_bus.dwd_ms_rela_listed_supp_cust_se where filedate in ( select max(filedate) from seeyii_emr_bus.dwd_ms_rela_listed_supp_cust_se ) AND datastatus != 3 AND typecode = 1 AND compcode is not NULL AND compname is not NULL ) as tb3 ON tb1.id = tb3.pkid ), des_dat as ( select subjectcode, eventdate, eventsubject, eigenvalue, retrovalue, CAST(null AS STRING) as url, 'SJ000302001' as eventtype, CAST(null AS STRING) as expiredate, concat( '本单位于', eventdate, '成为上市公司', custna, '的供应商', if( (targetname is null) or (targetname = ''), '', concat( '，向上市公司提供', targetname, '标的' ) ), '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate from source_df ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, retrovalue, fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), if(eventtype is NULL, '#', eventtype), if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"9", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_emr_tert.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240303' )
    SELECT  'SJ000305001',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, aprvpubldt, regcapitalam, date_format(estiblishdate, 'yyyy-MM-dd') as eventdate, currencycode, regaddr, CAST(null AS STRING) as url, date_add( date_format(estiblishdate, 'yyyy-MM-dd'), 180 ) as expiredate, concat( concat( '100004', '&#&', regcapital, '&#&', '2' ) ) as eigenvalue ,concat_ws('&#&','全量正常经营状态企业表', 'sy_cd_ms_base_normal_comp_list', 'sourceId', string(tb1.id)) as retrovalue from ( select *, row_number() over ( partition by sourceid order by filedate desc ) rnumber from seeyii_emr_tert.dwd_ms_base_normal_comp_list ) as tb1 where rnumber = 1 and datastatus != 3 and estiblishdate is not null and estiblishdate != '' and date_format(estiblishdate, 'yyyy-MM-dd') >= '2023-01-01' ), next_df AS ( select *, if( (aprvpubldt is null) or (aprvpubldt = ''), '', concat('，于', aprvpubldt, '核准') ) as a, if( (regcapitalam is null) or (regcapitalam = ''), '', concat( '注册资本', regcapitalam, if( (currencycode is null) or (currencycode = ''), '', concat( '（币种：', currencycode, '）' ) ) ) ) as b, if( (regaddr is null) or (regaddr = ''), '', concat('注册地为', regaddr) ) as c from base_df ), final_df AS ( select *, concat( '公司于', eventdate, '成立', a, '，属新注册工商企业。', b, if( b = '', c, concat(',', c) ), '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000305001', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000305001' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"9", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_emr_tert.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240303' )
    SELECT  'SJ000204001',
     " with base_fin_his_df AS ( select compname as eventsubject, date_format(fintime, 'yyyy-MM-dd') as eventdate, compcode as subjectcode, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate, case WHEN finmoney regexp '万人民币' THEN bigint( substr( finmoney, 0, length(finmoney) - length('万人民币') ) )* 10000 WHEN finmoney regexp '亿人民币' THEN bigint( substr( finmoney, 0, length(finmoney) - length('亿人民币') ) )* 100000000 WHEN finmoney regexp '万美元' THEN bigint( substr( finmoney, 0, length(finmoney) - length('万美元') ) )* 70000 WHEN finmoney regexp '亿美元' THEN bigint( substr( finmoney, 0, length(finmoney) - length('亿美元') ) )* 700000000 WHEN finmoney regexp '万港元' THEN bigint( substr( finmoney, 0, length(finmoney) - length('万港元') ) )* 9000 WHEN finmoney regexp '亿港元' THEN bigint( substr( finmoney, 0, length(finmoney) - length('亿港元') ) )* 90000000 WHEN finmoney regexp '人民币' THEN bigint( substr( finmoney, 0, length(finmoney) - length('人民币') ) )* 1 WHEN finmoney regexp '美元' THEN bigint( substr( finmoney, 0, length(finmoney) - length('美元') ) )* 7 WHEN finmoney regexp '港元' THEN bigint( substr( finmoney, 0, length(finmoney) - length('港元') ) )* 0.9 end as finmoney, finround ,concat_ws('&#&','融资历史', 'sy_cd_me_trad_fin_his_new', 'sourceId', string(fingerid)) as retrovalue from seeyii_emr_bus.dwd_me_trad_fin_his_new where filedate in ( select max(filedate) as filedate from seeyii_emr_bus.dwd_me_trad_fin_his_new ) and datastatus != 3 and isvalid = 1 and compcode is not null and fintime is not null and fintime != '' and fintime > '1970-01-01' ), next_df AS ( select *, concat( concat( '100009', '&#&', string( if(finmoney is null, '', finmoney) ), '&#&', '3' ), '@@', concat( '100010', '&#&', if(finround is null, '', finround), '&#&', '3' ) ) as eigenvalue, concat( '公司于', eventdate, '最新宣布一轮新的私募融资。' ) as desc1 from base_fin_his_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000204001', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000204001' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from next_df ",
     map("tempview1",""),"9", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_emr_tert.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240303' )
    SELECT  'SJ000101001',
     " with source_df as ( SELECT tb2.subjectcode, tb1.eventdate, tb1.title, tb1.pt1 , concat_ws('&#&','专利', 'sy_cd_me_buss_ip_patn_new', 'id', string(tb1.id)) as retrovalue FROM ( select id, eventdate, title, case when pattype = '1' then '发明' when pattype = '2' then '实用新型' when pattype = '3' then '外观设计' when pattype = '4' then '短期专利' when pattype = '5' then '其它' when pattype = '6' then '译文' when pattype = '7' then '检索报告' when pattype = '8' then 'pct发明' when pattype = '9' then 'pct实用新型' else '' end as pt1 from ( select id, pattype, title, pubdate as eventdate, isvalid, datastatus, row_number() over ( partition by id order by filedate desc ) num from seeyii_emr_bus.dwd_me_buss_ip_patn ) t where t.num = 1 and t.eventdate >= '2021-01-01' AND t.datastatus != 3 AND t.isvalid = '1' ) AS tb1 JOIN ( select pkid, subjectcode from ( select pkid, compcode AS subjectcode, row_number() over ( partition by fingerid order by filedate desc ) num2 from seeyii_emr_bus.dwd_me_buss_ip_patn_se ) t where t.num2 = 1 and t.subjectcode is not null ) AS tb2 ON tb1.id = tb2.pkid ), des_dat as ( select subjectcode, eventdate, concat( concat('100021', '&#&', pt1, '&#&', '7') ) as eigenvalue, retrovalue, CAST(null AS STRING) as eventsubject, CAST(null AS STRING) as url, 'SJ000101001' as eventtype, CAST(null AS STRING) as expiredate, concat( '本单位于', eventdate, '新增专利', if( (pt1 is null) or (pt1 = ''), '', concat(',专利类型为', pt1) ), if( (title is null) or (title = ''), '', concat(',专利名称为', title) ), '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate from source_df ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, eigenvalue, retrovalue, fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), if(eventtype is NULL, '#', eventtype), if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"9", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_emr_tert.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240303' )
    SELECT  'SJ000101007',
     " with source_df AS ( SELECT DISTINCT tb2.eventsubject, tb2.subjectcode, tb1.eventdate, tb1.fullname ,concat_ws('&#&','软件著作权表', 'sy_cd_me_buss_ip_sf_cprt_new', 'id', string(tb1.id)) as retrovalue FROM ( select id, eventdate, fullname from ( select id, fullname, date_format(regtime, 'yyyy-MM-dd') as eventdate, isvalid, datastatus, row_number() over ( partition by id order by filedate desc ) num from seeyii_emr_tert.dwd_me_buss_ip_sf_cprt ) t where t.num = 1 AND t.datastatus != 3 AND t.isvalid = '1' AND t.eventdate is not null ) AS tb1 JOIN ( select pkid, eventsubject, subjectcode from ( select pkid, compname AS eventsubject, compcode AS subjectcode, row_number() over ( partition by fingerid order by filedate desc ) num2 from seeyii_emr_tert.dwd_me_buss_ip_sf_cprt_se ) t where t.num2 = 1 and t.subjectcode is not null ) AS tb2 ON tb1.id = tb2.pkid ), des_dat as ( select subjectcode, eventdate, eventsubject, CAST(null AS STRING) as url, 'SJ000101007' as eventtype, CAST(null AS STRING) as expiredate, concat( '本单位于', eventdate, '新增软件著作权', if( (fullname is null) or (fullname = ''), '', concat('，软件名称为', fullname) ), '。' ) as desc1, '1' as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime, {fileDate} as filedate ,retrovalue from source_df ), ret_dat as ( select eventsubject, subjectcode, eventdate, expiredate, url, eventtype, desc1, datastatus, filedate, modifytime, fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), if(eventtype is NULL, '#', eventtype), if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid ,retrovalue from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid, subjectcode, eventsubject, eventtype, desc1 as `desc`, url, eventdate, null as expiredate, null as property1, null as property2, null as property3, null as property4, null as eigenvalue,retrovalue, datastatus, modifytime, filedate from ret_dat ",
     map("tempview1",""),"9", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_emr_tert.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240303' )
    SELECT  'SJ100302009',
     "  with join_bidder_df AS ( select distinct b.compcode as subjectcode, b.suppnm as eventsubject, reporttitle, eventdate, a.compcode, url, concat(concat('100147', '&#&', if(suppprice like '%.%', if(suppprice REGEXP '^[0-9]+.[0-9]+$', suppprice, ''), if(suppprice REGEXP '^[0-9]+$', suppprice, '')), '&#&', '3')) as eigenvalue ,concat_ws('&#&','中标结果供应商表v2', 'sy_cd_me_buss_bidder_supplier_v2', 'fingerId', string(fingerid)) as retrovalue from tempview1 as a left join tempview3 as b on a.eventid = b.eventid where b.eventid is not null), base_df AS ( select distinct eventsubject, subjectcode, regexp_replace(reporttitle, '[\\000-\\017]|\\n|\\x1e|\\x0c|\\x0e|\\u3000|\\x11|\\x10|\\x0f|\\x1b|\"|\\r', '') as reporttitle, b.compname as bidunitnm, eventdate, url, CAST(null AS STRING) as expiredate, eigenvalue ,retrovalue from join_bidder_df as a left join tempview2 as b on a.compcode = b.compcode where b.compcode is not null ), next_df AS ( select *, if((reporttitle is null) or (reporttitle = ''), '', concat(',信息来源为', reporttitle)) as a0, if((bidunitnm is null) or (bidunitnm = ''), '', concat('，招标单位为', bidunitnm)) as a1 from base_df ), final_df AS ( select *, concat(eventdate, '，公司新增中标信息', a1, a0, '。') as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row(concat_ws(',', if(eventsubject is NULL, '#', eventsubject), if(subjectcode is NULL, '#', subjectcode ), 'SJ100302009', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if(expiredate is NULL, '#', expiredate) ) ) as eventid, subjectcode, eventsubject, 'SJ100302009' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format(current_timestamp(), 'yyyy-MM-dd HH:mm:ss') as modifytime from final_df  ",
     map("tempview2"," select distinct compname, compcode from ( select *,row_number() over (partition by fingerid order by filedate desc) as rnumber from seeyii_emr_tert.dwd_me_buss_bidder_comp_type ) as tb where rnumber = 1 and datastatus != 3 and compcode is not null ","tempview3"," select distinct eventid,suppnm,compcode,suppprice,fingerid from ( select eventid,suppnm,compcode,datastatus,suppprice,fingerid, row_number() over(partition by fingerid order by filedate desc) as rnumber from seeyii_emr_tert.dwd_me_buss_bidder_supplier_v2 ) as tb where rnumber = 1 and datastatus != 3 and compcode is not null","tempview1","select distinct compcode,reporttitle,date_format(startdate, 'yyyy-MM-dd') as eventdate,eventid,contenturl as url from ( select compcode,reporttitle,startdate,contenturl,datastatus,eventid, row_number() over(partition by sourceid order by filedate desc) as rnumber from seeyii_emr_tert.dwd_me_buss_bidder_info_v2 ) as tb where rnumber = 1 and datastatus != 3 and startdate is not null and compcode is not null ")
     ,"9", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_emr_tert.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240303' )
    SELECT  'SJ000305002',
     " with base_df AS ( select parentcode as subjectcode, parentname as eventsubject, compname, date_format(estiblishdate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, CAST(null AS STRING) as url, concat( '100148', '&#&', regcapital, '&#&', '2' ) as eigenvalue ,concat_ws('&#&','分支机构表', 'sy_cd_ms_base_branch_list', 'sourceId', string(id)) as retrovalue from ( select *, row_number() over( partition by sourceid order by filedate desc ) as rnumber from seeyii_emr_bus.dwd_ms_base_branch_list ) as tb where rnumber = 1 and datastatus != 3 and estiblishdate is not null and estiblishdate != '' and parentcode is not null ), next_df AS ( select *, if( (compname is null) or (compname = ''), '', concat( '，分支机构名称为', compname ) ) as a0 from base_df ), final_df AS ( select *, concat( eventdate, '，公司设立分支机构', a0, '。' ) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000305002', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000305002' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, retrovalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"9", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    