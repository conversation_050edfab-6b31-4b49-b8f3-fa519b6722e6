# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2024/1/30
from cfg.config import source_file_path
from core.excel_base import ExcelBase


class Static(ExcelBase):
    def __init__(self):
        super().__init__()
        self.file_map = {
            "600084": "dwd_ms_cn_comp_ve_five_set_600084_20240123.csv",
            # "104001": "dwd_ms_cn_comp_ve_main_inco_20240126.csv",
            # "206020": "dwd_ms_cn_comp_ve_mpro_rat_20240126.csv",
            # "104003": "dwd_ms_cn_comp_ve_npro_sk_20240126.csv",
            # "700008": "dwd_ms_cn_comp_ve_bcm_first_700008.csv",
            "600083": "dwd_ms_cn_comp_ve_five_set_600083_20240125.csv",
            "700047": "dwd_ms_cn_comp_ve_bcm_third_700047_20240130.csv",
            "700048": "dwd_ms_cn_comp_ve_bcm_third_700048_20240130.csv",
            "700049": "dwd_ms_cn_comp_ve_bcm_third_700049_20240130.csv",
            "700057": "dwd_ms_cn_comp_ve_bcm_fourth_700057_20240130.csv",
        }
        self.name_map = {
            "700047": "营业收入增长",
            "700048": "毛利率增长",
            "700049": "净利润增长",
            "600084": "资产负债率",
            # "104001": "营业收入",
            # "206020": "毛利率",
            # "104003": "净利润",
            # "700008": "经营性现金流规模",
            "600083": "净资产",
            "700057": "总资产增长",
        }

    def process(self):
        csv_result = dict()
        for code, file_name in self.file_map.items():
            csv_list = self.read_excel_data(file_name)
            csv_result.setdefault(code, dict())
            for i in csv_list:
                if str(i["indicationid"]) != str(code):
                    continue
                csv_result[code].setdefault(i["compcode"], i["indicationvalue"])

        result = list()
        a_items = self.query_a_name()
        three_items = self.query_three_name()
        sh_names = a_items | three_items
        print(len(sh_names))
        a_year_data = self.query_a_yysr_data()
        three_data = self.query_three_yysr_data()
        three_data_2 = self.query_three_yysr_data_2()
        zzc_a = self.query_a_zzc_data()
        zzc_three = self.query_three_zzc_data()
        for code, tmp in a_items.items():
            tmp.update(a_year_data.get(code, dict()))
            tmp.update(zzc_a.get(code, dict()))
            for zb_code, en in self.name_map.items():
                v = csv_result.get(zb_code).get(code)
                if v:
                    tmp[en] = self.sort_by_year(v) if zb_code in {"700047", "700048", "700049", "700057"} else v
            result.append(tmp)

        for code, tmp in three_items.items():
            tmp.update(three_data.get(code, dict()))
            tmp.update(three_data_2.get(code, dict()))
            tmp.update(zzc_three.get(code, dict()))
            for zb_code, en in self.name_map.items():
                v = csv_result.get(zb_code).get(code)
                if v:
                    tmp[en] = self.sort_by_year(v) if zb_code in {"700047", "700048", "700049", "700057"} else v
            result.append(tmp)

        self.save_data_excel(result)

    def read_excel_data(self, file_name):
        import pandas as pd
        df = pd.read_csv(source_file_path + "/" + file_name)
        data_list = df.to_dict(orient="records")
        data_list = self.filter_data(data_list)
        return data_list

    def save_data_excel(self, result):
        field_cfg = {
            'compCode': ('公司编号', 0),
            'compName': ('公司名称', 1),
            'ty': ('类型', 2),
            'listStatus': ('上市状态', 3),
            'listDate': ('上市时间', 4),
            '营业收入增长': ('营业收入增长', 5),
            '营业收入_2020': ('营业收入_2020', 6),
            '营业收入_2021': ('营业收入_2021', 7),
            '营业收入_2022': ('营业收入_2022', 8),
            '毛利率增长': ('毛利率增长', 9),
            '毛利率_2020': ('毛利率_2020', 10),
            '毛利率_2021': ('毛利率_2021', 11),
            '毛利率_2022': ('毛利率_2022', 12),
            '净利润增长': ('净利润增长', 13),
            '净利润_2020': ('净利润_2020', 14),
            '净利润_2021': ('净利润_2021', 15),
            '净利润_2022': ('净利润_2022', 16),
            '资产负债率': ('资产负债率', 17),
            '净资产': ('净资产', 18),
            '总资产增长': ('总资产增长', 19),
            '总资产_2020': ('总资产_2020', 20),
            '总资产_2021': ('总资产_2021', 21),
            '总资产_2022': ('总资产_2022', 22),
        }
        self._excel_name = self.name_add_date("交行财务数据统计.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def sort_by_year(self, input_str):
        items = input_str.split(',')
        sorted_items = sorted(items)
        # sorted_items = [item for item in sorted_items if
        #                   item.startswith('2020') or item.startswith('2021') or item.startswith('2022')]
        output_str = ','.join(sorted_items)
        return output_str

    def query_a_zzc_data(self):
        result = dict()
        sql = """
        select compCode, endDate, totAsset,reportType from sy_cd_ms_fin_sk_balsheet    
        where dataStatus!=3 and reportType in (1,3)  and endDate >"2020" and endDate like "%12-31%"
        """
        query_schema = dict(db_key="xskv2", sql_statement=sql)
        result_items = self._data_server.call("query_sql_item", query_schema)
        new_items = self.get_dict_with_priority(result_items)
        for item in new_items:
            compCode = item["compCode"]
            grossRatio = item["totAsset"]
            result.setdefault(compCode, dict())
            endDate = str(item["endDate"])
            if "2020" in endDate:
                result[compCode].setdefault("总资产_2020", grossRatio)
            elif "2021" in endDate:
                result[compCode].setdefault("总资产_2021", grossRatio)
            elif "2022" in endDate:
                result[compCode].setdefault("总资产_2022", grossRatio)
        return result

    def query_three_zzc_data(self):
        result = dict()
        sql = """
        select compCode, endDate, totalAssets,infoPublDate from sy_cd_ms_fin_nq_balsheet    
        where dataStatus!=3  and endDate >"2020" and endDate like "%12-31%" and ifMerged=1
        """
        query_schema = dict(db_key="xskv2", sql_statement=sql)
        result_items = self._data_server.call("query_sql_item", query_schema)
        new_items = self.get_latest_dicts(result_items)
        for item in new_items:
            compCode = item["compCode"]
            grossRatio = item["totalAssets"]
            result.setdefault(compCode, dict())
            endDate = str(item["endDate"])
            if "2020" in endDate:
                result[compCode].setdefault("总资产_2020", grossRatio)
            elif "2021" in endDate:
                result[compCode].setdefault("总资产_2021", grossRatio)
            elif "2022" in endDate:
                result[compCode].setdefault("总资产_2022", grossRatio)
        return result

    def get_latest_dicts(self, data_list):
        latest_dicts = {}

        for data_dict in data_list:
            comp_code = data_dict.get('compCode')
            end_date = data_dict.get('endDate')
            info_pub_date = data_dict.get('infoPublDate')

            if comp_code and end_date and info_pub_date:
                key = (comp_code, end_date)
                if key not in latest_dicts or info_pub_date > latest_dicts[key].get('infoPublDate'):
                    latest_dicts[key] = data_dict

        return list(latest_dicts.values())

    def query_three_yysr_data_2(self):
        result = dict()
        sql = """
        select compCode, endDateProfit as endDate, ifAdjustProfit as mark, round(grossRatio *100, 4) as grossRatio from sy_cd_ms_fin_nq_mainindex 
        where dataStatus!=3 and ifAdjustProfit in (1,2)  and endDateProfit >"2020" and endDateProfit like "%12-31%"
        """
        query_schema = dict(db_key="xskv2", sql_statement=sql)
        result_items = self._data_server.call("query_sql_item", query_schema)
        new_items = self.get_dict_with_priority_three(result_items)
        for item in new_items:
            compCode = item["compCode"]
            grossRatio = item["grossRatio"]
            result.setdefault(compCode, dict())
            endDate = str(item["endDate"].date())
            if "2020" in endDate:
                result[compCode].setdefault("毛利率_2020", grossRatio)
            elif "2021" in endDate:
                result[compCode].setdefault("毛利率_2021", grossRatio)
            elif "2022" in endDate:
                result[compCode].setdefault("毛利率_2022", grossRatio)
        return result

    def query_three_yysr_data(self):
        result = dict()
        sql = """
        select compCode, endDate, mark, operatingIncome,netProfit from sy_cd_ms_fin_nq_maindata 
        where dataStatus!=3 and mark in (1,2)  and endDate >"2020" and endDate like "%12-31%"
        """
        query_schema = dict(db_key="xskv2", sql_statement=sql)
        result_items = self._data_server.call("query_sql_item", query_schema)
        new_items = self.get_dict_with_priority_three(result_items)
        for item in new_items:
            compCode = item["compCode"]
            mainBusiIncome = item["operatingIncome"]
            netProfit = item["netProfit"]
            result.setdefault(compCode, dict())
            endDate = str(item["endDate"].date())
            if "2020" in endDate:
                result[compCode].setdefault("营业收入_2020", mainBusiIncome)
                result[compCode].setdefault("净利润_2020", netProfit)
            elif "2021" in endDate:
                result[compCode].setdefault("营业收入_2021", mainBusiIncome)
                result[compCode].setdefault("净利润_2021", netProfit)
            elif "2022" in endDate:
                result[compCode].setdefault("营业收入_2022", mainBusiIncome)
                result[compCode].setdefault("净利润_2022", netProfit)
        return result

    def get_dict_with_priority_three(self, lst):
        compcode_dict = {}

        for d in lst:
            compCode = d['compCode']
            endDate = str(d['endDate'].date())
            mark = d['mark']

            if compCode not in compcode_dict:
                compcode_dict[compCode] = {}

            if endDate not in compcode_dict[compCode]:
                compcode_dict[compCode][endDate] = {}

            if str(mark) == "1":
                compcode_dict[compCode][endDate] = d
            elif str(mark) == "2" and 'mark' not in compcode_dict[compCode][endDate]:
                compcode_dict[compCode][endDate] = d

        result = []
        for compCode, comp_dict in compcode_dict.items():
            for endDate, dict_with_priority in comp_dict.items():
                result.append(dict_with_priority)
        return result

    def query_a_yysr_data(self):
        result = dict()
        sql = """
        select compCode, endDate, reportType, mainBusiIncome, sgpmargin, netProfit * 10000 as netProfit from sy_cd_ms_fin_sk_mainindex 
        where dataStatus!=3 and reportType in (1,3)  and endDate >"2020" and endDate like "%1231%"
        """
        query_schema = dict(db_key="xskv2", sql_statement=sql)
        result_items = self._data_server.call("query_sql_item", query_schema)
        new_items = self.get_dict_with_priority(result_items)
        for item in new_items:
            compCode = item["compCode"]
            mainBusiIncome = item["mainBusiIncome"]
            sgpmargin = item["sgpmargin"]
            netProfit = item["netProfit"]
            result.setdefault(compCode, dict())
            endDate = item["endDate"]
            if "2020" in endDate:
                result[compCode].setdefault("营业收入_2020", mainBusiIncome)
                result[compCode].setdefault("毛利率_2020", sgpmargin)
                result[compCode].setdefault("净利润_2020", netProfit)
            elif "2021" in endDate:
                result[compCode].setdefault("营业收入_2021", mainBusiIncome)
                result[compCode].setdefault("毛利率_2021", sgpmargin)
                result[compCode].setdefault("净利润_2021", netProfit)
            elif "2022" in endDate:
                result[compCode].setdefault("营业收入_2022", mainBusiIncome)
                result[compCode].setdefault("毛利率_2022", sgpmargin)
                result[compCode].setdefault("净利润_2022", netProfit)
        return result

    def get_dict_with_priority(self, lst):
        compcode_dict = {}

        for d in lst:
            compCode = d['compCode']
            endDate = d['endDate']
            reportType = d['reportType']

            if compCode not in compcode_dict:
                compcode_dict[compCode] = {}

            if endDate not in compcode_dict[compCode]:
                compcode_dict[compCode][endDate] = {}

            if str(reportType) == "3":
                compcode_dict[compCode][endDate] = d
            elif str(reportType) == "1" and 'reportType' not in compcode_dict[compCode][endDate]:
                compcode_dict[compCode][endDate] = d

        result = []
        for compCode, comp_dict in compcode_dict.items():
            for endDate, dict_with_priority in comp_dict.items():
                result.append(dict_with_priority)

        return result

    def query_a_name(self):
        result = dict()
        sql = """
        SELECT id, compCode,compName,if(listStatus=1,"上市", "非上市") as listStatus, dataStatus, "A股" as ty,listDate from sy_cd_ms_base_sk_stock where  
        id > {} ORDER BY id ASC limit 1000;
        """
        for items in self._query_sql_iter_by_id(sql, "xskv2"):
            for item in items:
                compCode = item["compCode"]
                dataStatus = item["dataStatus"]
                if dataStatus == 3:
                    continue
                result.setdefault(compCode, item)
        return result

    def query_three_name(self):
        result = dict()
        sql = """
        SELECT id, compCode,compName,if(listStatus=1,"上市", "非上市") as listStatus, dataStatus, "三板" as ty,listDate from sy_cd_ms_base_nq_stock where
        id > {} ORDER BY id ASC limit 1000;
        """
        for items in self._query_sql_iter_by_id(sql, "xskv2"):
            for item in items:
                compCode = item["compCode"]
                dataStatus = item["dataStatus"]
                if dataStatus == 3:
                    continue
                result.setdefault(compCode, item)
        return result


if __name__ == '__main__':
    p = Static()
    p.process()
