# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/09
import json
import http.client
import traceback
from elasticsearch.helpers import scan
from elasticsearch import Elasticsearch

from core.excel_base import ExcelBase
from moduler.company_area_info import CompanyArea


class AreaProject(ExcelBase):
    def __init__(self):
        super(AreaProject, self).__init__()
        self.area = CompanyArea()
        self.build = {
            'ownerCompany': ('业主单位名称', 0),
            'projectName': ('项目名称', 1),
            'provinceArea': ('地区', 2),
            'publishDate': ('发布日期', 3),
            'industry': ('行业', 4),
            'period': ('建设周期', 5),
            'progress': ('进展阶段', 6),
            'amount': ('总投资金额（万元）', 7),
            'location': ('所在地', 8),
            'scale': ('建设内容及规模', 9),
            'content': ('项目简介', 10),
            'company_business': ('业务亮点', 11)
        }
        self.zb_build = {
            'company': ('单位名称', 0),
            'pro_name': ('项目名称', 1),
            'pro_code': ('项目代码', 2),
            'pro_content': ('项目内容', 3),
            'approval_matters': ('审批事项', 4),
            'approval_dep': ('审批部门', 5),
            'approval_result': ('审批结果', 6),
            'approval_time': ('审批时间', 7),
            'approval_num': ('审批文号', 8),
            'pro_type': ('项目类型', 9),
            'province': ('省份', 10),
            'company_business': ('业务亮点', 11),
        }

    def process(self, *args, **kwargs):
        result1, result2 = list(), list()
        for item in self.ext_build_project():
            name = item["ownerCompany"]
            area_dict = self.area.run([name]) or dict()
            city = area_dict.get(name, dict()).get("cityName")
            if city != '泰州市':
                continue
            # 标签信息
            cat_dict = self.query_category_info(name)
            category_data = self.category_data_process(cat_dict)
            item.update(category_data)
            result1.append(item)
        self.save_data_excel(result1, self.build, "泰州市重大资产在建项目")

        for item in self.ext_project():
            name = item["company"]
            area_dict = self.area.run([name]) or dict()
            city = area_dict.get(name, dict()).get("cityName")
            if city != '泰州市':
                continue
            # 标签信息
            cat_dict = self.query_category_info(name)
            category_data = self.category_data_process(cat_dict)
            item.update(category_data)
            result2.append(item)
        self.save_data_excel(result2, self.zb_build, "泰州市备案核准投资项目")

    def save_data_excel(self, result, field_cfg, name):
        self._excel_name = self.name_add_date("{}.xlsx".format(name))
        self.save_to_excel(field_cfg, {"sheet1": result})

    @staticmethod
    def query_category_info(raw_name):
        result_list = list()
        headers = {"Content-type": "application/x-www-form-urlencoded",
                   "Accept": "text/plain"}
        httpClient = http.client.HTTPConnection("10.10.128.185", 6226, timeout=30)
        # httpClient = http.client.HTTPConnection("60.205.212.21", 6226, timeout=30)
        cookie, limit_num = None, 20
        while True:
            try:
                request_1_1 = {
                    "user_id": "lqp",
                    "cname": raw_name}
                if cookie is not None:
                    request_1_1["cookie"] = cookie
                params = json.dumps(request_1_1)
                httpClient.request(
                    "POST", "/v1/companys/category_info", params, headers)
                response = httpClient.getresponse()
                result = response.read().decode("utf-8")
                result = json.loads(result)
                if result["response_state"] != 1:
                    raise Exception("request err.param={}".format(params))
                response_content = result["response_content"]
                return response_content
            except Exception as exc:
                err_msg = traceback.format_exc()
                err_msg = " ## ".join(err_msg.split("\n"))
                err_msg = " ## ".join([err_msg, str(exc)])
                print(err_msg)
                break
        if httpClient is not None:
            httpClient.close()
        return result_list

    @staticmethod
    def category_data_process(cat_dict):
        """
        :param cat_dict:
        :return:  company_nature:性质标签, market:所属市场, company_business:业务亮点
        """
        category_dict = dict()
        # 过滤掉 政府奖励 证照 起草标准 的标签
        # filter_cat = [
        #     "SYAP10001#", "SYAP10002#", "SYAP10003#", "SYAP10004#", "SYAM10000#",
        #     "SYAP10005#", "SYAQ10001#", "SYAQ10002#", "SYAQ10003#"]
        filter_cat = []
        for cat_key in ["company_nature", "market"]:
            category_dict.setdefault(cat_key, ','.join([i["name"] for i in cat_dict.get(cat_key, list())]))
        company_business = cat_dict.get("company_business", list())
        c_bus = list()
        for item in company_business:
            cat = item["category"]
            name = item["name"]
            if "SYAM10000" in cat:
                names = [i for i in name.split("-") if i != '证照']
                category_dict.setdefault("licence", list())
                category_dict["licence"].extend(names)

            if cat not in filter_cat:
                c_bus.append(name)
        category_dict["company_business"] = ','.join(c_bus)

        licence = category_dict.get("licence")
        if licence:
            category_dict["licence"] = ",".join(licence)
        return category_dict

    def ext_build_project(self):
        """
        重大在建项目
        :param company_list:
        :return:
        """
        sql_statement = """SELECT * from ext_build_project where provinceArea='江苏';"""
        query_schema = dict(db_key="seeyii", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list

    def ext_project(self):
        """
        备案核准投资项目
        :param :
        :return:
        """
        result = list()
        self.__es = Elasticsearch(
            hosts=[{"host": "*************",
                    "port": 1482}], timeout=120)
        search_body = {
            "query": {"match_phrase": {
                "province": "江苏省"
            }}
        }
        raw_data = scan(
            self.__es, search_body, scroll="10m", index="invest_index_v2",
            doc_type="important_invest", timeout="10m")
        for item in raw_data:
            raw_item = item["_source"]
            result.append(raw_item)
        return result


if __name__ == '__main__':
    p = AreaProject()
    p.run()
