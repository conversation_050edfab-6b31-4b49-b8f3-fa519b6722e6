# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/02/22
import os
import json
import decimal
import xlsxwriter
from itertools import chain
from datetime import datetime, date

from cfg.config import out_file_path
from core.excel_base import ExcelBase
from moduler.base_info_api_thread import BaseInfoMaster
from moduler.company_build_project import CompanyMajorProject
from moduler.company_category import CompanyCategory, cat_map
from moduler.company_inv_event import InvEvent
from moduler.company_law_data import CompanyCPWS, CompanyFYGG, CompanyKTGG
from moduler.company_mysql_licence import MysqlLicence
from moduler.company_mysql_reward import MysqlReward
from moduler.company_mysql_standar import MysqlStandar
from moduler.company_repoet_shebao import SheBao
from moduler.company_staff_data import CompanyStaffData
from moduler.company_supply_data import CompanySupply
from utils.datetime_util import DatetimeUtil

"""
导出内容：
1、工商概况。 
2、企业标签。
3、重大在建项目。
4、专项证照。
5、政府奖励。
6、起草标准。
7、供应商及客户。
8、裁判文书。
9、开庭公告。
10、法院公告。
11、公司高管。
12、职工社保。
13、投融资事件（101、201、301、401、501）。
"""

class SCFH(ExcelBase):
    def __init__(self):
        super(SCFH, self).__init__()
        self.fun_map = {
            "工商概况": BaseInfoMaster,
            "企业标签": CompanyCategory,
            "重大在建项目": CompanyMajorProject,
            "专项证照": MysqlLicence,
            "政府奖励": MysqlReward,
            "起草标准": MysqlStandar,
            "供应商及客户": CompanySupply,
            "裁判文书": CompanyCPWS,
            "开庭公告": CompanyKTGG,
            "法院公告": CompanyFYGG,
            "公司高管": CompanyStaffData,
            "职工社保": SheBao,
            "投融资事件": InvEvent,
        }
        self.field_dict_map = {
            "工商概况": {
                'companyName': ('公司名称', 0),
                'credit_code': ('统一信用代码', 1),
                'legal_person_name': ('法定代表人', 2),
                'reg_capital': ('注册资本', 3),
                'establish_date': ('成立时间', 4),
                'reg_status': ('注册状态', 5),
                'company_org_type': ('公司组织类型', 6),
                'reg_province': ('省', 7),
                'reg_city': ('市', 8),
                'reg_district': ('区', 9),
                'reg_location': ('注册地址', 10),
                'postal_address': ('办公地址', 11),
                'phone_number': ('联系电话', 12),
                'business_scope': ('经营范围', 13),
                'company_nature': ('性质标签', 14),
                'market': ('所属市场', 15),
                'company_business': ('业务亮点', 16)},
            "企业标签": {
                'cname': ('公司名称', 0),
                'category': ('标签', 1),
            },
            "重大在建项目": {
                'ownerCompany': ('业主单位名称', 0),
                'projectName': ('项目名称', 1),
                'provinceArea': ('地区', 2),
                'publishDate': ('发布日期', 3),
                'industry': ('行业', 4),
                'period': ('建设周期', 5),
                'progress': ('进展阶段', 6),
                'amount': ('总投资金额（万元）', 7),
                'location': ('所在地', 8),
                'scale': ('建设内容及规模', 9),
                'content': ('项目简介', 10),
            },
            "专项证照": {
                'compName': ('公司名称', 0),
                'licenceBeginDate': ('生效日期', 1),
                'licenceCategory': ('证照分类', 2),
                'licenceType': ('证照类型', 3),
                'publishOrg': ('发布机关', 4),
                'details': ('证照详情', 5),
            },
            "政府奖励": {
                'compName': ('公司名称', 0),
                'rewardSources': ('奖励来源', 1),
                'rewardName': ('奖励名称', 2),
                'rewardProject': ('奖励项目', 3),
                'rewardYear': ('奖励年份', 4),
                'areaCategory': ('奖励级别', 5)},
            "起草标准": {
                'draftOrg': ('起草单位', 0),
                'standardName': ('标准名称', 1),
                'standardType': ('标准类型', 2),
                'teamName': ('标准团体', 3),
                'relatedProducts': ('相关产品', 4),
                'publishDate': ('发布时间', 5),
                'details': ('详情', 6),
            },
            "供应商及客户": {
                'cname': ('公司名称', 0),
                'company_name': ('供应商名称', 1),
                'relation': ('关系类型', 2),
            },
            "裁判文书": {
                'plaintiff': ('原告/上诉人', 0),
                'plaintiffs': ('原告们', 1),
                'defendant': ('被告/被上诉人', 2),
                'defendants': ('被告们', 3),
                'thirdParty': ('第三人', 4),
                'thirdParties': ('第三人们', 5),
                'caseId': ('案号', 6),
                'caseType': ('案件类型', 7),
                'documentType': ('文书类型', 8),
                'provinceCode': ('省份编码', 9),
                'lawCourt': ('法院', 10),
                'judgeTime': ('裁判日期', 11),
                'submitTime': ('发布时间', 12),
                'documentTitle': ('文书标题', 13),
                'documentHead': ('文书首部', 14),
                'documentText': ('文书正文', 15),
                'documentTail': ('文书尾部', 16),
                'partiesInfo': ('当事人们信息', 17),
                'lawsuitRecord': ('申诉记录', 18),
                'caseFact': ('案件事实', 19),
                'judgeReason': ('判决理由', 20),
                'judgeConclusion': ('判决结论', 21),
                'lawCaseReason': ('案由', 22),
                'trialProcedure': ('审理程序', 23),
                'lawBasis': ('法律依据', 24), },
            "开庭公告": {'party': ('当事人（公示对象）', 0),
                     'eventTime': ('开庭日期', 1),
                     'plaintiff': ('原告', 2),
                     'defendant': ('被告', 3),
                     'court': ('法院', 4),
                     'caseReason': ('案由', 5),
                     'courtroom': ('法庭', 6),
                     'contractors': ('承办部门', 7),
                     'planDate': ('排期日期', 8),
                     'caseNo': ('案号', 9),
                     'judge': ('审判长', 10),
                     'content': ('公告内容', 11), },
            "法院公告": {
                'party': ('当事人', 0),
                'court': ('公告人', 1),
                'punishDetail': ('案由', 2),
                'noticeType': ('公告类型', 3),
                'content': ('案件内容', 4),
                'ctimeDate': ('公示宣布日', 5),
            },
            "公司高管": {
                'cname': ('公司名称', 0),
                'name': ('高管名称', 1),
                'position': ('职务', 2),
            },
            "职工社保": {
                'name': ('公司名称', 0),
                'endowment_insurance': ('城镇职工基本养老保险', 1),
                'unemployment_insurance': ('失业保险', 2),
                'medical_insurance': ('职工基本医疗保险', 3),
                'employment_injury_insurance': ('工伤保险', 4),
                'maternity_insurance': ('生育保险', 5),
            },
            "投融资事件": {
                'subject': ('投资主体', 0),
                'compName': ('被投企业', 1),
                'chgType': ('投资类型', 2),
                'chgDate': ('投资日期', 3),
                'chgRatio': ('变更股比', 4),
                'chgAmount': ('投资数量', 5),
                'chgCash': ('投资金额', 6),
                'chgPrice': ('投资价格', 7),
            },
        }

    def process(self, *args, **kwargs):
        for exc in [
            "四川省拥有电力证照的公司.xlsx",
            "国家电网公司下属投资的四川公司名单.xlsx",
            "电力产业链相关公司.xlsx"
        ]:
            names = self.read_excel_data(self._in_file_path + "/{}".format(exc))
            name_list = list({i["公司名称"] for i in names})
            output_file_path = os.path.join(out_file_path, exc)
            xls_file = xlsxwriter.Workbook(output_file_path, options={'strings_to_urls': False})
            xls_file.allow_zip64 = True
            for sheet, fun in self.fun_map.items():
                result = list()
                self.data_process(result, fun, name_list, sheet)
                xls_sheet = xls_file.add_worksheet(name=sheet)
                self._save_to_excel(self.field_dict_map[sheet], xls_sheet, result, sheet)
            xls_file.close()

    def data_process(self, result, fun, name_list, sheet):
        result_data = fun.run(name_list)
        if sheet == '企业标签':
            for item in result_data.values():
                cat_list = item["category"]
                tmp_cat_list = list()
                for cat in cat_list:
                    if "#" in cat:
                        s_cat, num = cat.split("#")
                        tmp_cat_list.append(cat_map[s_cat].format(num))
                    else:
                        tmp_cat_list.append(cat_map[cat])
                result.append({"cname": item["cname"], "category": ",".join(tmp_cat_list)})
        elif sheet == '裁判文书':
            finger_id_set = set()
            for item in list(chain.from_iterable(result_data.values())):
                finger_id = item["fingerId"]
                if finger_id in finger_id_set:
                    continue
                finger_id_set.add(finger_id)
                result.append(item)
        elif sheet in {"工商概况", "职工社保"}:  # dict
            result.extend(result_data.values())
        elif sheet in {"专项证照", "政府奖励", "起草标准", "开庭公告", "法院公告",
                       "重大在建项目", "供应商及客户", "公司高管", "投融资事件"}:  # list
            result.extend(list(chain.from_iterable(result_data.values())))

    @staticmethod
    def _save_to_excel(field_dict, xls_sheet, item_list, sheet):
        for title in field_dict.values():
            xls_sheet.write(0, title[1], title[0])
        row_no = 1
        for result_item in item_list:
            for field_name, title in field_dict.items():
                field_value = result_item.get(field_name, "")
                if isinstance(field_value, decimal.Decimal):
                    field_value = float(field_value)
                if isinstance(field_value, datetime):
                    field_value = DatetimeUtil.date_to_str(field_value)
                if isinstance(field_value, date):
                    field_value = field_value.strftime("%Y-%m-%d")
                if not isinstance(field_value, str):
                    field_value = json.dumps(field_value, ensure_ascii=False)
                if field_value == "null":
                    continue
                xls_sheet.write(row_no, title[1], field_value)
            row_no += 1
        print("{} finished.".format(sheet))

    def read_excel_data(self, file_name, sheet="Sheet1"):
        data_list = self._extract_data(file_name, sheet)
        return data_list


if __name__ == '__main__':
    p = SCFH()
    p.process()
