# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2022/9/1
import datetime
import itertools
import re
from openpyxl import Workbook
from openpyxl.styles import Alignment, Font, colors, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
import urllib.parse
import pymysql
import argparse
import copy


class bullentin:
    def __init__(self):
        # self.tp = [
        #     'SJ000101004', 'SJ000103008', 'SJ000101009', 'SJ000302003', 'SJ000103001', 'SJ000103003',
        #     'SJ000103005',
        #     'SJ000301002', 'SJ000101003', 'SJ000103006', 'SJ000103007', 'SJ000301001', 'SJ000302001', 'SJ000302005',
        #     'SJ000101001',
        #     'SJ000102003', 'SJ000102005', 'SJ000102006', 'SJ000102007', 'SJ000102008', 'SJ000402001', 'SJ100204003',
        #     'SJ000101005',
        #     'SJ000101007', 'SJ000202001', 'SJ000401002', 'SJ000402002', 'SJ000402003', 'SJ000402004', 'SJ000302007',
        #     'SJ000305001',
        #     'SJ000204005', 'SJ000304001', 'SJ000402005', 'SJ000401001', 'SJ000102009'
        # ]
        self.tp = {
            "SJ000101004": "新药研发进入新阶段", "SJ000103008": "获得国家级科技成果奖励", "SJ000101009": "制定并发布标准",
            "SJ000302003": "成为央企供应商", "SJ000103001": "获评“技术创新示范企业”称号", "SJ000103003": "获评“服务型制造示范企业”称号",
            "SJ000103005": "获评“国家众创空间”称号", "SJ000301002": "重大项目获得环评通过", "SJ000101003": "最新获得集成电路专利",
            "SJ000103006": "获得国家级科技成果转化项目", "SJ000103007": "获得省级科技成果转化项目", "SJ000301001": "重大固定资产投资项目获批",
            "SJ000302001": "成为上市公司供应商", "SJ000302005": "成为金融机构供应商", "SJ000101001": "获得专利",
            "SJ000102003": "认定企业重点实验室", "SJ000102005": "认定企业技术中心", "SJ000102006": "认定院士专家工作站",
            "SJ000102007": "认定工程研究中心/实验室", "SJ000102008": "认定新型研发机构", "SJ000402001": "原有发行债券即将到期",
            "SJ100204003": "最近1年连续融资", "SJ000101005": "获得商标", "SJ000101007": "获得软件著作权",
            "SJ000202001": "最新完成股权再融资", "SJ000401002": "减持上市公司股份套现", "SJ000402002": "A股公司股权质押即将到期",
            "SJ000402003": "新三板公司股权质押即将到期", "SJ000402004": "新三板公司贷款即将到期", "SJ000302007": "成为政府机构供应商",
            "SJ000305001": "新注册工商企业", "SJ000204005": "募集设立新基金（已备案）", "SJ000304001": "最新迁址",
            "SJ000402005": "A股公司贷款即将到期", "SJ000401001": "机构理财产品即将到期（仅上市公司）", "SJ000102009": "认定高新技术企业研发中心"
        }

    def poss(self, startDate, endDate, period):
        # data2, tpe = self.query_sj(startDate, endDate)
        # print('data2')
        # print(data2)
        data1 = self.query_join_financing(startDate, endDate)
        print('data1')
        print(data1)
        # data3 = self.start_ipo_coach(startDate, endDate)
        # print('data3')
        # print(data3)
        # data4 = self.ipo_shgh(startDate, endDate)
        # print('data4')
        # print(data4)
        # data5, description = self.ipo_bf(startDate, endDate)
        # print('data5')
        # print(data5)
        # print(description)
        # ex_ame = self.excel_css(startDate, endDate, period, data1, data2, data3, data4, data5, description, tpe)

    def connect_135(self, sqlexpression):
        db = pymysql.connect(host='*************',
                             port=4000,
                             user='kf2_poc',
                             password='shiye1805A',
                             database='seeyii_assets_database',
                             charset='utf8',
                             cursorclass=pymysql.cursors.DictCursor
                             )
        cursor = db.cursor()
        # 使用execute方法执行sql语句
        cursor.execute(sqlexpression)
        # 使用fetall()获取全部数据
        data = cursor.fetchall()
        # 打印所有的数据
        # print(data)
        # 关闭游标的数据库链接
        cursor.close()
        db.close()
        return list(data)

    def connect_152(self, sqlexpression):
        db = pymysql.connect(host='*************',
                             port=4000,
                             user='kf2_tmp',
                             password='shiye1805A',
                             database='seeyii_assets_database',
                             charset='utf8',
                             cursorclass=pymysql.cursors.DictCursor
                             )
        cursor = db.cursor()
        # 使用execute方法执行sql语句
        cursor.execute(sqlexpression)
        # 使用fetall()获取全部数据
        data = cursor.fetchall()
        # 打印所有的数据
        # print(data)
        # 关闭游标的数据库链接
        cursor.close()
        db.close()
        return list(data)

    def connect_136(self, sqlexpression):
        db = pymysql.connect(host='************',
                             port=63306,
                             user='kf2_poc',
                             password='shiye1805A',
                             database='seeyii_db',
                             charset='utf8',
                             cursorclass=pymysql.cursors.DictCursor
                             )
        cursor = db.cursor()
        # 使用execute方法执行sql语句
        cursor.execute(sqlexpression)
        # 使用fetall()获取全部数据
        data = cursor.fetchall()
        # 打印所有的数据
        # print(data)
        # 关闭游标的数据库链接
        cursor.close()
        db.close()
        return list(data)

    def query_join_financing(self, startDate, endDate):
        dat = self.query_sj_financing(startDate, endDate)
        # if not dat:
        #     print('数据不存在')
        #     return list()
        # sourceid_str = "','".join([str(x) for x in dat[1]])
        # # print(sourceid_str)
        # sqlexpression = """select id as sourceId, investmentTurn  from sy_cd_me_news_oth_financing where id in ('{}') and dataStatus<>3""".format(
        #     sourceid_str)
        # # print('融资++++++++++++++++++++++++++++++++++++')
        # # print(sqlexpression)
        # dat_financing = self.connect_152(sqlexpression.format())
        # financing_result = dict()
        # for item in dat_financing:
        #     financing_result[str(item["sourceId"])] = item['investmentTurn']
        # # print(financing_result)
        # data1 = copy.deepcopy(dat[0])
        # for item in data1:
        #     sourceId = item.get('sourceId')
        #     item['融资轮次'] = financing_result[sourceId]
        #     del item['sourceId']
        return dat

    def query_sj_financing(self, startDate, endDate):
        fin_sql = """
        select companyFullName '商机主体',investmentTurn '融资轮次', financeDate '生效日期',
        CONCAT(province,'-',city,'-',district) '地域'
         from sy_cd_me_news_oth_financing where financeDate >= "{}" AND financeDate <="{}" 
         and dataStatus<>3 and investmentTurn !="IPO上市"
        """.format(startDate, endDate)
        fin_dict = dict()
        fin_data_list = self.connect_135(fin_sql)
        for f_item in fin_data_list:
            fin_dict.setdefault(f_item["商机主体"], f_item)

        const_sql = """
        select cValue from sy_cd_mt_sys_const where 
        constValueDesc in ("农、林、牧、渔业", "采矿业", "批发和零售业", "住宿和餐饮业", "房地产业") and constCode=3
        """
        const_list = self.connect_135(const_sql)
        const_values = list()
        for c_item in const_list:
            const_values.append(str(c_item["cValue"]))

        cmp_info_sql = """
        select compName, FLOOR(a.regCapital/1000000) '注册资本(万元)', a.ssfCount '员工人数', b.constValueDesc '所属行业'
        from sy_cd_ms_base_gs_comp_info_new as a 
        left join sy_cd_mt_sys_const as b on a.industryId=b.cValue and a.dataStatus!=3
        where b.cValue not in ({}) and b.dataStatus!=3  and constCode=5
        AND a.ssfCount>50 AND a.currencyCode = "人民币" AND a.compName NOT LIKE "%股份%" 
        and a.compName in ({})
        """.format(','.join(["{!r}".format(i) for i in const_values]),
                   ",".join(["{!r}".format(i) for i in fin_dict.keys()]))

        cmp_list = self.connect_135(cmp_info_sql)
        cmp_dict = dict()
        for c_item in cmp_list:
            cmp = c_item.pop("compName")
            cmp_dict.setdefault(cmp, c_item)

        result = list()
        for name, item in fin_dict.items():
            cmp_info = cmp_dict.get(name, dict())
            item.update(cmp_info)
            if len(item)>=6:
                result.append(item)
        result.sort(key=lambda x: (x['生效日期']), reverse=True)
        new_result = result[:10]
        for i, item in enumerate(new_result, start=1):
            item['序号'] = i
        return result

        # sqlexpression = """SELECT  a.`sourceId` 'sourceId',o.eventDate '生效日期' ,o.eventSubject '商机主体'
        #             ,middleName '所属行业',CONCAT(provinceName,'-',cityName,'-',district) '地域' ,FLOOR(c.regCapital/1000000) '注册资本(万元)',c.ssfCount '员工人数'
        #             FROM sy_sa_sysk_me_buss_event_oppo o
        #             LEFT JOIN sy_cd_ms_base_gs_comp_info_new c ON o.eventSubject = c.compName
        #             LEFT JOIN sy_cd_mt_sys_const  const ON const.cValue = o.eventType
        #             LEFT JOIN sy_cd_me_buss_event_oppo_retro a ON o.`eventId` = a.`eventId`
        #             WHERE o.eventType IN ("SJ000204001") AND eventDate >= "{}"  AND eventDate <="{}"
        #             AND a.dataStatus<>3 AND c.dataStatus<>3 AND o.dataStatus<>3 AND ssfCount>50   AND c.currencyCode = "人民币"
        #             AND o.eventSubject NOT LIKE "%股份%" AND o.className<>"农、林、牧、渔业"AND o.className<>"采矿业"
        #             AND o.className <>"批发和零售业" AND  o.className <>"住宿和餐饮业" AND  o.className <>"房地产业"
        #             ORDER BY o.eventType,eventDate DESC LIMIT 10;""".format(startDate, endDate)
        # # print(sqlexpression)
        # # for tpe in self.tp:
        # data1 = self.connect_135(sqlexpression)
        # if len(data1) >= 5:
        #     data1.sort(key=lambda x: (x['生效日期']), reverse=True)
        #     sourceid_list = list()
        #     for i, item in enumerate(data1, start=1):
        #         item['序号'] = i
        #         sourceid_list.append(item['sourceId'])
        #     return data1, sourceid_list
        # return None

    def query_sj(self, startDate, endDate):
        sqlexpression = """SELECT  o.eventDate '生效日期' ,o.eventSubject '商机主体' 
            ,middleName '所属行业',CONCAT(provinceName,'-',cityName,'-',district) '地域' ,floor(c.regCapital/1000000) '注册资本(万元)',c.ssfCount '员工人数' 
            from sy_sa_sysk_me_buss_event_oppo o 
            left join sy_cd_ms_base_gs_comp_info_new c on o.eventSubject = c.compName
            left join sy_cd_mt_sys_const  const on const.cValue = o.eventType
            where o.eventType in ("{}") and eventDate >= "{}"  and eventDate <="{}"
            and c.dataStatus<>3 and o.dataStatus<>3 and ssfCount>50   and c.currencyCode = "人民币"
            and o.eventSubject not like "%股份%" and o.className<>"农、林、牧、渔业"and o.className<>"采矿业" 
            and o.className <>"批发和零售业" and  o.className <>"住宿和餐饮业" and  o.className <>"房地产业" 
            order by o.eventType,eventDate desc limit 5;"""
        # print(sqlexpression)
        for tpe in list(self.tp.keys()):
            data = self.connect_135(sqlexpression.format(tpe, startDate, endDate))
            if len(data) >= 5:
                print("data2数据商机类型为:{}".format(tpe))
                data.sort(key=lambda x: (x['生效日期']), reverse=True)
                for i, item in enumerate(data, start=1):
                    item['序号'] = i
                return data, self.tp[tpe]
        return "没有符合条件的数据", "没有符合条件的数据"

    def start_ipo_coach(self, startDate, endDate):
        # sqlexpression = """select statDate '启动辅导日期' ,ipo.compName '公司名称' , const.constValueDesc'所属行业',CONCAT(geo.provinceName,'-',geo.cityName,'-',geo.district) '地域',ipo.reguInstitution '证监局',ipo.preName '辅导机构' from (
        #     SELECT a.* FROM `sy_cd_me_trad_ipo_base` a,
        #     (SELECT projId FROM `sy_cd_me_trad_ipo_detail` WHERE  statDate >= "{}"  AND statDate <="{}"
        #     AND dataStatus<3 AND stat=101) b
        #     WHERE  a.projId =b.projId AND a.dataStatus<3
        #     ) ipo
        #     left join sy_cd_ms_base_gs_comp_info_new c on ipo.compName = c.compName
        #     left join sy_cd_ms_base_comp_geo_new geo on c.compCode = geo.compCode
        #     left join sy_cd_mt_sys_const const on const.cValue = c.industryId
        #     left join (select * from sy_cd_mt_sys_const where constCode = 13 and dataStatus<3) const2 on const2.constValue = ipo.market
        #     where c.dataStatus<3 and geo.dataStatus<3 and const.dataStatus<3 and const.constCode = 5
        #     order by statDate desc ;"""
        # data = self.connect_135(sqlexpression.format(startDate, endDate))

        sqlexpression = """
        SELECT a.statDate '启动辅导日期',a.compName '公司名称',a.reguInstitution '证监局', a.preName '辅导机构'
            FROM `dws_me_trad_ipo_base` a,
            (SELECT * FROM `dws_me_trad_ipo_detail` WHERE   dataStatus<3 AND stat=101
            and  statDate >="{}" AND  statDate <="{}") b 
            WHERE  a.projId =b.projId AND a.dataStatus<3
        """
        data = self.connect_136(sqlexpression.format(startDate, endDate))
        name = [itm['公司名称'] for itm in data]
        ind_sqlexpression = """
        select a.compName '公司名称',const.constValueDesc'所属行业' ,CONCAT(geo.provinceName,'-',geo.cityName,'-',geo.district) '地域'
        from sy_cd_ms_base_gs_comp_info_new as a 
        left join (select * from sy_cd_mt_sys_const where constCode = 5 and dataStatus<3) const on const.cValue = a.industryId
        left join sy_cd_ms_base_comp_geo_new geo on a.compCode = geo.compCode
        where a.compName in ('{}') and a.dataStatus<3 and geo.dataStatus<3
        """
        ind_data = dict()
        for ind in self.connect_135(ind_sqlexpression.format("','".join(name))):
            ind_data.update({ind['公司名称']: ind})

        for dic in data:
            nae = dic.get('公司名称')
            ind_geo = ind_data.get(nae)
            dic.update(ind_geo)

        if data:
            data.sort(key=lambda x: (x['启动辅导日期']), reverse=True)
            for i, item in enumerate(data, start=1):
                item['序号'] = i
            return data
        return []

    def ipo_shgh(self, startDate, endDate):
        # sqlexpression9 = """select statDate '最新状态日期' ,ipo.compName '公司名称',const2.constValueDesc '上市板块' , const.constValueDesc'所属行业',CONCAT(geo.provinceName,'-',geo.cityName,'-',geo.district) '地域',ipo.reguInstitution '证监局',ipo.brokName '保荐机构'from (
        #     SELECT a.* FROM `sy_cd_me_trad_ipo_base` a,
        #     (SELECT projId FROM `sy_cd_me_trad_ipo_detail` WHERE  statDate >="{}"  AND statDate <="{}"  AND dataStatus<3 AND stat in (209,212) ) b
        #     WHERE  a.projId =b.projId AND a.dataStatus<3
        #     ) ipo
        #     left join sy_cd_ms_base_gs_comp_info_new c on ipo.compName = c.compName
        #     left join sy_cd_ms_base_comp_geo_new geo on c.compCode = geo.compCode
        #     left join sy_cd_mt_sys_const const on const.cValue = c.industryId
        #     left join (select * from sy_cd_mt_sys_const where constCode = 13 and dataStatus<3) const2 on const2.constValue = ipo.market
        #     where c.dataStatus<3 and geo.dataStatus<3 and const.dataStatus<3 and const.constCode = 5
        #     and ipo.currStat in (209,212) order by statDate desc; """
        # data = self.connect_135(sqlexpression.format(startDate, endDate))
        sqlexpression = """
        SELECT a.statDate '最新状态日期',a.compName '公司名称',a.reguInstitution '证监局', a.brokName '保荐机构',
        CASE 
            WHEN a.market=101 THEN '上交所主板'
            WHEN a.market=201 THEN '深交所主板'
            WHEN a.market=301 THEN '创业板'
            WHEN a.market=302 THEN '创业板'
            WHEN a.market=401 THEN '科创板'
            WHEN a.market=501 THEN '北交所'
            END AS '上市板块'

            FROM `dws_me_trad_ipo_base` a,
            (SELECT * FROM `dws_me_trad_ipo_detail` WHERE   dataStatus<3 AND stat in (209,212) 
            and  statDate >="{}" AND  statDate <="{}") b 
            WHERE  a.projId =b.projId AND a.dataStatus<3
        """
        data = self.connect_136(sqlexpression.format(startDate, endDate))
        name = [itm['公司名称'] for itm in data]
        ind_sqlexpression = """
        select a.compName '公司名称',const.constValueDesc'所属行业' ,CONCAT(geo.provinceName,'-',geo.cityName,'-',geo.district) '地域'
        from sy_cd_ms_base_gs_comp_info_new as a 
        left join (select * from sy_cd_mt_sys_const where constCode = 5 and dataStatus<3) const on const.cValue = a.industryId
        left join sy_cd_ms_base_comp_geo_new geo on a.compCode = geo.compCode
        where a.compName in ('{}') and a.dataStatus<3 and geo.dataStatus<3
        """
        ind_data = dict()
        for ind in self.connect_135(ind_sqlexpression.format("','".join(name))):
            ind_data.update({ind['公司名称']: ind})

        for dic in data:
            nae = dic.get('公司名称')
            ind_geo = ind_data.get(nae)
            if not ind_geo:
                dic.update({"公司名称": nae, "所属行业": "--", "地域": "--"})
            else:
                dic.update(ind_geo)
        if data:
            data.sort(key=lambda x: (x['最新状态日期']), reverse=True)
            for i, item in enumerate(data, start=1):
                item['序号'] = i
            return data
        return []

    def ipo_bf(self, startDate, endDate):
        sqlexpression = """
        SELECT a.statDate '最新状态日期',a.compName '公司名称',a.reguInstitution '证监局', a.brokName '保荐机构',
        CASE 
            WHEN a.market=101 THEN '上交所主板'
            WHEN a.market=201 THEN '深交所主板'
            WHEN a.market=301 THEN '创业板'
            WHEN a.market=302 THEN '创业板'
            WHEN a.market=401 THEN '科创板'
            WHEN a.market=501 THEN '北交所'
            END AS '上市板块',
            CASE
            when a.currStat=101	then '启动上市辅导'
            when a.currStat=102	then '完成辅导验收'
            when a.currStat=103	then '终止上市辅导'
            when a.currStat=201	then '已受理'
            when a.currStat=202	then '已问询'
            when a.currStat=203	then '已反馈'
            when a.currStat=204	then '预披露'
            when a.currStat=205	then '更新预披露'
            when a.currStat=206	then '中止'
            when a.currStat=207	then '终止（审核不通过）'
            when a.currStat=208	then '终止（撤回）'
            when a.currStat=209	then '发审会通过'
            when a.currStat=210	then '发审会未通过'
            when a.currStat=211	then '暂缓表决'
            when a.currStat=212	then '上市委会议通过'
            when a.currStat=213	then '上市委会议未通过'
            when a.currStat=214	then '暂缓审议'
            when a.currStat=215	then '复审委会议通过'
            when a.currStat=216	then '复审委会议未通过'
            when a.currStat=217	then '提交注册'
            when a.currStat=218	then '注册生效'
            when a.currStat=219	then '不予注册'
            when a.currStat=220	then '终止注册'
            when a.currStat=221	then '证监会核准'
            when a.currStat=222	then '证监会不予核准'
            when a.currStat=223	then '补充审核'
            when a.currStat=301	then '已发行上市'
            when a.currStat=302	then '发行失败'
            when a.currStat=303	then '发行暂缓'
            when a.currStat=224	then '报送证监会'
            when a.currStat=225	then '挂牌委会议通过'
            when a.currStat=226	then '挂牌委会议未通过'
            when a.currStat=227	then '证监会受理'
            when a.currStat=228	then '核准'
            when a.currStat=229	then '不予核准'
            when a.currStat=230	then '终止核准'
            END AS '最新状态'
            FROM `dws_me_trad_ipo_base` a,
            (SELECT * FROM `dws_me_trad_ipo_detail` WHERE   dataStatus<3 AND stat in (206,207,208,210,211,213,214,216) 
            and  statDate >="{}" AND  statDate <="{}") b 
            WHERE  a.projId =b.projId AND a.dataStatus<3
        """
        data = self.connect_136(sqlexpression.format(startDate, endDate))
        name = [itm['公司名称'] for itm in data]
        ind_sqlexpression = """
        select a.compName '公司名称',const.constValueDesc'所属行业' ,CONCAT(geo.provinceName,'-',geo.cityName,'-',geo.district) '地域'
        from sy_cd_ms_base_gs_comp_info_new as a 
        left join (select * from sy_cd_mt_sys_const where constCode = 5 and dataStatus<3) const on const.cValue = a.industryId
        left join sy_cd_ms_base_comp_geo_new geo on a.compCode = geo.compCode
        where a.compName in ('{}') and a.dataStatus<3 and geo.dataStatus<3
        """
        ind_data = dict()
        for ind in self.connect_135(ind_sqlexpression.format("','".join(name))):
            ind_data.update({ind['公司名称']: ind})

        for dic in data:
            nae = dic.get('公司名称')
            ind_geo = ind_data.get(nae)
            dic.update(ind_geo)
        # if not data:
        #     return []
        data.sort(key=lambda x: (x['最新状态日期']), reverse=True)
        for i, item in enumerate(data, start=1):
            item['序号'] = i
        hz_ret = """上周 审核受阻 的企业 共{}家(""".format(len(data))
        zz_list, qt_list = list(), list()
        data.sort(key=lambda _: _.get("最新状态"))
        for i, xx in itertools.groupby(data, lambda i: i.get("最新状态")):
            it_list = list(xx)
            if i in ('终止（审核不通过）', '终止（撤回）'):
                zz_list.append(len(it_list))
            else:
                pj = i + str(len(it_list)) + '家'
                qt_list.append(pj)
        if zz_list:
            print(zz_list)
            qt_list.append('终止'+str(sum(zz_list)) + '家')
        pj_str = ','.join(qt_list)
        description = hz_ret + pj_str + ')'
        for item in data:
            st = item['最新状态']
            ipo_date = item['最新状态日期']
            item['最新状态\n最新状态日期'] = st + '\n' + str(ipo_date)
            del item['最新状态']
            del item['最新状态日期']
        data.sort(key=lambda x: (x['序号']), reverse=False)
        print(1111)
        print(description)
        print(data)
        return data, description

    def excel_css(self, startDate, endDate, period,  data, data2, data3, data4, data5, description, tpe):
        wb = Workbook()
        ws = wb.active
        ws.title = "一周拟IPO事件回顾"
        # wb.create_sheet(title='一周拟IPO事件回顾')

        # thick 厚的
        # thin 薄的
        # 预设单元格边框样式（style=""  边框样式（粗细、单双线等）；color=colors.XXX，边框颜色，xxx是颜色名称）
        border_set = Border(left=Side(style='thin', color=colors.BLACK),  # 左边框
                            right=Side(style='thin', color=colors.BLACK),  # 右边框
                            top=Side(style='thin', color=colors.BLACK),  # 上边框
                            bottom=Side(style='thin', color=colors.BLACK))  # 下边框

        # 需要合并的左上方和右下方单元格坐标
        ws.merge_cells(range_string='B1:J1')
        # ws.merge_cells(start_row=5, start_column=4, end_row=8, end_column=8)
        startTime = datetime.datetime.strptime(startDate, "%Y-%m-%d")
        endTime = datetime.datetime.strptime(endDate, "%Y-%m-%d")
        ws['B1'] = "近一周商机/IPO资讯({}月{}日~{}月{}日)".format(startTime.month, startTime.day, endTime.month, endTime.day)

        # 单元格取消合并使用unmerge_cells
        # ws.unmerge_cells(range_string='A1:B3')
        # horizontal = 'left',  # 水平对齐，可选general、left、center、right、fill、justify、centerContinuous、distributed
        # vertical = 'top',  # 垂直对齐， 可选top、center、bottom、justify、distributed
        # underline = None,  # 下划线, 可选'singleAccounting', 'double', 'single', 'doubleAccounting
        # text_rotation = 0,  # 字体旋转，0~180整数
        # wrap_text = False,  # 是否自动换行
        # shrink_to_fit = False,  # 是否缩小字体填充
        # indent = 0,  # 缩进值
        # ws.unmerge_cells(start_row=5, start_column=4, end_row=8, end_column=8)
        left_center = Alignment(horizontal='left', vertical='center', wrapText=True)
        alignment_center = Alignment(horizontal='center', vertical='center', wrapText=True)
        ws['B1'].alignment = alignment_center
        ws['B1'].font = Font(name='微软雅黑', size=16, italic=False, color=colors.BLACK, bold=True)

        ws.merge_cells(range_string='B2:J2')
        ws['B2'] = "总第{}期".format(period)
        ws['B2'].alignment = alignment_center
        ws['B2'].font = Font(name='微软雅黑', size=11, italic=False, color=colors.BLACK, bold=True)

        ws.merge_cells(range_string='B4:C4')
        ws['B4'].hyperlink = "https://ibds.mszq.com/index.html?page=oSABusiness"
        ws['B4'] = "一、商机摘选"
        # 00FF6600    00FF0000
        ws['B4'].font = Font(name='微软雅黑', size=14, italic=False, color='00FF6600', bold=True)

        ws.merge_cells(range_string='B6:D6')
        ws['B6'].hyperlink = "https://ibds.mszq.com/index.html?page=oSABusiness"
        ws['B6'] = "更多维度商机 >>"
        ws['B6'].alignment = left_center
        ws['B6'].font = Font(name='微软雅黑', size=11, italic=False, color='00FF6600', bold=True)

        sj_title = ['序号', '生效日期', '商机主体', '融资轮次', '所属行业', '地域', '注册资本(万元)', '员工人数']
        for i in ws["B7:I7"]:
            for j in i:
                j.alignment = alignment_center
                j.font = Font(name='微软雅黑', size=11, italic=False, color=colors.WHITE, bold=True)
                j.fill = PatternFill(patternType='solid', fgColor='00FF6600')
                j.border = border_set  # 将制定单元格的边框设置为前面预设的格式
                cell_index = i.index(j)
                j.value = sj_title[cell_index]

        for i in ws["B8:I8"]:
            for j in i:
                j.border = border_set
        ws.merge_cells(range_string='B8:I8')
        ws['B8'].hyperlink = "https://ibds.mszq.com/index.html?page=oSABusiness"
        ws['B8'] = "最新宣布一轮新的私募融资。更多>>"
        ws['B8'].alignment = left_center
        ws['B8'].font = Font(name='微软雅黑', size=11, italic=False, color=colors.BLACK, bold=True)
        ws['B8'].fill = PatternFill(patternType='solid', fgColor='FFDA65')
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        # 商机融资测试数据
        # data = [
        #     {'序号': 1, '生效日期': '2022/8/26', '商机主体': '东莞健力口腔医院', '融资轮次': '战略投资轮', '地域': None, '注册资本(万元)': 1435,
        #      '员工人数': 156,
        #      '所属行业': '医院'},
        #     {'序号': 2, '生效日期': '2022/8/25', '商机主体': '浙江数新网络有限公司', '融资轮次': 'Pre-A轮', '地域': '江苏省-无锡市-锡山区',
        #      '注册资本(万元)': 2000,
        #      '员工人数': 70, '所属行业': '软件开发'},
        #     {'序号': 3, '生效日期': '2022/8/23', '商机主体': '无锡海古德新技术有限公司', '融资轮次': '战略投资轮', '地域': '浙江省-杭州市-西湖区',
        #      '注册资本(万元)': 6129,
        #      '员工人数': 87, '所属行业': '工程和技术研究和试验发展'},
        #     {'序号': 4, '生效日期': '2022/8/22', '商机主体': '杭州片段网络科技有限公司', '融资轮次': 'A轮', '地域': '浙江省-杭州市-余杭区', '注册资本(万元)': 1000,
        #      '员工人数': 88, '所属行业': '软件开发'},
        #     {'序号': 5, '生效日期': '2022/8/22', '商机主体': '上海威乐汽车空调器有限公司', '融资轮次': None, '地域': '上海市-上海市-松江区', '注册资本(万元)': 8000,
        #      '员工人数': 96, '所属行业': '汽车零部件及配件制造'},
        # ]

        # 填充商机摘选数据

        sjrz_ys = {'序号': 'B', '生效日期': 'C', '商机主体': 'D', '融资轮次': 'E', '所属行业': 'F',
                   '地域': 'G', '注册资本(万元)': 'H', '员工人数': 'I'
                   }
        # 行数列表，商机从excel第九行开始写入
        row_list = [x + ws.max_row + 1 for x in range(0, len(data))]
        for i, item in enumerate(data):
            for k, v in item.items():
                rw = sjrz_ys.get(k)
                cerw = rw + str(row_list[i])
                if rw == 'D':
                    ws[
                        cerw].hyperlink = "https://ibds.mszq.com/index.html?page=routeStockDetail&company_name=" + urllib.parse.quote(
                        v)
                    ws[cerw].font = Font(name='微软雅黑', size=11, italic=False, color='00FF6600', bold=True)
                ws[cerw].border = border_set
                ws[cerw].alignment = alignment_center
                ws[cerw] = v
        sj_row1 = ws.max_row + 1
        for i in ws["B{}:I{}".format(sj_row1, sj_row1)]:
            for j in i:
                j.alignment = alignment_center
                j.font = Font(name='微软雅黑', size=11, italic=False, color=colors.BLACK, bold=False)
                j.border = border_set  # 将制定单元格的边框设置为前面预设的格式
                j.value = '···'

        sj_row2 = ws.max_row + 1
        for i in ws["B{}:I{}".format(sj_row2, sj_row2)]:
            for j in i:
                j.border = border_set
        ws.merge_cells(range_string='B{}:I{}'.format(sj_row2, sj_row2))
        ws['B{}'.format(sj_row2)].hyperlink = "https://ibds.mszq.com/index.html?page=oSABusiness"
        ws['B{}'.format(sj_row2)] = "更多维度商机 >>"
        ws['B{}'.format(sj_row2)].font = Font(name='微软雅黑', size=11, italic=False, color='00FF6600', bold=True)
        # ws['B{}'.format(sj_row2)].border = border_set
        ws['B{}'.format(sj_row2)].alignment = alignment_center
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        # 商机新药研发测试数据
        # data2 = [
        #     {'序号': 1, '生效日期': '2022/8/24', '商机主体': '正大天晴药业集团南京顺欣制药有限公司', '所属行业': '卫生材料及医药用品制造', '地域': '江苏省-南京市-江宁区',
        #      '注册资本(万元)': 50000, '员工人数': 658},
        #     {'序号': 2, '生效日期': '2022/8/24', '商机主体': '北京赛林泰医药技术有限公司', '所属行业': '其他科技推广服务业', '地域': '北京市-北京市-海淀区',
        #      '注册资本(万元)': 3000,
        #      '员工人数': 87},
        #     {'序号': 3, '生效日期': '2022/8/24', '商机主体': '浙江金立源药业有限公司', '所属行业': '化学药品制剂制造', '地域': '浙江省-绍兴市-上虞区',
        #      '注册资本(万元)': 6820,
        #      '员工人数': 218},
        #     {'序号': 4, '生效日期': '2022/8/23', '商机主体': '山东京卫制药有限公司', '所属行业': '卫生材料及医药用品制造', '地域': '山东省-泰安市-岱岳区',
        #      '注册资本(万元)': 10000,
        #      '员工人数': 855},
        #     {'序号': 5, '生效日期': '2022/8/23', '商机主体': '江苏海岸药业有限公司', '所属行业': '医学研究和试验发展', '地域': '江苏省-苏州市-吴江区',
        #      '注册资本(万元)': 15000,
        #      '员工人数': 245},
        # ]

        xy_row1 = ws.max_row + 2
        sjxy_title = ['序号', '生效日期', '商机主体', '所属行业', '地域', '注册资本(万元)', '员工人数']
        for i in ws["B{}:H{}".format(xy_row1, xy_row1)]:
            for j in i:
                j.alignment = alignment_center
                j.font = Font(name='微软雅黑', size=11, italic=False, color=colors.WHITE, bold=True)
                j.fill = PatternFill(patternType='solid', fgColor='00FF6600')
                j.border = border_set  # 将制定单元格的边框设置为前面预设的格式
                cell_index = i.index(j)
                j.value = sjxy_title[cell_index]
        xy_row2 = ws.max_row + 1
        for i in ws["B{}:H{}".format(xy_row2, xy_row2)]:
            for j in i:
                j.border = border_set
        ws.merge_cells(range_string='B{}:H{}'.format(xy_row2, xy_row2))
        ws['B{}'.format(xy_row2)].hyperlink = "https://ibds.mszq.com/index.html?page=oSABusiness"
        ws['B{}'.format(xy_row2)] = "{}。 更多>>".format(tpe)
        ws['B{}'.format(xy_row2)].alignment = left_center
        ws['B{}'.format(xy_row2)].font = Font(name='微软雅黑', size=11, italic=False, color=colors.BLACK, bold=True)
        ws['B{}'.format(xy_row2)].fill = PatternFill(patternType='solid', fgColor='FFDA65')

        sjxy_ys = {'序号': 'B', '生效日期': 'C', '商机主体': 'D', '所属行业': 'E', '地域': 'F', '注册资本(万元)': 'G', '员工人数': 'H'}

        row_list = [x + ws.max_row + 1 for x in range(0, len(data2))]
        for i, item in enumerate(data2):
            for k, v in item.items():
                rw = sjxy_ys.get(k)
                cerw = rw + str(row_list[i])
                if rw == 'D':
                    ws[
                        cerw].hyperlink = "https://ibds.mszq.com/index.html?page=routeStockDetail&company_name=" + urllib.parse.quote(
                        v)
                    ws[cerw].font = Font(name='微软雅黑', size=11, italic=False, color='00FF6600', bold=True)
                ws[cerw].border = border_set
                ws[cerw].alignment = alignment_center
                ws[cerw] = v

        sjxy_row2 = ws.max_row + 1
        for i in ws["B{}:H{}".format(sjxy_row2, sjxy_row2)]:
            for j in i:
                j.alignment = alignment_center
                j.font = Font(name='微软雅黑', size=11, italic=False, color=colors.BLACK, bold=False)
                j.border = border_set  # 将制定单元格的边框设置为前面预设的格式
                j.value = '···'

        sjxy_row3 = ws.max_row + 1
        for i in ws["B{}:H{}".format(sjxy_row3, sjxy_row3)]:
            for j in i:
                j.border = border_set
        ws.merge_cells(range_string='B{}:H{}'.format(sjxy_row3, sjxy_row3))
        ws['B{}'.format(sjxy_row3)].hyperlink = "https://ibds.mszq.com/index.html?page=oSABusiness"
        ws['B{}'.format(sjxy_row3)] = "更多维度商机 >>"
        ws['B{}'.format(sjxy_row3)].font = Font(name='微软雅黑', size=11, italic=False, color='00FF6600', bold=True)
        # ws['B{}'.format(sj_row2)].border = border_set
        ws['B{}'.format(sjxy_row3)].alignment = alignment_center
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        # 二、IPO资讯
        ipo_news1 = ws.max_row + 2
        ws.merge_cells(range_string='B{}:C{}'.format(ipo_news1, ipo_news1))
        ws['B{}'.format(ipo_news1)] = "二、IPO资讯"
        ws['B{}'.format(ipo_news1)].hyperlink = "https://ibds.mszq.com/index.html?page=monitor_ipo"
        # 00FF6600    00FF0000
        ws['B{}'.format(ipo_news1)].font = Font(name='微软雅黑', size=14, italic=False, color='00FF0000', bold=True)

        ipo_news2 = ws.max_row + 2
        ws.merge_cells(range_string='B{}:D{}'.format(ipo_news2, ipo_news2))
        ws['B{}'.format(ipo_news2)].hyperlink = "https://ibds.mszq.com/index.html?page=monitor_ipo"
        ws['B{}'.format(ipo_news2)] = "更多IPO资讯 >>"
        ws['B{}'.format(ipo_news2)].alignment = left_center
        ws['B{}'.format(ipo_news2)].font = Font(name='微软雅黑', size=11, italic=False, color='00FF0000', bold=True)

        # 序号	启动辅导日期	公司名称	所属行业	地域	证监局	辅导机构
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        # data3 = [
        #     {'序号': 1, '启动辅导日期': '2022/8/22', '公司名称': '衣拉拉集团股份有限公司', '所属行业': '纺织、服装及家庭用品批发', '地域': '山东省-烟台市-福山区',
        #      '证监局': '山东证监局',
        #      '辅导机构': '中信证券股份有限公司', }
        # ]
        ipo_news3 = ws.max_row + 1
        ws.merge_cells(range_string='B{}:c{}'.format(ipo_news3, ipo_news3))
        ws['B{}'.format(ipo_news3)] = "上周 启动IPO辅导 的企业 共{}家".format(len(data3))
        ws['B{}'.format(ipo_news3)].font = Font(name='微软雅黑', size=11, italic=False, color=colors.BLACK, bold=True)

        ipo_news4 = ws.max_row + 1
        ipo_news_title = ['序号', '启动辅导日期', '公司名称', '所属行业', '地域', '证监局', '辅导机构']
        for i in ws["B{}:H{}".format(ipo_news4, ipo_news4)]:
            for j in i:
                j.alignment = alignment_center
                j.font = Font(name='微软雅黑', size=11, italic=False, color=colors.WHITE, bold=True)
                j.fill = PatternFill(patternType='solid', fgColor='00FF0000')
                j.border = border_set  # 将制定单元格的边框设置为前面预设的格式
                cell_index = i.index(j)
                j.value = ipo_news_title[cell_index]
        if not data3:
            iponews9 = ws.max_row + 1
            ws.merge_cells(range_string='B{}:H{}'.format(iponews9, iponews9))
            ws['B{}'.format(iponews9)] = "暂无数据"
            for i in ws["B{}:H{}".format(iponews9, iponews9)]:
                for j in i:
                    j.border = border_set
            ws['B{}'.format(iponews9)].alignment = alignment_center
        else:
            ipo_news_ys1 = {'序号': 'B', '启动辅导日期': 'C', '公司名称': 'D', '所属行业': 'E', '地域': 'F', '证监局': 'G', '辅导机构': 'H'}
            row_list = [x + ws.max_row + 1 for x in range(0, len(data3))]
            for i, item in enumerate(data3):
                for k, v in item.items():
                    rw = ipo_news_ys1.get(k)
                    cerw = rw + str(row_list[i])
                    if rw == 'D':
                        ws[
                            cerw].hyperlink = "https://ibds.mszq.com/index.html?page=routeStockDetail&company_name=" + urllib.parse.quote(
                            v)
                        ws[cerw].font = Font(name='微软雅黑', size=11, italic=False, color='00FF0000', bold=True)
                    ws[cerw].border = border_set
                    ws[cerw].alignment = alignment_center
                    ws[cerw] = v

        ipo_news5 = ws.max_row + 1
        for i in ws["B{}:H{}".format(ipo_news5, ipo_news5)]:
            for j in i:
                j.border = border_set
        ws.merge_cells(range_string='B{}:H{}'.format(ipo_news5, ipo_news5))
        ws['B{}'.format(ipo_news5)].hyperlink = "https://ibds.mszq.com/index.html?page=monitor_ipo"
        ws['B{}'.format(ipo_news5)] = "更多IPO资讯 >>"
        ws['B{}'.format(ipo_news5)].font = Font(name='微软雅黑', size=11, italic=False, color='00FF0000', bold=True)
        ws['B{}'.format(ipo_news5)].alignment = alignment_center
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        # 序号	最新状态日期	公司名称	上市板块	所属行业	地域	证监局	保荐机构
        # data4 = [
        #     {'序号': 1, '最新状态日期': '2022/8/26', '公司名称': '湖北省宏源药业科技股份有限公司', '上市板块': '创业板', '所属行业': '专用化学产品制造',
        #      '地域': '湖北省-黄冈市-罗田县', '证监局': '湖北证监局', '保荐机构': '民生证券股份有限公司'},
        #     {'序号': 2, '最新状态日期': '2022/8/26', '公司名称': '深圳市景创科技电子股份有限公司', '上市板块': '创业板', '所属行业': '其他电子设备制造',
        #      '地域': '广东省-深圳市-宝安区', '证监局': '深圳证监局', '保荐机构': '中信证券股份有限公司'},
        #     {'序号': 3, '最新状态日期': '2022/8/26', '公司名称': '浙江夜光明光电科技股份有限公司', '上市板块': '北交所', '所属行业': '机织服装制造',
        #      '地域': '浙江省-台州市-椒江区', '证监局': None, '保荐机构': '中泰证券股份有限公司'},
        # ]
        ipo_news6 = ws.max_row + 1
        ws.merge_cells(range_string='B{}:D{}'.format(ipo_news6, ipo_news6))
        ws['B{}'.format(ipo_news6)] = "上周 审核过会 的企业  共{}家".format(len(data4))
        ws['B{}'.format(ipo_news6)].font = Font(name='微软雅黑', size=11, italic=False, color=colors.BLACK, bold=True)

        ipo_news7 = ws.max_row + 1
        ipo_news_title = ['序号', '最新状态日期', '公司名称', '上市板块', '所属行业', '地域', '证监局', '保荐机构']
        for i in ws["B{}:I{}".format(ipo_news7, ipo_news7)]:
            for j in i:
                j.alignment = alignment_center
                j.font = Font(name='微软雅黑', size=11, italic=False, color=colors.WHITE, bold=True)
                j.fill = PatternFill(patternType='solid', fgColor='00FF0000')
                j.border = border_set  # 将制定单元格的边框设置为前面预设的格式
                cell_index = i.index(j)
                j.value = ipo_news_title[cell_index]
        if not data4:
            iponews8 = ws.max_row + 1
            ws.merge_cells(range_string='B{}:I{}'.format(iponews8, iponews8))
            ws['B{}'.format(iponews8)] = "暂无数据"
            for i in ws["B{}:I{}".format(iponews8, iponews8)]:
                for j in i:
                    j.border = border_set
            ws['B{}'.format(iponews8)].alignment = alignment_center
        else:
            ipo_news_ys2 = {'序号': 'B', '最新状态日期': 'C', '公司名称': 'D', '上市板块': 'E', '所属行业': 'F', '地域': 'G', '证监局': 'H',
                            '保荐机构': 'I'}
            row_list = [x + ws.max_row + 1 for x in range(0, len(data4))]
            for i, item in enumerate(data4):
                for k, v in item.items():
                    rw = ipo_news_ys2.get(k)
                    cerw = rw + str(row_list[i])
                    if rw == 'D':
                        ws[
                            cerw].hyperlink = "https://ibds.mszq.com/index.html?page=routeStockDetail&company_name=" + urllib.parse.quote(
                            v)
                        ws[cerw].font = Font(name='微软雅黑', size=11, italic=False, color='00FF0000', bold=True)
                    ws[cerw].border = border_set
                    ws[cerw].alignment = alignment_center
                    ws[cerw] = v

        ipo_news8 = ws.max_row + 1
        for i in ws["B{}:I{}".format(ipo_news8, ipo_news8)]:
            for j in i:
                j.border = border_set
        ws.merge_cells(range_string='B{}:I{}'.format(ipo_news8, ipo_news8))
        ws['B{}'.format(ipo_news8)].hyperlink = "https://ibds.mszq.com/index.html?page=monitor_ipo"
        ws['B{}'.format(ipo_news8)] = "更多IPO资讯 >>"
        ws['B{}'.format(ipo_news8)].font = Font(name='微软雅黑', size=11, italic=False, color='00FF0000', bold=True)
        ws['B{}'.format(ipo_news8)].alignment = alignment_center
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        # 序号	"最新状态\n最新状态日期"	公司名称	上市板块	所属行业	地域	证监局	保荐机构

        # data5 = [
        #     {'序号': 1, '最新状态\n最新状态日期': '终止（审核不通过）\n2022/8/26', '公司名称': '江苏伟康洁婧医疗器械股份有限公司', '上市板块': '创业板',
        #      '所属行业': '卫生材料及医药用品制造'
        #         , '地域': '江苏省-宿迁市-沭阳县', '证监局': '江苏证监局', '保荐机构': '东吴证券股份有限公司'},
        #     {'序号': 2, '最新状态\n最新状态日期': '终止（审核不通过）\n2022/8/26', '公司名称': '宁波恒普真空科技股份有限公司', '上市板块': '科创板',
        #      '所属行业': '工程和技术研究和试验发展'
        #         , '地域': '浙江省-宁波市-慈溪市', '证监局': '宁波证监局', '保荐机构': '方正证券承销保荐有限责任公司'},
        # ]

        ipo_news9 = ws.max_row + 1
        ws.merge_cells(range_string='B{}:D{}'.format(ipo_news9, ipo_news9))
        ws['B{}'.format(ipo_news9)] = description
        ws['B{}'.format(ipo_news9)].font = Font(name='微软雅黑', size=11, italic=False, color=colors.BLACK, bold=True)

        ipo_news10 = ws.max_row + 1
        ipo_news10_title = ['序号', '最新状态\n最新状态日期', '公司名称', '上市板块', '所属行业', '地域', '证监局', '保荐机构']
        for i in ws["B{}:I{}".format(ipo_news10, ipo_news10)]:
            for j in i:
                j.alignment = alignment_center
                j.font = Font(name='微软雅黑', size=11, italic=False, color=colors.WHITE, bold=True)
                j.fill = PatternFill(patternType='solid', fgColor='00FF0000')
                j.border = border_set  # 将制定单元格的边框设置为前面预设的格式
                cell_index = i.index(j)
                j.value = ipo_news10_title[cell_index]
        if not data5:
            iponews7 = ws.max_row + 1
            ws.merge_cells(range_string='B{}:I{}'.format(iponews7, iponews7))
            ws['B{}'.format(iponews7)] = "暂无数据"
            for i in ws["B{}:I{}".format(iponews7, iponews7)]:
                for j in i:
                    j.border = border_set
            ws['B{}'.format(iponews7)].alignment = alignment_center
        else:
            ipo_news_ys3 = {'序号': 'B', '最新状态\n最新状态日期': 'C', '公司名称': 'D', '上市板块': 'E', '所属行业': 'F', '地域': 'G',
                            '证监局': 'H',
                            '保荐机构': 'I'}
            row_list = [x + ws.max_row + 1 for x in range(0, len(data5))]
            print(row_list)
            for i, item in enumerate(data5):
                for k, v in item.items():
                    rw = ipo_news_ys3.get(k)
                    cerw = rw + str(row_list[i])
                    if rw == 'D':
                        ws[
                            cerw].hyperlink = "https://ibds.mszq.com/index.html?page=routeStockDetail&company_name=" + urllib.parse.quote(
                            v)
                        ws[cerw].font = Font(name='微软雅黑', size=11, italic=False, color='00FF0000', bold=True)
                    ws[cerw].border = border_set
                    ws[cerw].alignment = alignment_center
                    ws[cerw] = v

        ipo_news11 = ws.max_row + 1
        for i in ws["B{}:I{}".format(ipo_news11, ipo_news11)]:
            for j in i:
                j.border = border_set
        ws.merge_cells(range_string='B{}:I{}'.format(ipo_news11, ipo_news11))
        ws['B{}'.format(ipo_news11)].hyperlink = "https://ibds.mszq.com/index.html?page=monitor_ipo"
        ws['B{}'.format(ipo_news11)] = "更多IPO资讯 >>"
        ws['B{}'.format(ipo_news11)].font = Font(name='微软雅黑', size=11, italic=False, color='00FF0000', bold=True)
        ws['B{}'.format(ipo_news11)].alignment = alignment_center
        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        end1 = ws.max_row + 2
        ws.merge_cells(range_string='B{}:H{}'.format(end1, end1))
        ws['B{}'.format(end1)] = "注：审核受阻项目将收集处于中止、终止（审核不通过）、 终止（撤回）、 发审会未通过、暂缓表决、 上市委会议未通过、暂缓审议、 复审委会议未通过 的项目"
        ws['B{}'.format(end1)].alignment = left_center
        ws['B{}'.format(end1)].font = Font(name='微软雅黑', size=11, italic=False, color=colors.BLACK, bold=False)

        end_time = ws.max_row + 1
        d1 = datetime.date.today()
        ws['I{}'.format(end_time)] = "日期:{}年{}月{}日".format(d1.year, d1.month, d1.day)
        ws['I{}'.format(end_time)].font = Font(name='微软雅黑', size=11, italic=False, color=colors.BLACK, bold=True)
        alignment_center_bott = Alignment(horizontal='center', vertical='bottom', wrapText=True)
        ws['I{}'.format(end_time)].alignment = alignment_center

        # ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
        # 设置一个字典用于保存列宽数据
        dims = {}
        # 遍历表格数据，获取自适应列宽数据
        for row in ws.rows:
            for cell in row:
                if cell.value:
                    # 遍历整个表格，把该列所有的单元格文本进行长度对比，找出最长的单元格
                    # 在对比单元格文本时需要将中文字符识别为1.7个长度，英文字符识别为1个，这里只需要将文本长度直接加上中文字符数量即可
                    # re.findall('([\u4e00-\u9fa5])', cell.value)能够识别大部分中文字符
                    cell_len = 0.7 * len(re.findall('([\u4e00-\u9fa5])', str(cell.value))) + len(str(cell.value))
                    dims[cell.column] = max((dims.get(cell.column, 0), cell_len))
        for col, value in dims.items():
            # 设置列宽，get_column_letter用于获取数字列号对应的字母列号，最后值+2是用来调整最终效果的
            ws.column_dimensions[get_column_letter(col)].width = value + 3

        # rows = ws.max_row
        # columns = ws.max_column
        # print(rows)
        # print(columns)

        # 设置行高
        # ws['A1']='行高被设置为 100'
        # ws.row_dimensions[1].height=100
        jjb = ws.max_row + 1
        for x in range(1, jjb):
            ws.row_dimensions[x].height = 30
        # 设置列宽
        # ws['B2']='列宽被设置为 50'

        ws.column_dimensions['A'].width = 2
        ws.column_dimensions['B'].width = 7
        tm = datetime.datetime.now().strftime("%Y%m%d")
        ex_ame = '商机简报{}.xlsx'.format(tm)
        wb.save(ex_ame)
        return ex_ame


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='商机简报')
    parser.add_argument('--startDate', type=str, default=None)
    parser.add_argument('--endDate', type=str, default=None)
    parser.add_argument('--period', type=str, default=None)
    args = parser.parse_args()
    print(args.startDate)
    print(args.endDate)
    print(args.period)
    cl = bullentin()
    cl.poss(args.startDate, args.endDate, args.period)

