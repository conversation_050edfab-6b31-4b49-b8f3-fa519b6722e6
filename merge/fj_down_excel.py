# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022-11-1
from core.excel_base import ExcelBase


class Down(ExcelBase):
    def __init__(self):
        super(Down, self).__init__()
        self.schema_db = "seeyii_db"
        self.schema_table = "sy_dwd_event_attachment_info"
        self.schema_db_key = "seeyii_db"

    def process(self, *args, **kwargs):
        url_set = self.read_excel_data(self._in_file_path + "/中信建投_未下载附件清单.xlsx", "test")
        print(f"url_set = {len(url_set)}")
        raw_data = self.query_down_data(url_set)
        print(f"raw_data = {len(raw_data)}")
        # field_cfg = self.import_data_cfg()
        # self._excel_name = self.name_add_date("{}.xlsx".format(self.schema_table))
        # self.save_to_excel(field_cfg, {"sheet1": raw_data})
        return raw_data

    def import_data_cfg(self):
        data_dict = dict()
        item_list = self.query_table_schema()
        for idx, item in enumerate(item_list):
            data_dict.setdefault(item["column_name"],
                                 (item["column_comment"] if item["column_comment"] else item["column_name"], idx))
        return data_dict

    def query_table_schema(self):
        data_list = list()
        sql = """select column_name, column_comment 
        from information_schema.columns where table_schema ='{}' 
         and table_name = '{}';"""
        query_schema = dict(db_key=self.schema_db_key, sql_statement=sql.format(self.schema_db, self.schema_table))
        result_list = self._data_server.call("query_sql_item", query_schema)
        for item in result_list:
            data_list.append(item)
        return data_list

    def query_down_data(self, write):
        num = 1
        result = list()
        sql = """SELECT * FROM sy_dwd_event_attachment_info WHERE id>'{}' order by id ASC limit 1000;"""
        for result_list in self._query_sql_iter_by_id(sql, "seeyii_db"):
            for item in result_list:
                url = item["url"]
                if url in write:
                    result.append(item)
            num += 1
            # print(f"第{num}批={len(result)}")
        return result

    def read_excel_data(self, file_name, sheet="Sheet"):
        result = set()
        data_list = self._extract_data(file_name, sheet)
        for item in data_list:
            result.add(item["eventUrl"])
        return result


if __name__ == '__main__':
    p = Down()
    p.process()
