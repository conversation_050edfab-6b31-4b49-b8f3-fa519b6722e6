# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022-10-24
from core.excel_base import ExcelBase


class Change(ExcelBase):
    def __init__(self):
        super(Change, self).__init__()
        self.data_cfg = {
            # a - b = 新增
            # b - a = 删除
            "a": {"db_key": "section_data", "sql_statement": """
            select * from dwd_mm_cn_smjj_lp_rela where id>'{}' 
            order by id ASC limit 1000"""},

            "b": {"db_key": "db_seeyii", "sql_statement": """
            select * from dwd_mm_cn_smjj_lp_rela where id>'{}' and dataStatus!=3
            order by id ASC limit 1000
            """}
        }
        self.schema_db = "section_data"
        self.schema_table = "dwd_mm_cn_smjj_lp_rela"
        self.schema_db_key = "section_data"

    def process(self, *args, **kwargs):
        temp_dict = dict()
        for k, cfg in self.data_cfg.items():
            temp_dict[k] = self.query_sql_data(cfg)
            print(f"raw_{k}={len(temp_dict[k])}")
        # a - b = 新增
        inc_finger = temp_dict["a"].keys() - temp_dict["b"].keys()
        print(f"inc={len(inc_finger)}")
        # b - a = 删除
        del_finger = temp_dict["b"].keys() - temp_dict["a"].keys()
        print(f"_del={len(del_finger)}")

        _inc = [temp_dict["a"][k] for k in inc_finger]
        _del = [temp_dict["b"][k] for k in del_finger]

        self.change_data_status(_del)
        field_cfg = self.import_data_cfg()
        self._excel_name = self.name_add_date("{}_变化数据.xlsx".format(self.schema_table))
        self.save_to_excel(field_cfg, {"sheet1": _inc + _del})

    @staticmethod
    def change_data_status(items):
        for item in items:
            item["dataStatus"] = 3

    def query_sql_data(self, cfg):
        result = dict()
        sql = cfg["sql_statement"]
        db_key = cfg["db_key"]
        for items in self._query_sql_iter_by_id(sql, db_key):
            for item in items:
                finger_id = item["fingerId"]
                result.setdefault(finger_id, item)
        return result

    def import_data_cfg(self):
        data_dict = dict()
        item_list = self.query_table_schema()
        for idx, item in enumerate(item_list):
            data_dict.setdefault(item["column_name"],
                                 (item["column_comment"] if item["column_comment"] else item["column_name"], idx))
        return data_dict

    def query_table_schema(self):
        data_list = list()
        sql = """select column_name, column_comment 
        from information_schema.columns where table_schema ='{}' 
         and table_name = '{}';"""
        query_schema = dict(db_key=self.schema_db_key, sql_statement=sql.format(self.schema_db, self.schema_table))
        result_list = self._data_server.call("query_sql_item", query_schema)
        for item in result_list:
            data_list.append(item)
        return data_list


if __name__ == '__main__':
    p = Change()
    p.process()
