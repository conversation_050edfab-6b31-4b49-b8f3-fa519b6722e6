# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/6/21
from core.excel_base import ExcelBase

"""
农行借贷
"""


class NHJD(ExcelBase):
    def __init__(self):
        super(NHJD, self).__init__()

    def process(self):
        result = list()
        area_map = dict()
        for items in self.read_excel_data(self._in_file_path + "/农行借贷白名单.xlsx"):
            area_map.setdefault(items["证券名称"], items["城市"])

        tmp_result = dict()
        names = list(area_map.keys())
        for idx in range(0, len(names), 10):
            name_list = names[idx: idx + 10]
            for raw_name in name_list:
                for y in [2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020]:
                    tmp_result.setdefault(raw_name, dict())
                    tmp_result[raw_name].setdefault(y, list())
            raw_items = self.query_mysql_data(name_list)
            for item in raw_items:
                year = item["infoPublDate"]
                name = item["chiNameAbbr"]
                tmp_result.setdefault(name, dict())
                tmp_result[name].setdefault(year, list())
                tmp_result[name][year].append(item)

        for name, year_dict in tmp_result.items():
            for year, item_list in year_dict.items():
                sum_dict = dict()
                for r_item in item_list:
                    lender = r_item["lender"]
                    if "中国银行" in lender:
                        sum_dict.setdefault("中国银行", 0.0)
                        sum_dict["中国银行"] += float(r_item["firstLoanSum"]) or float(r_item["latestLoanSum"])
                    elif "农业银行" in lender:
                        sum_dict.setdefault("农业银行", 0.0)
                        sum_dict["农业银行"] += float(r_item["firstLoanSum"]) or float(r_item["latestLoanSum"])
                    elif "工商银行" in lender:
                        sum_dict.setdefault("工商银行", 0.0)
                        sum_dict["工商银行"] += float(r_item["firstLoanSum"]) or float(r_item["latestLoanSum"])
                    elif "建设银行" in lender:
                        sum_dict.setdefault("建设银行", 0.0)
                        sum_dict["建设银行"] += float(r_item["firstLoanSum"]) or float(r_item["latestLoanSum"])
                    elif "银行" in lender:
                        sum_dict.setdefault("银行", 0.0)
                        sum_dict["银行"] += float(r_item["firstLoanSum"]) or float(r_item["latestLoanSum"])
                    else:
                        print(name, lender)
                new_item = dict()
                new_item['name'] = name
                new_item['year'] = year
                new_item['area'] = area_map[name]
                for i in ["中国银行", "农业银行", "工商银行", "建设银行", "银行"]:
                    new_item[i] = sum_dict.get(i, 0)
                result.append(new_item)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'year': ('年份', 0),
            'name': ('证券名称', 1),
            'area': ('城市', 2),
            '中国银行': ('中国银行', 3),
            '农业银行': ('农业银行', 4),
            '工商银行': ('工商银行', 5),
            '建设银行': ('建设银行', 6),
            '银行': ('其他银行', 7)}
        self._excel_name = self.name_add_date("农行POC.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def query_mysql_data(self, names):
        name_str = "','".join(names)
        sql_statement = """SELECT chiNameAbbr, lender, firstLoanSum, latestLoanSum, 
                            YEAR(infoPublDate) as infoPublDate from 
                        sy_cd_ms_base_secumain as a 
                        RIGHT JOIN 
                        sy_cd_me_trad_sk_credit as b 
                        on 
                        a.companyCode=b.companyCode 
                        and b.borrowerAssociation=1 and a.secuCategory=1
                        and b.lenderAssociation=999 and a.dataStatus!=3 and b.dataStatus!=3
                        where b.infoPublDate BETWEEN '2010-01-01' and '2020-12-31' and chiNameAbbr in ('{}');"""
        query_schema = dict(db_key="tidb_152", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list

    def read_excel_data(self, file_name, sheet="Sheet1"):
        data_list = self._extract_data(file_name, sheet)
        return data_list


if __name__ == '__main__':
    p = NHJD()
    p.process()
