# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022-8-30
from collections import defaultdict

from core.excel_base import ExcelBase


class ExcelToMongo(ExcelBase):
    def __init__(self):
        super(ExcelToMongo, self).__init__()

    def process(self):
        px_name_map = self.reader_map_data()
        # old_dict = self.query_mongo_data()
        excel_list = self.reader_execl_data()
        excel_dict = defaultdict(list)
        for item in excel_list:
            tmp_dict = {"investor": item["investor"]}
            spectrum_name = item["spectrum_name"]
            top_entity = item.get("top_entity")
            if top_entity:
                tmp_dict["top_entity"] = top_entity
            excel_dict[spectrum_name].append(tmp_dict)
        result_list = list()
        for spectrum_name, v in excel_dict.items():
            tmp_dict = dict()
            tmp_dict["investor_list"] = v
            tmp_dict["spectrum_name"] = spectrum_name
            old_data = px_name_map.get(spectrum_name)
            # old_data = old_dict.get(old_px_name)
            if old_data:
                feature_id = old_data["feature_id"]
                tmp_dict["feature_id"] = feature_id
                alias_name = old_data.get("alias_name")
                if alias_name:
                    tmp_dict["alias_name"] = alias_name
            else:
                print("此谱系不在历史谱系中={}".format(spectrum_name))
                # tmp_dict["feature_id"] = self._hash(spectrum_name)
            result_list.append(tmp_dict)
        print("谱系的数量={}".format(len(result_list)))
        self.save_to_mongo(result_list)

    def query_mongo_data(self):
        result = dict()
        query_schema = {
            "db_name": "raw_data",
            "collection_name": "investor_spectrum_info",
            "query_condition": {},
            "query_field": {"_id": 0}}
        query_result = self._data_server.call("query_item", query_schema)
        for item in query_result:
            spectrum_name = item["spectrum_name"]
            result.setdefault(spectrum_name, item)
        return result

    def reader_execl_data(self):
        filename = self._in_file_path + '/新版谱系白名单.xlsx'
        data_list = self._extract_data(filename, "新版谱系白名单")
        return data_list

    def reader_map_data(self):
        # result = defaultdict()
        # filename = self._in_file_path + '/新旧谱系关系映射表_全_20220923.xlsx'
        # data_list = self._extract_data(filename, "20220923")
        # for item in data_list:
        #     new_px_name = item.get("新版谱系名称")
        #     old_px_name = item.get("旧版谱系名称")
        #     if new_px_name and old_px_name:
        #         result[new_px_name] = old_px_name
        # return result
        result = dict()
        query_schema = {
            "db_name": "raw_data",
            "collection_name": "investor_spectrum_info_v2_bak",
            "query_condition": {},
            "query_field": {"_id": 0}}
        query_result = self._data_server.call("query_item", query_schema)
        for item in query_result:
            spectrum_name = item["spectrum_name"]
            # feature_id = item["feature_id"]
            result.setdefault(spectrum_name, item)
        return result

    def save_to_mongo(self, data_list):
        if not data_list:
            return
        insert_schema = {
            "db_name": "raw_data",
            "collection_name": "investor_spectrum_info_v2",
            "data_list": data_list}
        self._data_server.call("batch_insert_item_v2", insert_schema)


if __name__ == '__main__':
    p = ExcelToMongo()
    p.process()
