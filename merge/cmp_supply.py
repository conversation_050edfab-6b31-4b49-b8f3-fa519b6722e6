# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/05/10
import operator
from functools import reduce
from core.excel_base import ExcelBase
from moduler.company_supply_data import CompanySupply


class CMPSupply(ExcelBase):
    def __init__(self):
        super(CMPSupply, self).__init__()
        self.supply = CompanySupply()

    def process(self, *args, **kwargs):
        result = list()
        names = ['宏发科技股份有限公司',
                 '成都燃气集团股份有限公司',
                 '湖北宜昌交运集团股份有限公司',
                 '凯龙高科技股份有限公司',
                 '湖北京山轻工机械股份有限公司',
                 '广州广哈通信股份有限公司',
                 '中新科技集团股份有限公司',
                 '南侨食品集团（上海）股份有限公司',
                 '快意电梯股份有限公司',
                 '中科软科技股份有限公司',
                 '重庆再升科技股份有限公司',
                 '新光圆成股份有限公司',
                 '无锡威孚高科技集团股份有限公司',
                 '张家港保税科技（集团）股份有限公司',
                 '金诚信矿业管理股份有限公司',
                 '海南航空控股股份有限公司',
                 '杭州老板电器股份有限公司',
                 '光大嘉宝股份有限公司',
                 '广东新劲刚新材料科技股份有限公司',
                 '广州迪森热能技术股份有限公司',
                 '四川安宁铁钛股份有限公司',
                 '嘉美食品包装（滁州）股份有限公司',
                 '深圳奥雅设计股份有限公司',
                 '中核苏阀科技实业股份有限公司',
                 '江苏凯伦建材股份有限公司',
                 '银亿股份有限公司',
                 '重庆三峡油漆股份有限公司',
                 '北京首商集团股份有限公司',
                 '广东乐心医疗电子股份有限公司']
        r = self.supply.run(names)
        result.extend(reduce(operator.add, list(r.values())[:101]))
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'cname': ('公司名称', 0),
            'company_name': ('供应商名称', 1),
            'relation': ('关系类型', 2),
            'mark': ('供应商排名', 3),
            'period': ('年度', 4)}
        self._excel_name = self.name_add_date("供应链POC.xlsx")
        self.save_to_excel(field_cfg, {"Sheet": result})


if __name__ == '__main__':
    p = CMPSupply()
    p.process()