# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/09
from core.excel_base import ExcelBase
from moduler.company_all_cyl_names import AllCyl
from moduler.company_area_info import CompanyArea
import json
import http.client
import traceback
from collections import defaultdict


class AreaCyl(ExcelBase):
    def __init__(self):
        super(AreaCyl, self).__init__()
        self.all_cyl = AllCyl()
        self.area = CompanyArea()

    def process(self, *args, **kwargs):
        result, valid_list, _all = list(), list(), defaultdict(list)
        name_list = self.all_cyl.run()
        for raw in name_list:
            cmp = raw["compName"]
            _all[cmp].append(raw)

        names = list(_all.keys())
        print("names ={}".format(len(names)))
        num = 0
        for idx in range(0, len(names), 100):
            name_l = names[idx: idx + 100]
            num += 100
            area_dict = self.area.run(name_l) or dict()
            for n in name_l:
                city = area_dict.get(n, dict()).get("cityName")
                if city != '泰州市':
                    continue
                valid_list.extend(_all[n])
            print(num)

        print("valid_list ={}".format(len(valid_list)))
        for item in valid_list:
            name = item["compName"]
            # 标签信息
            cat_dict = self.query_category_info(name)
            category_data = self.category_data_process(cat_dict)
            item.update(category_data)
            result.append(item)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'compName': ('公司名称', 0),
            'IndustryName': ('视野行业', 1),
            'cyl': ('所属产业链', 2),
            'company_business': ('业务亮点', 3)}
        self._excel_name = self.name_add_date("江苏银行产业链相关数据.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    @staticmethod
    def query_category_info(raw_name):
        result_list = list()
        headers = {"Content-type": "application/x-www-form-urlencoded",
                   "Accept": "text/plain"}
        httpClient = http.client.HTTPConnection("10.10.128.185", 6226, timeout=30)
        # httpClient = http.client.HTTPConnection("60.205.212.21", 6226, timeout=30)
        cookie, limit_num = None, 20
        while True:
            try:
                request_1_1 = {
                    "user_id": "edcec33c8bdf48249cfc7985d39a39a3",
                    "cname": raw_name}
                if cookie is not None:
                    request_1_1["cookie"] = cookie
                params = json.dumps(request_1_1)
                httpClient.request(
                    "POST", "/v1/companys/category_info", params, headers)
                response = httpClient.getresponse()
                result = response.read().decode("utf-8")
                result = json.loads(result)
                if result["response_state"] != 1:
                    raise Exception("request err.param={}".format(params))
                response_content = result["response_content"]
                return response_content
            except Exception as exc:
                err_msg = traceback.format_exc()
                err_msg = " ## ".join(err_msg.split("\n"))
                err_msg = " ## ".join([err_msg, str(exc)])
                print(err_msg)
                break
        if httpClient is not None:
            httpClient.close()
        return result_list

    @staticmethod
    def category_data_process(cat_dict):
        """
        :param cat_dict:
        :return:  company_nature:性质标签, market:所属市场, company_business:业务亮点
        """
        category_dict = dict()
        # 过滤掉 政府奖励 证照 起草标准 的标签
        # filter_cat = [
        #     "SYAP10001#", "SYAP10002#", "SYAP10003#", "SYAP10004#", "SYAM10000#",
        #     "SYAP10005#", "SYAQ10001#", "SYAQ10002#", "SYAQ10003#"]
        filter_cat = []
        for cat_key in ["company_nature", "market"]:
            category_dict.setdefault(cat_key, ','.join([i["name"] for i in cat_dict.get(cat_key, list())]))
        company_business = cat_dict.get("company_business", list())
        c_bus = list()
        for item in company_business:
            cat = item["category"]
            name = item["name"]
            if "SYAM10000" in cat:
                names = [i for i in name.split("-") if i != '证照']
                category_dict.setdefault("licence", list())
                category_dict["licence"].extend(names)

            if cat not in filter_cat:
                c_bus.append(name)
        category_dict["company_business"] = ','.join(c_bus)

        licence = category_dict.get("licence")
        if licence:
            category_dict["licence"] = ",".join(licence)
        return category_dict


if __name__ == '__main__':
    p = AreaCyl()
    p.run()
