# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022/5/31
import json
import networkx as nx
from core.excel_base import ExcelBase
from moduler.company_alias_name import CompanyAlias
from moduler.company_category import CompanyCategory
from moduler.company_holder import CompanyHolder
from copy import deepcopy
from collections import defaultdict
from moduler.company_a_and_xsb_holder import CompanyAandXsbShareholder
from moduler.company_invester import CompanyInvest
from pprint import pprint
from cfg.constant import reg_status_map

names = [
    '三门峡速达交通节能科技股份有限公司', '爱驰汽车有限公司', '江苏九洲投资集团有限公司', '江苏悦达集团有限公司', '天际汽车科技集团有限公司', '合众新能源汽车有限公司', '江苏金彭集团有限公司',
    '威马智慧出行科技（上海）股份有限公司', '力帆科技（集团）股份有限公司', '广州汽车工业集团有限公司', '北京汽车集团有限公司', '江苏牛创新能源科技有限公司', '保时捷汽车股份有限公司', '东风汽车集团有限公司',
    '中国第一汽车集团有限公司', '蔚来控股有限公司', '湖南恒润高科股份有限公司', '宜宾发展控股集团有限公司', '大运集团有限公司', '长城汽车股份有限公司', '武汉路特斯科技有限公司', '福特汽车(中国)有限公司',
    '上饶市腾骏科技股份有限公司', '重庆市新特长寿新能源汽车有限公司', '小米科技有限责任公司', '上海汽车工业（集团）有限公司', '比亚迪股份有限公司', '福建省汽车工业集团有限公司',
    '克莱斯勒(中国)汽车销售有限公司', '浙江大华技术股份有限公司', '比德文控股集团有限公司', '浙江吉利控股集团有限公司', '中国机械工业集团有限公司', '海马投资集团有限公司', '巴伐利亚发动机制造厂股份有限公司',
    '华晨汽车集团控股有限公司', '云度新能源汽车有限公司', '安徽江淮汽车集团控股有限公司', '北京百度网讯科技有限公司', '中国长安汽车集团有限公司', '奇瑞汽车股份有限公司', '重庆小康控股有限公司',
    '江铃汽车集团有限公司', '北京车和家信息技术有限公司', '广东小鹏汽车科技有限公司', '华人运通投资有限公司', '山东御捷马新能源汽车制造有限公司']
fund_cat = {
    "PrivateFund",
    "UnrecordedFund"
}
org_cat = {
    "PrivateFundCompany", "PublicFundCompany", "SecurityCompany", "FuturesCompany", "InvestCompanyOfBroker",
    "Inv100003", "PublicChildCompany", "DevelopmentBank", "SYAU10000", "PolicyBank", "AssetManagementCompany",
    "FinancialLeaseCompany", "LargeCommercialBank", "JointStockCommercialBank", "PrivateBank", "RuralCommercialBank",
    "ForeignBank", "CityCommercialBank", "NationalInvestmentPlatform", "InvestmentConsultCompany", "TrustCompany",
    "FinanceCompany", "InsuranceCompany", "InsuranceAssetCompany", "GovGXInvestCompany", "GovCYInvestCompany",
    "FinanceHoldingsCompany", "FundOfFund", "UnrecordedPrivateFundCompany"
}


class ZhPevcPoc(ExcelBase):
    """
    招行PEVC数据POC  https://docs.qq.com/doc/DU1ZURE1jeWFSS0JG?u=715bcab3647a4cffa65dcbd48ff5359b
    """

    def __init__(self):
        super(ZhPevcPoc, self).__init__()
        self.holder = CompanyHolder()
        self.category = CompanyCategory()
        self.alias = CompanyAlias()
        self.a_xsb_holder = CompanyAandXsbShareholder()
        self.inv_cmp = CompanyInvest()
        self.a_xsb_names = {i["cname"] for i in self.query_company_category()}

    # def process(self, *args, **kwargs):
    #     # node_list, ctl_node_set, link_dict = list(), set(), {"上汽大众汽车有限公司": {}}
    #     # self.add_ctl_node(link_dict, node_list, ctl_node_set, "上汽大众汽车有限公司")

    def process(self, *args, **kwargs):
        result = list()
        name_list = list({i.replace("(", "（").replace(")", "）").strip() for i in names})
        print(f"name_list = {len(name_list)}")
        alias_dict = self.alias.process(name_list)
        name_l = list({alias_dict.get(i, i) for i in name_list})
        print(f"name_l = {len(name_l)}")
        for name in name_l:
            ctl_list = self.query_ctl_info(name)  # 查控制人
            print(f"集团：{name} ctl_list={len(ctl_list)}")
            link_dict, ctl_node_set = dict(), set()
            link_dict.setdefault(name, dict())
            for item in ctl_list:
                node = item["nodeName"]
                link = json.loads(item["ctlPath"])["ctlLink"]
                link_dict[name].setdefault(node, list()).append(link)
                # 用于补充去重
                ctl_node_set.add(node)
            # 补充控制人节点
            node_list = list()
            new_link_dict = self.add_ctl_node(link_dict, node_list, ctl_node_set, name)
            # 查询标签
            cat_dict = self.category.process(node_list)
            temp_list = list()
            for jt_name, node_dict in new_link_dict.items():
                for node, link_list in node_dict.items():
                    cat = set(cat_dict.get(node, dict()).get("category", set()))
                    org = cat & org_cat
                    fund = cat & fund_cat
                    if not org and not fund:  # 既不是机构又不是基金
                        continue
                    mgs_name = self.max_ctl_data(link_list, node)
                    result_link = self.gen_link_path(link_list, jt_name, node)
                    temp_list.append(
                        {"s_name": jt_name, "e_name": node, "mgs_name": mgs_name,
                         "e_type": "机构" if org else "基金", "link": result_link})
            self.add_attr_field(temp_list, node_list)
            result.extend(temp_list)
        self.save_date(result)

    def add_ctl_node(self, link_dict, node_list, ctl_node_set, jt_name):
        ctl_node_set.add(jt_name)
        all_name = list(ctl_node_set)
        # X+S1 对外投资的子公司中 ， X与S1 是该子公司大股东的 节点S2，将S2也作为控制节点
        holder_result = list()
        for name in all_name:
            link = [name]
            finger = set()
            self.inv_and_max_holder(name, name, link, holder_result, finger, num=1)

        holder_dict = dict()
        for node in holder_result:
            cname = node["name"]
            holder_dict.setdefault(cname, list()).append(node)

        new_link_dict = dict()
        for jt_cmp, node_dict in link_dict.items():
            # 补充集团子公司投资数据
            for node, link_list in node_dict.items():
                if node in holder_dict:
                    for item in holder_dict[node]:
                        node_link = deepcopy(link_list)
                        inv_name = item["inv_name"]
                        node_list.append(inv_name)  # 用于查标签
                        holder_link = item["link"]
                        for link in node_link:
                            link.extend(holder_link)
                        new_link_dict.setdefault(jt_cmp, dict()).setdefault(inv_name, node_link)
            # 补充集团直接投资的数据
            for item in holder_dict.get(jt_cmp, list()):
                inv_name = item["inv_name"]
                if inv_name in ctl_node_set:
                    continue
                node_list.append(inv_name)  # 用于查标签
                holder_link = item["link"]
                new_link_dict.setdefault(jt_cmp, dict()).setdefault(inv_name, list()).append(holder_link)

        for jt_cmp, node_dict in link_dict.items():
            for node, link_list in node_dict.items():
                node_list.append(node)  # 用于查标签
                new_link_dict.setdefault(jt_cmp, dict()).setdefault(node, link_list)
        return new_link_dict

    def inv_and_max_holder(self, raw, name, link, result, finger, num):
        if len(name) < 4 or name in finger:
            result.append({"name": raw, "inv_name": name, "link": self.link_process(link)})
            return
        finger.add(name)
        inv_dict = self.inv_cmp.run([name])
        inv_names = {i["inv_name"] for i in inv_dict.get(name) or list()}
        inv_holder_dict = self.d_holder_cmp(list(inv_names))
        if not inv_holder_dict and num > 1:
            result.append({"name": raw, "inv_name": name, "link": self.link_process(link)})
        elif num > 1:
            result.append({"name": raw, "inv_name": name, "link": self.link_process(link)})
        for inv_item in inv_holder_dict.get(name) or list():
            new_link = deepcopy(link)
            inv_name = inv_item["name"]
            radio = inv_item["radio"]
            new_link.append(radio)
            new_link.append(inv_name)
            self.inv_and_max_holder(raw, inv_name, new_link, result, finger, num + 1)

    @staticmethod
    def link_process(link):
        result = list()
        for i in range(0, len(link), 2):
            if i > len(link) - 3:
                break
            result.append({
                'source_ctl': link[i],
                "target_ctl": link[i + 2],
                "ctl_ratio": link[i + 1]})
        return result

    def d_holder_cmp(self, names):
        # 大股东
        result = defaultdict(list)
        for i in names:
            if i in self.a_xsb_names:
                holder_data = self.a_xsb_holder.run([i])
            else:
                holder_data = self.holder.run([i])
            holder_list = holder_data.get(i) or list()
            d_holder_list = self.max_holder(holder_list)  # 大股东数据
            for d_holder in d_holder_list:
                holder_name = d_holder["shareholder_name"]
                if len(holder_name) < 4:  # 大股东是自然人时，不再查找该公司的兄弟企业
                    continue
                d_radio = str(round(d_holder["holdRatio"], 2))
                result[holder_name].append(
                    {"name": i, "holder": holder_name, "radio": d_radio,
                     "path": f"{holder_name}->{d_radio}->{i}"})
        return result

    @staticmethod
    def max_holder(holder_list):
        # 大股东
        result = defaultdict(list)
        for item in holder_list:
            ratio = float(item.get("holdRatio")) or 0.0
            result[ratio].append(item)
        if not result:
            return []
        max_ratio = max(result)
        if max_ratio == 0 or not max_ratio:
            return []
        else:
            return result[max_ratio]

    def query_company_category(self):
        query_schema = {
            "db_name": "category_data",
            "collection_name": "company_category",
            "query_condition": {"category": {"$in": ["ThirdCompany", "ACompany"]}, "is_valid": True},
            "query_field": {"cname": 1}}
        query_result = self._data_server.call("query_item", query_schema) or list()
        return query_result

    def save_date(self, result):
        field_cfg = {
            'e_name': ('机构名称', 0),
            'e_type': ('下属机构类型', 1),
            'mgs_name': ('所属投资机构名称', 2),
            's_name': ('所属集团', 3),
            'estiblishDate': ('成立日期', 4),
            'regCapital': ('注册资本(万元)', 5),
            'regStatus': ('注册状态', 6),
            'investAmount': ('认缴资本(万元)', 7),
            'holdRatio': ('持股比例', 8),
            'invNum': ('已投企业数', 9),
            'link': ('控制链路', 10),
        }
        self._excel_name = self.name_add_date("集团企业客户下属机构及基金表.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def gen_link_path(self, link_list, sv, ev):
        relation_link = list()
        for link in link_list:
            g = nx.MultiDiGraph()
            self.gen_graph_by_path(link, g)
            gen_simple_path = nx.all_simple_edge_paths(g, sv, ev)
            for path in gen_simple_path:
                relation_item = list()
                relation_item.append(path[0][0])
                for _, ev, ratio in path:
                    relation_item.append(str(round(float(ratio) * 100, 2)) + "%")
                    relation_item.append(ev)
                relation_link.append("->".join(relation_item))
        return "\n".join(relation_link)

    @staticmethod
    def gen_graph_by_path(n_paths, g):
        for n_path in n_paths:
            source_ctl = n_path["source_ctl"]
            target_ctl = n_path["target_ctl"]
            ctl_ratio = n_path["ctl_ratio"]
            direct = 1
            g.add_edge(source_ctl, target_ctl, ctl_ratio, dir=direct)
            g.add_edge(target_ctl, source_ctl, ctl_ratio, dir=direct * -1)

    def add_attr_field(self, temp_list, node_list):
        gs_data = self.query_gs_data(node_list)
        holder_data = self.holder.process(node_list)
        for item in temp_list:
            e_name = item["e_name"]
            mgs_name = item["mgs_name"]
            gs_dict = gs_data.get(e_name, dict())
            reg_status = gs_dict.get("regStatus", "")
            if reg_status:
                gs_dict["regStatus"] = reg_status_map[reg_status]
            item.update(gs_dict)  # estiblishDate|regCapital
            # 认缴资本,持股比例
            holder_list = holder_data.get(e_name, dict())
            for holder in holder_list:
                if holder["shareholder_name"] == mgs_name:
                    holder["holdRatio"] = str(round(holder["holdRatio"] * 100, 2)) + "%"
                    item.update(holder)  # investAmount|holdRatio
                    break
            # 已投企业数
            num = self.inv_cmp.run([e_name]).get(e_name, list())  # invNum
            if num:
                item["invNum"] = len(num)

    def query_gs_data(self, names):
        result = dict()
        name_str = "','".join(names)
        sql_statement = """SELECT compName, estiblishDate, regCapital/1000000 as regCapital, regStatus
         from sy_cd_ms_base_gs_comp_info_new WHERE dataStatus!=3 and compName in ('{}');"""
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            name = item["compName"]
            result.setdefault(name, item)
        return result

    def query_holder_data(self, names):
        result = dict()
        name_str = "','".join(names)
        sql_statement = """SELECT compName, estiblishDate, regCapital from sy_cd_ms_base_gs_comp_info_new 
                WHERE dataStatus!=3 and compName in ('{}');"""
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            name = item["compName"]
            result.setdefault(name, item)
        return result

    def query_inv_num(self, name):
        sql_statement = f"""select count(distinct entName) as num from sy_cs_me_trad_inv_event_flow_se where pkId in (
        select pkId from sy_cs_me_trad_inv_event_flow_se where entName='{name}' and typeCode=2 and dataStatus!=3)
         and typeCode=1 and dataStatus!=3;"""
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list[0]["num"]

    @staticmethod
    def max_ctl_data(link_list, node):
        max_ratio, max_mgs = 0, None
        for link in link_list:
            for item in link:
                source_ctl = item["source_ctl"]
                target_ctl = item["target_ctl"]
                ctl_ratio = item["ctl_ratio"]
                if node == target_ctl:
                    if float(ctl_ratio) > max_ratio:
                        max_ratio = float(ctl_ratio)
                        max_mgs = source_ctl
        return max_mgs

    def query_ctl_info(self, name):
        sql_statement = f"""
        SELECT ctlerName, nodeName, ctlPath, ctlRatio from sy_cd_ms_rela_ctl_info where ctlerName='{name}'"""
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    zh_pevc_poc = ZhPevcPoc()
    zh_pevc_poc.process()
