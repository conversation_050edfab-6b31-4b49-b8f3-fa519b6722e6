# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2020/03/25

import json
import os
import time

import paramiko
import requests
from multiprocessing import Pool
from urllib.parse import urlparse

from merge.fj_down_excel import Down
from utils.datetime_util import DatetimeUtil

# 下载目录
download_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                             "panda" + "_download")
if not os.path.exists(download_path):
    os.makedirs(download_path)


def download_process(attach_list, batch):
    try:
        dp = DownloadProcess()
        dp.download_file(attach_list, batch)
    except Exception as exc:
        print(exc)


class DownloadProcess(object):
    def __init__(self):
        super().__init__()

    @staticmethod
    def url_parse(raw_url):
        url_structure = urlparse(raw_url)
        return url_structure.hostname

    def download_file(self, attach_list, batch):
        start_time = DatetimeUtil.get_datetime_now()
        for attach_info in attach_list:
            accessory = json.loads(attach_info["accessory"])
            ori_path = attach_info["oriPath"]
            for attach in accessory:
                self.download_attachment(attach, ori_path)
        end_time = DatetimeUtil.get_datetime_now()
        print(batch)
        print(end_time - start_time)

    def download_attachment(self, attach, ori_path):
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.89 Safari/537.36'
        }
        file_path = os.path.join(download_path, ori_path)
        if not os.path.exists(file_path):
            try:
                os.makedirs(file_path)
            except Exception as e:
                print(e)

        attach_url = attach.get('url')
        if not attach['url'].startswith('http'):
            return {}
        name = attach.get('name')
        if name:
            name = name.replace('/', "_")
        else:
            name = attach_url.split('/')[-1]
        file_name = str(int(time.time() * 1000)) + name
        file_save_path = os.path.join(file_path, file_name)
        acc_type = attach.get('acc_type', "")
        f_name = file_name.split(".")
        if len(f_name) <= 1:
            file_name = file_name + "." + acc_type
        else:
            if len(f_name[-1]) > 5:
                file_name = file_name + "." + acc_type
        data = {
            "url": attach_url,
            "name": name,
            "acc_type": acc_type,
            "file_name": file_name,
        }
        try:
            down_res = ''
            try:
                down_res = requests.get(attach_url, headers=headers, timeout=(5, 45))
            except Exception as e:
                print(e)

            if not down_res:
                print(f"url = {attach_url}")
                data.setdefault("error_info", "timeout")
                return data
            if down_res.status_code != 200:
                down_res.raise_for_status()
            with open(file_save_path, 'wb') as file:
                file.write(down_res.content)
                # print(file_save_path, ori_path + "/" + file_name)
                self.upload(file_save_path, ori_path + "/" + file_name)
            return data
        except Exception as e:
            print(f"url = {attach_url}")
            print(e)
            data.setdefault("error_info", str(e))
            return data

    @staticmethod
    def upload(local_path, remote_path):
        hostname = '*************'
        port = 6022
        username = 'shiye_sftp_2020032402'
        password = 'shiye2020032402'
        # 获取Transport实例
        tran = paramiko.Transport((hostname, port))

        # 连接SSH服务端，使用password
        tran.connect(username=username, password=password)

        # 获取SFTP实例
        sftp = paramiko.SFTPClient.from_transport(tran)

        # mkdir
        path_list = remote_path.split('/')[:-1]
        path = "/data/"
        for item in path_list:
            path = path + "/" + item
            try:
                sftp.mkdir(path)
            except Exception as e:
                continue
        remote_path = "/data/" + remote_path
        # 执行上传动作
        sftp.put(local_path, remote_path)
        # print("local_path= " + local_path)
        # print("remote_path= " + remote_path)
        # 执行下载动作
        # sftp.get(remotepath, localpath)

        tran.close()


class DownloadFile():
    def __init__(self):
        super().__init__()
        self.worker_number = 10

    def process(self):
        attach_list = Down().process()
        print(f"attach_list = {len(attach_list)}")
        self.download(attach_list)

    def download(self, attach_list):
        process_list = list()
        batch, start, step = 1, 1, 30

        pool = Pool(processes=self.worker_number)
        while attach_list:
            process = pool.apply_async(download_process, (attach_list[:step], batch))
            attach_list = attach_list[step:]
            process_list.append(process)
            start += step
            batch += 1
        print(f"all_batch={batch}")

        pool.close()
        pool.join()
        return self.handler(process_list)

    def handler(self, process_list):
        for process in process_list:
            process.get()


if __name__ == '__main__':
    DownloadFile().process()
