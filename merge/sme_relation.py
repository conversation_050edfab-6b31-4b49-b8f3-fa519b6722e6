# -*- encoding:utf-8 -*-
# Copyright (c) 2025 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2025/9/15

from core.excel_base import ExcelBase


class SmeRelation(ExcelBase):
    def __init__(self):
        super(SmeRelation, self).__init__()

    def process(self, names):
        """
        处理公司名称列表，查询小微企业关联信息
        :param names: 公司名称列表
        """
        print(f"开始处理 {len(names)} 个公司的小微企业关联信息")
        result = list()

        for idx in range(0, len(names), 100):
            name_list = names[idx:idx + 100]
            base_info_list = self.query_names(name_list)
            print(f"第 {idx // 100 + 1} 批次，查询到基础信息: {len(base_info_list)} 条")

            # 查询小微企业关联信息
            batch_sme_result = self.query_sme_relation(base_info_list)
            result.extend(batch_sme_result)

        print(f"总共处理完成 {len(result)} 条小微企业关联信息")
        # self.save_data_excel(result)
        self.update_mysql(result)

    def query_names(self, names):
        """
        根据公司名称查询基础信息
        :param names: 公司名称列表
        :return: 包含compCode和compName的列表
        """
        name_str = "','".join(names)
        sql_statement = """SELECT compCode, compName from sy_cd_ms_base_normal_comp_list 
                WHERE dataStatus!=3 and compName in ('{}');"""
        query_schema = dict(db_key="xskv2", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list

    def query_sme_relation(self, base_info_list):
        """
        查询小微企业关联信息
        :param base_info_list: 包含compCode和compName的基础信息列表
        :return: 包含company_name、sme_relation、ecosystem_data和standard_setting的结果列表
        """
        if not base_info_list:
            return []

        # 获取所有公司的compCode
        comp_codes = [str(item["compCode"]) for item in base_info_list]
        name_str = "','".join(comp_codes)

        # 查询小微企业表
        sql_statement = """SELECT compCode, compName from sy_cd_ms_base_micro_list
                WHERE dataStatus!=3 and compCode in ('{}');"""
        query_schema = dict(db_key="xskv2", sql_statement=sql_statement.format(name_str))
        micro_list = self._data_server.call("query_sql_item", query_schema) or list()

        print(f"在小微企业表中找到 {len(micro_list)} 条匹配记录")

        # 查询上市企业表 sy_cd_ms_base_sk_stock
        stock_sql_statement = """SELECT compCode, compName from sy_cd_ms_base_sk_stock
                WHERE dataStatus!=3 and compCode in ('{}');"""
        stock_query_schema = dict(db_key="xskv2", sql_statement=stock_sql_statement.format(name_str))
        stock_list = self._data_server.call("query_sql_item", stock_query_schema) or list()

        print(f"在上市企业表中找到 {len(stock_list)} 条匹配记录")

        # 查询标准制定相关信息
        standard_counts = self.query_standard_counts(base_info_list)

        # 创建小微企业compCode集合，用于快速查找
        micro_comp_codes = {item["compCode"] for item in micro_list}

        # 创建上市企业compCode集合，用于快速查找
        stock_comp_codes = {item["compCode"] for item in stock_list}

        # 构建结果列表
        result = []
        for item in base_info_list:
            comp_code = item["compCode"]
            comp_name = item["compName"]
            sme_relation = "小微企业" if comp_code in micro_comp_codes else "无关联"
            ecosystem_data = "上市企业" if comp_code in stock_comp_codes else "未上市"

            # 获取标准制定参与情况
            standard_count = standard_counts.get(comp_code, 0)
            standard_setting = "参与标准制定" if standard_count > 0 else "未参与标准制定"

            result.append({
                "company_name": comp_name,
                "sme_relation": sme_relation,
                "ecosystem_data": ecosystem_data,
                "standard_setting": standard_setting,
                "comp_code": comp_code  # 添加compCode便于调试
            })

        return result

    def query_standard_counts(self, base_info_list):
        """
        查询企业参与标准制定的数量
        :param base_info_list: 包含compCode和compName的基础信息列表
        :return: 字典，key为compCode，value为标准数量
        """
        if not base_info_list:
            return {}

        # 获取所有公司的compCode
        comp_codes = [str(item["compCode"]) for item in base_info_list]
        name_str = "','".join(comp_codes)

        standard_counts = {}

        # 定义四个标准表
        standard_tables = [
            "sy_cd_me_buss_std_ttbzmd",    # 团体标准名单
            "sy_cd_me_buss_std_gjbzmd",    # 国家标准名单
            "sy_cd_me_buss_std_dfbz_table", # 地方标准备案清单表
            "sy_cd_me_buss_std_hybz_table"  # 行业标准备案清单表
        ]

        # 查询每个标准表
        for table_name in standard_tables:
            sql_statement = f"""SELECT compCode, COUNT(*) as standard_count
                    FROM {table_name}
                    WHERE dataStatus!=3 and compCode in ('{name_str}')
                    GROUP BY compCode;"""
            query_schema = dict(db_key="xskv2", sql_statement=sql_statement)

            try:
                result_list = self._data_server.call("query_sql_item", query_schema) or list()
                print(f"在{table_name}中找到 {len(result_list)} 个企业的标准记录")

                # 累加每个企业的标准数量
                for item in result_list:
                    comp_code = item["compCode"]
                    count = item["standard_count"]
                    standard_counts[comp_code] = standard_counts.get(comp_code, 0) + count

            except Exception as e:
                print(f"查询{table_name}时出错: {e}")
                continue

        print(f"总共找到 {len(standard_counts)} 个企业参与标准制定")
        return standard_counts

    def save_data_excel(self, result):
        """
        保存小微企业关联数据到Excel
        :param result: 小微企业关联信息列表
        """
        # 小微企业关联信息字段配置
        field_cfg = {
            'company_name': ('company_name', 0),
            'sme_relation': ('sme_relation', 1),
            'ecosystem_data': ('ecosystem_data', 2),
            'standard_setting': ('standard_setting', 3),
            # 'comp_code': ('公司代码', 4)
        }

        # 生成Excel文件名
        self._excel_name = self.name_add_date("小微企业关联数据.xlsx")

        # 保存到Excel
        self.save_to_excel(field_cfg, {"小微企业关联": result})

        # 统计信息
        sme_count = sum(1 for item in result if item["sme_relation"] == "小微企业")
        no_relation_count = len(result) - sme_count
        listed_count = sum(1 for item in result if item["ecosystem_data"] == "上市企业")
        unlisted_count = len(result) - listed_count
        standard_participate_count = sum(1 for item in result if item["standard_setting"] == "参与标准制定")
        standard_not_participate_count = len(result) - standard_participate_count

        print(f"数据保存完成:")
        print(f"- 文件名: {self._excel_name}")
        print(f"- 总记录数: {len(result)}")
        print(f"- 小微企业: {sme_count} 家")
        print(f"- 无关联: {no_relation_count} 家")
        print(f"- 上市企业: {listed_count} 家")
        print(f"- 未上市: {unlisted_count} 家")
        print(f"- 参与标准制定: {standard_participate_count} 家")
        print(f"- 未参与标准制定: {standard_not_participate_count} 家")

    def update_mysql(self, result):
        """
        将数据更新插入到MySQL数据库
        :param result: 小微企业关联信息列表
        """
        if not result:
            print("没有数据需要更新到MySQL")
            return

        print(f"开始将 {len(result)} 条数据更新到MySQL数据库")

        # 定义表名和数据库配置
        table_name = "supply_relationship"  # 请根据实际表名修改
        db_key = "psbc2025"  # 请根据实际数据库配置修改

        # 批量处理数据
        batch_size = 100
        success_count = 0
        error_count = 0

        for idx in range(0, len(result), batch_size):
            batch_data = result[idx:idx + batch_size]
            try:
                self._update_mysql_batch(batch_data, table_name, db_key)
                success_count += len(batch_data)
                print(f"成功更新第 {idx // batch_size + 1} 批次，共 {len(batch_data)} 条记录")
            except Exception as e:
                error_count += len(batch_data)
                print(f"第 {idx // batch_size + 1} 批次更新失败: {e}")
                continue

        print(f"MySQL更新完成:")
        print(f"- 成功更新: {success_count} 条")
        print(f"- 更新失败: {error_count} 条")

    def _update_mysql_batch(self, batch_data, table_name, db_key):
        """
        批量更新MySQL数据
        :param batch_data: 批次数据
        :param table_name: 表名
        :param db_key: 数据库配置key
        """
        # 构建INSERT ... ON DUPLICATE KEY UPDATE SQL语句
        sql_values = []

        for item in batch_data:
            company_name = item.get("company_name", "").replace("'", "\\'")
            sme_relation = item.get("sme_relation", "").replace("'", "\\'")
            ecosystem_data = item.get("ecosystem_data", "").replace("'", "\\'")
            standard_setting = item.get("standard_setting", "").replace("'", "\\'")

            value_str = f"('{company_name}', '{sme_relation}', '{ecosystem_data}', '{standard_setting}')"
            sql_values.append(value_str)

        values_clause = ",\n".join(sql_values)

        sql_statement = f"""
        INSERT INTO {table_name}
        (company_name, sme_relation, ecosystem_data, standard_setting)
        VALUES {values_clause}
        ON DUPLICATE KEY UPDATE
        sme_relation = VALUES(sme_relation),
        ecosystem_data = VALUES(ecosystem_data),
        standard_setting = VALUES(standard_setting)
        """

        # 执行SQL语句
        query_schema = dict(db_key=db_key, sql_statement=sql_statement)
        self._data_server.call("execute_sql_item", query_schema)




if __name__ == '__main__':
    # 测试数据
    test_names = [
        "株洲千金药业股份有限公司','浙江正裕工业股份有限公司','长江智能科技（广东）股份有限公司','上海文鳐信息科技有限公司','扬州崔八味生物科技有限公司"
    ]

    sme = SmeRelation()

    # 处理指定公司的小微企业关联信息
    sme.process(test_names)

    # 可选：导出所有小微企业名单
    # sme.export_all_sme_companies()