# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/18
import json
import http.client
import traceback

from core.excel_base import ExcelBase
from moduler.company_area_info import CompanyArea
from moduler.company_industry_and_cyl import CompanyIndustryAndCYL

"""
农行数据导出需求
1、导出“国家电网公司”下属投资公司中注册所在地为四川省所有公司名录，包括实控产业公司、参股公司。
导出字段：企业名称、注册地省份、注册地（市，区）、控股公司/参股公司、行业 注册地址和办公地址。
"""


class NH(ExcelBase):
    def __init__(self):
        super(NH, self).__init__()
        self.file_name = self._in_file_path + "/股权关联关系_20210118 (1).xlsx"

    def process(self, *args, **kwargs):
        result = list()
        name_dict, area_dict, ind_dict = dict(), dict(), dict()
        for item in self.read_excel_data(self.file_name):
            c_name = item["成员企业名称"]
            name_dict.setdefault(c_name, item)
            # name_dict[c_name].append(item)
        cmp_names = list(name_dict.keys())
        for idx in range(0, len(cmp_names), 50):
            names = cmp_names[idx: idx + 50]
            area_info = CompanyArea.run(names)
            area_dict.update(area_info)
            ind_info = CompanyIndustryAndCYL.run(names)
            ind_dict.update(ind_info)
        num = 0
        for name, item in name_dict.items():
            num += 1
            print(num)
            tmp_item = dict()
            area = area_dict.get(name, dict())
            province = area.get("provinceName")
            if not province or province != '四川省':
                continue
            tmp_item.update(area)

            base_info = self.query_gs_info(name) or dict()
            tmp_item.update(base_info)

            ind = ind_dict.get(name, list())
            ind_list = [i["IndustryName"] for i in ind if i.get("IndustryName")]
            if ind_list:
                tmp_item["IndustryName"] = ",".join(ind_list)

            addr = self.query_rep_info(name) or dict()
            bg_addr = addr.get("content", dict()).get("postal_address")
            if bg_addr:
                tmp_item["bg_addr"] = bg_addr
            tmp_item["name"] = name
            tmp_item["link"] = item["链路"]
            relation = item["关系类型"]
            tmp_item["relation"] = "参股公司" if relation in {"参股公司", "非实际控制的参股企业"} else "控股公司"
            result.append(tmp_item)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            'name': ('公司名称', 0),
            'relation': ('关系类型', 1),
            'IndustryName': ('视野行业', 2),
            'provinceName': ('所属省', 3),
            'cityName': ('所属市', 4),
            'district': ('所属区', 5),
            'reg_location': ('注册地址', 6),
            'bg_addr': ('办公地址', 7),
            'link': ('链路', 8),
        }
        self._excel_name = self.name_add_date("农业银行POC数据.xlsx")
        self.save_to_excel(field_cfg, {"Sheet": result})

    def read_excel_data(self, file_name, sheet="Sheet"):
        data_list = self._extract_data(file_name, sheet)
        return data_list

    @staticmethod
    def query_rep_info(raw_name):
        result_list = list()
        headers = {"Content-type": "application/x-www-form-urlencoded",
                   "Accept": "text/plain"}
        httpClient = http.client.HTTPConnection("10.10.128.185", 9102, timeout=30)
        # httpClient = http.client.HTTPConnection("60.205.212.21", 9102, timeout=30)
        cookie, limit_num = None, 20
        while True:
            try:
                request_1_1 = {
                    "user_id": "edcec33c8bdf48249cfc7985d39a39a3",
                    "limit_num": limit_num,
                    "company": raw_name}
                if cookie is not None:
                    request_1_1["cookie"] = cookie
                params = json.dumps(request_1_1)
                httpClient.request(
                    "POST", "/v2.0/companies/background_info_add", params, headers)
                response = httpClient.getresponse()
                result = response.read().decode("utf-8")
                result = json.loads(result)
                if result["response_state"] != 1:
                    raise Exception("request err.param={}".format(params))
                response_content = result["response_content"]
                # print(response_content)
                return response_content
            except Exception as exc:
                err_msg = traceback.format_exc()
                err_msg = " ## ".join(err_msg.split("\n"))
                err_msg = " ## ".join([err_msg, str(exc)])
                print(err_msg)
                break
        if httpClient is not None:
            httpClient.close()
        return result_list

    @staticmethod
    def query_gs_info(raw_name):
        result_list = list()
        headers = {"Content-type": "application/x-www-form-urlencoded",
                   "Accept": "text/plain"}
        httpClient = http.client.HTTPConnection("10.10.128.185", 9102, timeout=30)
        # httpClient = http.client.HTTPConnection("60.205.212.21", 9102, timeout=30)
        cookie, limit_num = None, 20
        while True:
            try:
                request_1_1 = {
                    "user_id": "edcec33c8bdf48249cfc7985d39a39a3",
                    "limit_num": limit_num,
                    "company": raw_name}
                if cookie is not None:
                    request_1_1["cookie"] = cookie
                params = json.dumps(request_1_1)
                httpClient.request(
                    "POST", "/v2.0/companies/company_m_base_info", params, headers)
                response = httpClient.getresponse()
                result = response.read().decode("utf-8")
                result = json.loads(result)
                if result["response_state"] != 1:
                    raise Exception("request err.param={}".format(params))
                response_content = result["response_content"]
                return response_content
            except Exception as exc:
                err_msg = traceback.format_exc()
                err_msg = " ## ".join(err_msg.split("\n"))
                err_msg = " ## ".join([err_msg, str(exc)])
                print(err_msg)
                break
        if httpClient is not None:
            httpClient.close()
        return result_list


if __name__ == '__main__':
    p = NH()
    p.process()
    # p.query_rep_info("中国兵工物资东北公司")
