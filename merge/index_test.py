# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2024/1/19
from cfg.config import source_file_path
from core.excel_base import ExcelBase
from colorama import Fore, Back, Style


class IndexTest(ExcelBase):
    def __init__(self):
        super().__init__()
        self.file_map = {
            "600084": "dwd_ms_cn_comp_ve_five_set_600084.csv",
            "104001": "dwd_ms_cn_comp_ve_main_inco_20231229.csv",
            "206020": "dwd_ms_cn_comp_ve_mpro_rat_20240118.csv",
            "104003": "dwd_ms_cn_comp_ve_npro_sk.csv",
            "700008": "dwd_ms_cn_comp_ve_bcm_first_700008.csv",
            "600083": "dwd_ms_cn_comp_ve_five_set (1).csv",
        }

    def process(self):
        a_names = self.query_a_name()
        three_names = self.query_three_name()
        names = a_names | three_names
        for code, file_name in self.file_map.items():
            print("=" * 50)
            csv_result = set()
            csv_list = self.read_excel_data(file_name)
            for i in csv_list:
                if str(i["indicationid"]) != str(code):
                    continue
                csv_result.add(i["compcode"])
            coverage = self.coverage_rate(csv_result, names)
            print(f"A股公司数据量：{Fore.RED + str(len(a_names)) + Style.RESET_ALL}")
            print(f"三板公司数据量：{Fore.RED + str(len(three_names)) + Style.RESET_ALL}")
            print(f"A股三板合并后去重的公司数据量：{Fore.RED + str(len(names)) + Style.RESET_ALL}\n")

            print(f"{code} csv的数据量：{Fore.RED + str(len(csv_result)) + Style.RESET_ALL}\n")
            print(f"{code} 与 A股交集的数据量：{Fore.RED + str(len(csv_result & a_names)) + Style.RESET_ALL}")
            print(f"非A股的公司编码：{list(csv_result - a_names)[:5]}\n")
            print(f"{code} 与 三板交集的数据量：{Fore.RED + str(len(csv_result & three_names)) + Style.RESET_ALL}")
            print(f"非三板的公司编码：{list(csv_result - three_names)[:5]}\n")
            print(f"{code} 与 A股三板交集的数据量：{Fore.RED + str(len(csv_result & names)) + Style.RESET_ALL}")
            print(f"非A股三板的公司编码：{list(csv_result - names)[:5]}\n")
            print(f"A股三板不在 csv中的公司数量：{Fore.RED + str(len(names -csv_result)) + Style.RESET_ALL}")
            print(f"A股三板不在 csv中的公司编码：{list(names-csv_result)[:10]}\n")
            print(f"{code} 覆盖率：{Fore.RED + str(coverage) + Style.RESET_ALL}\n\n\n")

    def coverage_rate(self, a, b):
        #  A 中元素在集合 B 中出现的比例
        if len(b) == 0:
            return 0.0
        return len(a.intersection(b)) / len(b)

    def query_a_name(self):
        result = set()
        sql = """
        SELECT id, compCode,dataStatus from sy_cd_ms_base_sk_stock where listStatus=1 and 
        id > {} ORDER BY id ASC limit 1000;
        """
        for items in self._query_sql_iter_by_id(sql, "xskv2"):
            for item in items:
                compCode = item["compCode"]
                dataStatus = item["dataStatus"]
                if dataStatus == 3:
                    continue
                result.add(compCode)
        return result

    def query_three_name(self):
        result = set()
        sql = """
        SELECT id, compCode, dataStatus from sy_cd_ms_base_nq_stock where listStatus=1 and 
        id > {} ORDER BY id ASC limit 1000;
        """
        for items in self._query_sql_iter_by_id(sql, "xskv2"):
            for item in items:
                compCode = item["compCode"]
                dataStatus = item["dataStatus"]
                if dataStatus == 3:
                    continue
                result.add(compCode)
        return result

    def read_excel_data(self, file_name):
        import pandas as pd
        df = pd.read_csv(source_file_path + "/" + file_name)
        data_list = df.to_dict(orient="records")
        data_list = self.filter_data(data_list)
        return data_list


if __name__ == '__main__':
    p = IndexTest()
    p.process()
