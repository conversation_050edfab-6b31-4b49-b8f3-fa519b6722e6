# -*- coding: utf-8 -*-
"""
@Time ： 2023/1/16 15:00
@Auth ： liry
@File ：
@IDE ：PyCharm
readme: 算子测试
注意：
dev:
pred:
"""

import pandas as pd
import numpy as np
import jt_def

zb_df = pd.read_csv('../doc/dwd_ms_mtc_bs_mid_10_10_20240808.csv')
zb_df = zb_df[["compcode","indicationid", "gbindfir", "score"]]
zb_df = zb_df[zb_df.gbindfir == '电力、热力、燃气及水生产和供应业']

para = 10400511
md_df = pd.DataFrame()
md_df['compcode'] = pd.Series([163369145,
                               3068044249,
                               2353569609])

kq_df = zb_df[zb_df.indicationid == para]

print(kq_df.head(10))
# exit()

# kq_df.to_csv('E:\下载使用文件\接收人文件\刘青鹏\采矿业专利质押数量.csv')
# exit()


kq_df['indicationValue'] = kq_df['score'].copy()
kq_df.index = pd.Series(range(len(kq_df)))
del (kq_df['score'])
y_data_list = list(kq_df['indicationValue'])
t_data_array = np.array(jt_def.get_indication_digital(y_data_list))

t_data_list0 = list(t_data_array[t_data_array != -np.inf])

print(len(t_data_list0))

if len(t_data_list0) == 0:
    print(f'{para}---{len(kq_df)}--0')
    number = len(kq_df)
    kq_df['score'] = pd.Series(np.zeros(number))

else:
    # print(pd.merge(md_df, kq_df, on='compcode', how='left'))
    t_min = min(t_data_list0)
    t_data_list = [i - t_min + 1 for i in t_data_array]
    kq_df['indicationValue'] = pd.Series(t_data_list)

    max_value = np.max(t_data_list)
    min_value = np.min(t_data_list)

    if max_value == min_value:
        print(para, '单柱数据')
        kq_df['score'] = pd.Series(
            [50 if i > 0 else 0 for i in t_data_list])

    # 非单柱数值计算
    else:
        # 进行数据排序
        comp_data_frame = pd.DataFrame()
        comp_data_frame['compcode'] = kq_df['compcode'].copy()
        comp_data_frame['indicationValue'] = kq_df['indicationValue'].copy()
        comp_data_frame = comp_data_frame.sort_values(by=['indicationValue'], ascending=True)
        calc_df_delzero = comp_data_frame[comp_data_frame['indicationValue'] > 0]
        calc_df_delzero.index = pd.Series(range(len(calc_df_delzero)))  # 确认索引数据，防止排序造成数据偏移
        fg_value = 1

        # 防止出现标准差为0情况
        try:
            he_df0 = jt_def.skew_calc(calc_df_delzero, 'indicationValue')
        except:
            he_df0 = calc_df_delzero.copy()
            he_df0['indicationValue'] = pd.Series([100 for i in range(len(he_df0))])
        # 正值正态化
        print(pd.merge(md_df, he_df0, on='compcode', how='left'))

        # exit()
        bz_type = 'min-max'
        if bz_type == 'z-score':
            he_df1 = jt_def.zscore_calc(he_df0, 'indicationValue')
        else:
            he_df1 = jt_def.minmax_calc(he_df0, 'indicationValue', 2.5)

        # print(he_df1.head(), max(he_df1['indicationValue']),min(he_df1['indicationValue']))
        print(pd.merge(md_df, he_df1, on='compcode', how='left'))
        # 是否稀缺性映射
        ok_jing = '是'
        if ok_jing == '是':
            # end_value = jt_def.get_fg_score(fg_value)
            end_value = 60
            calc_minmax = [i * (100 - end_value) / 100 + end_value for i in
                           list(he_df1['indicationValue'])]
            he_df1['indicationValue'] = pd.Series(calc_minmax)
        print(pd.merge(md_df, he_df1, on='compcode', how='left'))
        exit()
        he_df1['score'] = he_df1['indicationValue']
        kq_df = kq_df[['compcode', 'compName', 'indexVal']]
        kq_df = pd.merge(kq_df, he_df1, on='compcode', how='left')
        kq_df = kq_df.drop_duplicates()
        kq_df.index = pd.Series(range(len(kq_df)))
        h_value = 0  # 默认回填值
        if fanf['是否异常值回填'] == '否':
            h_value = 0
        kq_df['score'] = pd.Series([round(i, 2) if i > 0 else h_value for i in kq_df['score']])
        ok_fan = fanf['是否转置转化']
        if ok_fan == '是':
            kq_df['score'] = pd.Series([100 - i for i in kq_df['score']])
