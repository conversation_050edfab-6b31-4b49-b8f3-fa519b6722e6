# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022/11/18
from core.excel_base import ExcelBase


class Chenge(ExcelBase):
    def __init__(self):
        super(<PERSON><PERSON>, self).__init__()
        self.schema_db = "db_seeyii"
        self.schema_table = "app_payh_ms_cn_px_spt"
        self.schema_db_key = "db_seeyii"
        self.db_124 = 'db_seeyii'
        self.db_157 = 'section_data'

    def process(self, *args, **kwargs):
        result = list()
        data_124_dict = self.query_raw_data(self.db_124)
        print(f"data_124_dict = {len(data_124_dict)}")
        data_157_dict = self.query_raw_data(self.db_157)
        print(f"data_157_dict = {len(data_157_dict)}")
        add = data_157_dict.keys() - data_124_dict.keys()
        print(f"inc={len(add)}")
        _del = data_124_dict.keys() - data_157_dict.keys()
        print(f"del={len(_del)}")
        for d in _del:
            data = data_124_dict[d]
            data["dataStatus"] = 3
            result.append(data)
        result += [data_157_dict[i] for i in add]
        print(f"change = {len(result)}")

        field_cfg = self.import_data_cfg()
        self._excel_name = self.name_add_date("{}.xlsx".format(self.schema_table))
        self.save_to_excel(field_cfg, {"sheet1": result})

    def query_raw_data(self, db_key):
        result = dict()
        sql = """SELECT * FROM %s WHERE id>'{}' order by id ASC limit 1000""" % self.schema_table
        for result_list in self._query_sql_iter_by_id(sql, db_key):
            for item in result_list:
                data_status = item["dataStatus"]
                if data_status == 3:
                    continue
                finger_id = item["fingerId"]
                result.setdefault(finger_id, item)
        return result

    def import_data_cfg(self):
        data_dict = dict()
        item_list = self.query_table_schema()
        for idx, item in enumerate(item_list):
            data_dict.setdefault(item["column_name"],
                                 (item["column_comment"] if item["column_comment"] else item["column_name"], idx))
        return data_dict

    def query_table_schema(self):
        data_list = list()
        sql = """select column_name, column_comment 
        from information_schema.columns where table_schema ='{}' 
         and table_name = '{}';"""
        query_schema = dict(db_key=self.schema_db_key, sql_statement=sql.format(self.schema_db, self.schema_table))
        result_list = self._data_server.call("query_sql_item", query_schema)
        for item in result_list:
            data_list.append(item)
        return data_list


if __name__ == '__main__':
    p = Chenge()
    p.process()
