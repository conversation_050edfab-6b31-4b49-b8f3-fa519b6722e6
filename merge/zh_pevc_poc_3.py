# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022/5/31
import json
import networkx as nx
from copy import deepcopy

from cfg.constant import reg_status_map
from core.excel_base import ExcelBase
from moduler.company_alias_name import CompanyAlias
from moduler.company_area_info import CompanyArea
from moduler.company_category import CompanyCategory
from moduler.company_gb_industry import CompanyGBIndustry
from moduler.company_holder import CompanyHolder

fund_cat = {
    "PrivateFund",
    "UnrecordedFund"
}
org_cat = {
    "PrivateFundCompany", "PublicFundCompany", "SecurityCompany", "FuturesCompany", "InvestCompanyOfBroker",
    "Inv100003", "PublicChildCompany", "DevelopmentBank", "SYAU10000", "PolicyBank", "AssetManagementCompany",
    "FinancialLeaseCompany", "LargeCommercialBank", "JointStockCommercialBank", "PrivateBank", "RuralCommercialBank",
    "ForeignBank", "CityCommercialBank", "NationalInvestmentPlatform", "InvestmentConsultCompany", "TrustCompany",
    "FinanceCompany", "InsuranceCompany", "InsuranceAssetCompany", "GovGXInvestCompany", "GovCYInvestCompany",
    "FinanceHoldingsCompany", "FundOfFund", "UnrecordedPrivateFundCompany"
}
name_dict = {
    '中国机械工业集团有限公司': ['北京鑫恒景元房地产投资有限公司', '北京中经华澳投资管理有限公司', '中泰世嘉建设投资有限公司', '北京中扶众望投资基金管理有限公司', '天津市良好投资发展有限公司',
                     '中自控国际工程投资有限公司', '共青城海德麦克斯韦股权投资合伙企业（有限合伙）', '无锡中融融优创享投资企业（有限合伙）',
                     '宁波梅山保税港区佳华逸金股权投资基金合伙企业（有限合伙）',
                     '上海淡若投资管理合伙企业（有限合伙）', '上海国年企业管理咨询合伙企业（有限合伙）', '绍融投资管理（上海）有限公司', '北京中融稳达资产管理有限公司',
                     '中融鼎兴资产管理有限公司',
                     '新余双鼎投资中心（有限合伙）', '北京中融鼎泓投资有限公司', '上海隆山投资管理有限公司', '达孜县鼎诚资本投资有限公司',
                     '宁波梅山保税港区悦融乐影股权投资合伙企业（有限合伙）',
                     '北京中融恒睿资本投资管理有限公司', '上海高特佳懿康投资合伙企业（有限合伙）', '天津中融鼎丰股权投资合伙企业（有限合伙）',
                     '宁夏恒天丝路产业投资基金合伙企业（有限合伙）',
                     '嘉兴融运投资合伙企业（有限合伙）', '中融世鸿资产管理有限公司', '上海美年大健康一期股权投资基金合伙企业（有限合伙）', '达孜县鼎瑞资本投资有限公司',
                     '上海鼎慕投资管理有限公司',
                     '深圳中融宝晟资产管理有限公司', '深圳中融丝路资产管理有限公司', '中融长河资本投资管理有限公司', '上海长昆投资管理有限公司', '中融汇今资产管理有限公司',
                     '深圳中融启点投资管理中心（有限合伙）', '天津中昌源投资有限公司', '上海金汇投资实业有限公司', '广东蓝海投资有限公司', '恒天资产管理有限公司',
                     '恒天创业投资有限公司',
                     '上海御恒投资有限公司', '恒天文化产业投资集团有限公司', '恒天（黄冈）时尚创意投资发展有限公司', '山东恒天投资有限公司', '中国纺织机械集团建设投资有限公司',
                     '三亚浦海旅游产业股权投资基金管理有限公司', '上海简亚投资中心（有限合伙）', '上海亚茂投资中心（有限合伙）', '上海浦万投资中心（有限合伙）',
                     '上海海亨投资中心（有限合伙）',
                     '上海协浦投资中心（有限合伙）', '上海莱韵投资有限公司', '上海金浦合芯企业管理合伙企业（有限合伙）', '中机建设集团（安庆）建设投资有限公司',
                     '中汽零（赣州）工业投资管理有限公司',
                     '深圳市深华盛投资发展有限公司', '温州明瓯基础设施投资有限公司', '上海申歆医疗投资管理有限公司', '国机（天津）创业投资中心（有限合伙）',
                     '国机（北京）投资基金管理有限责任公司',
                     '中海蓝色产业投资基金管理有限公司', '嘉兴昀泽艾欧投资合伙企业（有限合伙）', '郑州绿苑新材料投资有限公司', '北京三联国际投资有限责任公司',
                     '嘉兴聚力展业柒号股权投资合伙企业（有限合伙）', '湖州融睿股权投资合伙企业（有限合伙）', '中投中工丝路投资基金管理（北京）有限公司', '恒天金石江苏投资管理有限公司',
                     '恒天投资管理有限公司', '上海仁恒投资管理有限公司', '中瑞投资（大连）有限公司', '中机农业发展投资有限公司', '天津融辉鼎颐企业管理合伙企业（有限合伙）',
                     '中融（北京）资产管理有限公司', '中国国际农业投资有限公司', '中国一拖集团财务有限责任公司', '中非重工投资有限公司', '国机投资管理（上海）有限公司',
                     '国机投资管理成都有限公司', '北京中投中工丝路产业投资基金（有限合伙）', '中工投资管理有限公司', '北京掌运星融钨投资中心（有限合伙）',
                     '武汉光跃恒达私募股权投资基金管理合伙企业（有限合伙）', '宁波梅山保税港区国富衡诚投资合伙企业（有限合伙）', '美恭（上海）投资合伙企业（有限合伙）',
                     '北京鑫恒投资管理有限公司',
                     '意赞（上海）投资合伙企业（有限合伙）', '深圳市云津投资管理有限公司', '中融-光影传奇1号投资基金', '恒天精纺（北京）投资中心（有限合伙）',
                     '北京博智恒益投资中心（有限合伙）',
                     '天津融辉鼎盛股权投资合伙企业（有限合伙）', '天津永道投资合伙企业（有限合伙）', '上海树明投资有限公司', '鼎金价值1号稳健型证券投资基金',
                     '中融鼎新-鼎融盛源8号新三板投资基金',
                     '达孜县中融善从创业投资中心（有限合伙）', '嘉兴融赋投资合伙企业（有限合伙）', '中融鼎新-鼎融利丰19号投资基金', '安享价值1号私募投资基金',
                     '中融鼎新-鼎融汇富6号产业并购基金', '天津永毅投资合伙企业（有限合伙）', '北京中融新融投资合伙企业（有限合伙）', '天津才横管理咨询合伙企业（有限合伙）',
                     '中融商骥资产管理有限公司', '中融鼎新-融雅38号投资基金', '北京鼎金卓利股权投资管理中心', '中融鼎新-融珲29号TMT并购与新三板投资基金',
                     '中融鼎新-鼎融汇富17号投资基金',
                     '上海禹业投资中心（有限合伙）', '中融资产-新湖赋成量化6号特定客户资产管理计划', '北京中融铭新投资管理有限公司', '上海觅豪投资管理合伙企业（有限合伙）',
                     '湖州融骏股权投资合伙企业（有限合伙）', '达孜鼎诚-金星1号证券投资基金', '中融鼎新融稳达1号新三板基金', '北京中融鼎新投资管理有限公司', '鼎融利丰39号私募基金',
                     '中融掌运星-融艺私募股权投资基金', '上海钜信投资发展中心（有限合伙）', '中融宝晟晟世融安3号基金', '北京坤域管理咨询合伙企业（有限合伙）',
                     '中融恒睿-蓝天1号股权投资基金',
                     '中融鼎新-鼎融利丰23号投资基金', '中融-融佳新雅医药产业并购基金', '中融聚创资产管理有限公司', '中融鼎新-鼎融利丰43号私募投资基金',
                     '滁州市卓耕投资置业有限公司',
                     '中融鼎新-鼎融利丰16号投资基金', '中融鼎新-鼎融利丰8号基金', '中融鼎新-新里程1号股权投资基金', '上海千熙投资中心（有限合伙）',
                     '中融鼎新-金谷仓1号股权投资基金',
                     '中融宝晟-晟世融安15号契约型私募基金', '鑫璟宸澜（上海）投资中心（有限合伙）', '上海中融融欧一期股权投资基金合伙企业（有限合伙）', '珺敦投资管理（上海）有限公司',
                     '中融鼎新-天平1号基金', '上海确智投资管理合伙企业（有限合伙）', '北京恒欣汇智投资中心（有限合伙）', '上海朔盈投资管理合伙企业（有限合伙）',
                     '上海道颖投资管理中心（有限合伙）',
                     '北京博远聚鑫投资中心（有限合伙）', '中融鼎新-鼎融汇富15号投资基金', '中融鼎新-鼎融汇富11号契约型基金', '上海达简投资合伙企业（有限合伙）',
                     '湖州春和股权投资合伙企业（有限合伙）', '商骥（上海）投资中心（有限合伙）', '上海衡诗投资管理中心（有限合伙）', '上海璋睿企业管理咨询合伙企业（有限合伙）',
                     '中融亿成资产管理有限公司', '上海晟昱投资有限公司', '北京中核鼎元股权投资管理中心（有限合伙）', '中融鼎新定增组合类私募证券投资基金',
                     '北京中融长远投资管理有限公司',
                     '济南北安投资有限公司', '鼎鸿新三板优选基金4号', '恒天创投（西安）资产管理有限公司', '深圳市金地盈投资有限公司', '湖州景福融泰股权投资合伙企业（有限合伙）',
                     '中融鼎新-鼎融嘉盈7号证券投资基金', '中融鼎新-鼎融金牛16号定增投资基金', '中融宝晟-晟世融安4号基金', '深圳前海亚特兰图投资中心（有限合伙）',
                     '中融鼎新-金翼海外资产收购基金', '中融鼎新-国富稳隆2号股权投资基金', '上海稳慧投资合伙企业（有限合伙）', '上海赟峰投资合伙企业（有限合伙）',
                     '深圳市中融鼎兴投资合伙企业（有限合伙）', '中融大有资本投资管理有限公司', '融贵（天津）投资合伙企业（有限合伙）', '宁波梅山保税港区琨合股权投资合伙企业（有限合伙）',
                     '宁波明喻投资管理有限公司', '重庆冠铭峰麟投资中心（有限合伙）', '北京鼎金盛世投资管理有限公司', '中融鼎新-吉酒股权投资基金', '中融鼎新-融景安太医院并购基金',
                     '中融鼎新-鼎融盛源7号契约型基金', '中融鼎诚-康信1号股权投资基金', '中融鼎新双子证券投资基金1号', '宁波嘉凯融晟投资合伙企业（有限合伙）',
                     '中融鼎新-鼎融利丰2号契约型基金',
                     '中融稳达-慧誉1号契约型投资基金', '达孜中融鼎盛资产管理有限公司', '上海沣壹企业管理合伙企业（有限合伙）', '深圳鑫贵赢海润投资合伙企业（有限合伙）',
                     '宁波梅山保税港区视享股权投资合伙企业（有限合伙）', '中融稳达-春秋永乐融融2号基金', '中融鼎新-启点1号天使投资基金', '芜湖中投华阳投资中心（有限合伙）',
                     '中融稳达-稳达赢盈1号投资基金', '中融资产-精诚信诺23号特定客户资产管理计划', '上海悦乾创业投资合伙企业（有限合伙）', '上海广熹企业管理中心（有限合伙）',
                     '天津泰祥投资管理有限公司', '天津中融鼎晟股权投资合伙企业（有限合伙）', '中融鼎新-融申通新三板基金1号', '中融鼎新-鼎金价值5号证券投资私募基金',
                     '中融鼎新-鼎融利丰47号私募基金', '上海瑞扬舟投资管理中心（有限合伙）', '天津融衡股权投资合伙企业（有限合伙）', '鼎鸿新三板优选基金1号',
                     '无锡市恒仁源产业投资合伙企业（有限合伙）', '重庆卓智鑫盈投资中心（有限合伙）', '北京艾斯匹威资产管理有限公司', '深圳中融哈投纾困投资合伙企业（有限合伙）',
                     '上海迦晟投资管理有限公司', '嘉兴如嬴投资合伙企业（有限合伙）', '珠海融悟股权投资合伙企业（有限合伙）', '上海道舍投资合伙企业（有限合伙）',
                     '中融鼎新-融稳达5号新三板投资基金',
                     '上海汉将投资有限公司', '西藏融鼎宸富创业投资管理有限公司', '如皋中融锦时产业投资发展基金中心（有限合伙）', '北京鼎茂悦府企业管理中心（有限合伙）',
                     '上海钰阙企业管理合伙企业（有限合伙）', '深圳融利安盈企业管理中心（有限合伙）', '湖州鼎岳股权投资合伙企业（有限合伙）', '成都融金鼎晟企业管理中心',
                     '温州市金兴名晨投资中心（有限合伙）', '中融稳达-稳达赢盈6号私募基金', '中融鼎新-鼎融汇富2号投资基金', '中融鼎新-海外兴1号投资基金',
                     '中融鼎新-鼎融利丰20号工业40产业并购基金', '上海融钰企业管理中心（有限合伙）', '中融鼎新-鼎融汇富10号契约型基金', '中融鼎新-融巽晟财契约型基金',
                     '中融资产-稳盈7号资产管理计划', '上海推熹企业管理中心（有限合伙）', '中融鼎新-融鑫稳赢契约型基金', '北京中融汇富投资管理有限公司',
                     '中融资产-新湖赋成量化5号特定客户资产管理计划', '上海清芃投资咨询合伙企业（有限合伙）', '珺敦3号股权投资私募基金', '广西通达天和投资管理有限公司',
                     '中融鼎新-嘉选泽汇契约型基金', '宁波梅山保税港区融辉股权投资合伙企业（有限合伙）', '深圳市华融融庄投资企业（有限合伙）', '天津华溢管理咨询合伙企业（有限合伙）',
                     '中融鼎新-中林1号私募基金', '深圳东凌合富基金企业（有限合伙）', '天津永兆投资合伙企业（有限合伙）', '上海珏锦投资管理中心（有限合伙）',
                     '中融鼎新-鼎融嘉盈4号证券投资基金',
                     '中融基金-增金1号资产管理计划', '哈尔滨国际信托投资公司劳务服务公司', '鼎鸿新三板优选基金2号', '上海鼎彝投资中心（有限合伙）', '中融国际资本管理有限公司',
                     '天津融辉鼎泰股权投资合伙企业（有限合伙）', '中融鼎新-天平3号基金', '重庆昊睿融晟投资中心（有限合伙）', '中融鼎新-鼎融利丰7号基金',
                     '深圳市中融汇富投资管理有限公司',
                     '上海贵势投资管理有限公司', '杭州中铁光大舜壹投资合伙企业（有限合伙）', '上海金汇投资实业有限公司武汉公司', '中融鼎新-融稳达3号新三板投资基金',
                     '中融鼎新-宏观对冲1号证券投资基金', '上海金牧源投资合伙企业（有限合伙）', '中融鼎新-兴鹰1号新三板影视投资基金', '中融资产-稳盈11号增强型债券资产管理计划',
                     '湖州腾创股权投资合伙企业（有限合伙）', '中融鼎新-鼎融汇富5号基金', '睿冠（上海）投资合伙企业（有限合伙）', '中融鼎新-鼎金价值6号证券投资私募基金',
                     '深圳前海岩峰投资中心（有限合伙）', '中融鼎兴-兴鹰3号新能源私募基金', '铜陵市中融大有天源创业投资有限合伙企业（有限合伙）', '上海辔格瑟斯投资中心（有限合伙）',
                     '广州京瑚企业管理服务合伙企业（有限合伙）', '中融鼎新-鼎融盛源5号基金', '中融鼎新天狮股权投资基金1号', '深圳前海图蓝投资中心（有限合伙）',
                     '国机财务有限责任公司',
                     '嘉兴星旭股权投资合伙企业（有限合伙）', '宁波梅山保税港区金益华嘉股权投资基金合伙企业（有限合伙）', '中融鼎新-天平2号基金',
                     '中融资产-稳盈5号增强型债券专项资产管理计划',
                     '嘉兴融展投资合伙企业（有限合伙）', '杭州江干区基础设施投资管理合伙企业（有限合伙）', '鼎金价值2号进取型证券投资基金', '上海佰屹投资中心（有限合伙）',
                     '中鼎鸿道证券投资基金',
                     '上海中融融欧二期股权投资基金合伙企业（有限合伙）', '中融鼎新-鼎融嘉盈6号投资基金', '上海鼎芈投资中心（有限合伙）', '睿涞投资管理（上海）有限公司',
                     '中融鼎新-白羊新三板投资基金1号', '达孜鼎诚-新文化产业1号契约型私募基金', '中融鼎新-盈润47号基金', '中融掌运星资产管理有限公司',
                     '重庆昊海汇嘉投资中心（有限合伙）',
                     '天津融辉鼎合股权投资合伙企业（有限合伙）', '宁波梅山保税港区腾海锦钰股权投资合伙企业（有限合伙）', '上海鼎昭投资中心（有限合伙）',
                     '中融鼎新-资舟观复1号私募投资基金',
                     '中融资产-赋成量化3号特定客户资产管理计划', '中融-盈润33号新三板投资基金', '北京恒鑫融汇投资管理有限公司', '融尊（天津）投资合伙企业（有限合伙）',
                     '西藏融鼎坤企业管理有限合伙企业（有限合伙）', '中融鼎新-鼎融汇富20号健康产业基金', '嘉兴润泓中股权投资合伙企业（有限合伙）',
                     '宁波梅山保税港区中融聚坤投资合伙企业（有限合伙）',
                     '恒天创投投资管理有限公司', '恒美（黄冈）文化旅游投资有限公司', '天津融辉鼎乾股权投资合伙企业（有限合伙）', '上海一翀投资中心（有限合伙）',
                     '中融鼎新-鼎融汇富7号契约型基金',
                     '深圳前海中融海润投资管理有限公司', '天津中融国林投资管理中心（有限合伙）', '嘉兴融勤投资合伙企业（有限合伙）', '中融鼎新-恒睿1号股权投资基金',
                     '上海丹佳投资合伙企业（有限合伙）', '北京恒智欣晟投资中心（有限合伙）', '大连蕴诚投资管理中心（有限合伙）', '上海瑞扬投资管理有限公司',
                     '上海康昳投资中心（有限合伙）',
                     '国机资产管理有限公司', '江阴潮平阔岸投资中心（有限合伙）', '中融资产-稳盈9号资产管理计划', '中融鼎新-鼎融嘉盈5号证券投资基金', '中融鼎新射手打新基金1号',
                     '中融鼎新-汇福1号新三板基金', '北京中融永年资产管理有限公司', '重庆卓智恒远投资中心（有限合伙）', '湖州融瑞投资管理有限公司', '中融鼎新-鼎融利丰11号基金',
                     '中融鼎新-博盈1号新三板定增基金', '湖州融源瑞康股权投资合伙企业（有限合伙）', '宁波梅山保税港区缘宝股权投资合伙企业（有限合伙）',
                     '上海彤观投资管理合伙企业（有限合伙）',
                     '深圳市融鼎晖投资管理中心（有限合伙）', '嘉兴星诺股权投资合伙企业（有限合伙）', '深圳前海长洋股权投资基金（有限合伙）', '上海湘平投资中心（有限合伙）',
                     '北京融钨国鼎资产管理合伙企业（有限合伙）', '成都融金鼎晟企业管理中心（有限合伙）', '北京中融永年经济咨询合伙企业（有限合伙）', '北京中融商骥资本投资中心（有限合伙）',
                     '天津融辉鼎利企业管理合伙企业（有限合伙）', '新余童年投资合伙企业（有限合伙）', '上海瑞叱企业管理中心（有限合伙）', '北京汇信荣硕资产管理合伙企业（有限合伙）',
                     '新余悦童投资合伙企业（有限合伙）', '芜湖中融合宝投资中心（有限合伙）', '湖州景福华芯股权投资合伙企业（有限合伙）', '达孜县合十投资中心（有限合伙）',
                     '北京中融天瑞投资中心（有限合伙）', '宁波梅山保税港区迦银投资合伙企业（有限合伙）', '上海奥高管理咨询合伙企业（有限合伙）', '北京中融世鸿资本投资企业（有限合伙）',
                     '北京中融融泽资产管理有限公司', '青岛海联启航股权投资合伙企业（有限合伙）', '嘉兴融灏股权投资合伙企业（有限合伙）', '永瑞（天津）投资合伙企业（有限合伙）',
                     '上海岚唐投资管理合伙企业（有限合伙）', '湖州鼎齐股权投资合伙企业（有限合伙）', '天津融辉鼎坤股权投资合伙企业（有限合伙）', '北京戈壁瀚海资产管理合伙企业（有限合伙）',
                     '深圳市中融金盛股权投资基金（有限合伙）', '宁波梅山保税港区睿壹投资合伙企业（有限合伙）', '深圳中扶绿色农业产业投资合伙企业（有限合伙）',
                     '上海计兮投资中心（有限合伙）',
                     '上海望朴投资管理合伙企业（有限合伙）', '北京中融证金投资中心（有限合伙）', '成都融金瑞扬企业管理中心（有限合伙）', '深圳中扶旅游产业投资合伙企业（有限合伙）'],
    '上海汽车工业（集团）有限公司': ['上海宝鼎投资管理有限公司', '平潭综合实验区宝鼎灵越股权投资合伙企业（有限合伙）', '井冈山芯扬股权投资合伙企业（有限合伙）',
                       '井冈山芯航股权投资合伙企业（有限合伙）',
                       '嘉兴鼎芯股权投资合伙企业（有限合伙）', '井冈山芯拓股权投资合伙企业（有限合伙）', '上海宝鼎爱平投资合伙企业（有限合伙）', '嘉兴海橙投资合伙企业（有限合伙）',
                       '嘉兴芯粹投资合伙企业（有限合伙）', '井冈山乾芯股权投资合伙企业（有限合伙）', '平潭综合实验区宝鼎嘉越股权投资合伙企业（有限合伙）',
                       '井冈山嘉芯股权投资合伙企业（有限合伙）',
                       '井冈山市宝鼎顺芯股权投资合伙企业（有限合伙）', '井冈山博芯股权投资合伙企业（有限合伙）', '青岛海澳芯科私募股权投资基金合伙企业（有限合伙）',
                       '井冈山芯利股权投资合伙企业（有限合伙）', '嘉兴芯通投资合伙企业（有限合伙）', '井冈山芯睿股权投资合伙企业（有限合伙）', '嘉兴泽庞投资合伙企业（有限合伙）',
                       '上海佳芦投资咨询有限公司', '绍兴宝鼎久磊投资合伙企业（有限合伙）', '上海开弘投资管理有限公司', '溧阳爱为途篝股权投资合伙企业（有限合伙）',
                       '北京国汽智联投资管理有限公司',
                       '嘉兴颀晟投资合伙企业（有限合伙）', '常州赛可移动出行投资合伙企业（有限合伙）'], '福建省汽车工业集团有限公司': ['福建汽车投资有限公司'],
    '北京百度网讯科技有限公司': ['嘉兴小度互娱投资管理合伙企业（有限合伙）', '北京百众聚丰投资基金合伙企业（有限合伙）', '北京百慧聚鑫投资合伙企业（有限合伙）', '上弦月投资管理（北京）有限公司',
                     '嘉兴小度内容股权投资合伙企业（有限合伙）', '嘉兴小度投资管理有限公司', '北京毕威原始创新咨询中心（有限合伙）', '北京百度毕威企业管理中心（有限合伙）',
                     '三亚百川致新私募股权投资基金合伙企业（有限合伙）', '北京百致咨询管理中心（有限合伙）', '北京聚宽投资管理有限公司', '宁波梅山保税港区佰胜投资管理合伙企业（有限合伙）',
                     '宁波梅山保税港区佰毅投资管理合伙企业（有限合伙）', '北京百慧创赢投资管理有限公司', '宁波梅山保税港区百度致新资产管理有限公司',
                     '广州百度风投人工智能股权投资合伙企业（有限合伙）',
                     '北京创赢卓识咨询管理中心（有限合伙）', '聚宽全市场增强十二号私募证券投资基金', '聚宽广思对冲二号私募证券投资基金', '聚宽定制二十三号私募证券投资基金',
                     '聚宽进取中证500指数增强十号私募证券投资基金', '聚宽信淮500指数增强1号5期私募证券投资基金', '聚宽定制七号私募证券投资基金', '聚宽定制五十九号私募证券投资基金',
                     '聚宽信淮500指数增强1号私募证券投资基金', '聚宽定制六十号私募证券投资基金', '聚宽华智中证500指数增强一号私募证券投资基金', '聚宽全市场增强六号私募证券投资基金',
                     '聚宽金翼中证500指数增强2期私募证券投资基金', '聚宽定制五十号私募证券投资基金', '聚宽中证1000增强6号私募证券投资基金',
                     '聚宽安乾中证500指数增强二号私募证券投资基金',
                     '聚宽聚金增盈一号私募证券投资基金', '聚宽芙蓉一号私募证券投资基金', '聚宽定制18号私募证券投资基金', '聚宽定制三十八号私募证券投资基金',
                     '聚宽桂盈中证500指数增强一号私募证券投资基金', '聚宽信淮500指数增强1号1期私募证券投资基金', '聚宽招盈对冲私募证券投资基金', '聚宽定制六十三号私募证券投资基金',
                     '聚宽广思中证500指数增强二号私募证券投资基金', '聚宽城盈500增强私募证券投资基金', '汇富聚宽量化对冲二号私募证券投资基金', '聚宽定制瑞驰一号私募证券投资基金',
                     '聚宽定制二十四号私募证券投资基金', '聚宽信淮500指数增强D期私募证券投资基金', '聚宽招盈一号1期私募证券投资基金', '聚宽定制八号私募证券投资基金',
                     '聚宽定制四十号私募证券投资基金', '聚宽中证500指数增强二十三号私募证券投资基金', '聚宽卓越一号私募证券投资基金', '聚宽定制四十一号私募证券投资基金',
                     '聚宽定制十五号私募证券投资基金', '聚宽万盈中证500指数增强二号私募证券投资基金', '聚宽定制18号1期私募证券投资基金', '聚宽一号私募证券投资基金',
                     '聚宽对冲三号私募证券投资基金', '聚宽汇岭全市场增强私募证券投资基金', '聚宽定制三十号私募证券投资基金', '聚宽定制四十二号私募证券投资基金',
                     '聚宽中证500指数增强二十号私募证券投资基金', '聚宽定制三十六号私募证券投资基金', '聚宽定制瑞驰一号1期私募证券投资基金', '聚宽定制十号私募证券投资基金',
                     '聚宽全市场增强八号私募证券投资基金', '聚宽天成多空私募证券投资基金', '聚宽全市场增强十三号私募证券投资基金', '聚宽定制三十二号私募证券投资基金',
                     '聚宽航富中证500指数增强一号私募证券投资基金', '聚宽广思中证500指数增强五号私募证券投资基金', '聚宽定制九号私募证券投资基金',
                     '聚宽万盈中证500指数增强一号私募证券投资基金',
                     '聚宽定制五号私募证券投资基金', '聚宽中证500指数增强十五号私募证券投资基金', '聚宽航富私募证券投资基金', '聚宽广兴定制二号私募证券投资基金',
                     '聚宽信淮500指数增强1号8期私募证券投资基金', '聚宽定制中证500指数增强一号私募证券投资基金', '聚宽定制四十三号私募证券投资基金', '聚宽对冲五号私募证券投资基金',
                     '聚宽定制十六号私募证券投资基金', '聚宽定制二十六号私募证券投资基金', '聚宽定制四十五号私募证券投资基金', '聚宽进取中证500指数增强二号私募证券投资基金',
                     '聚宽广思对冲一号私募证券投资基金', '聚宽中证500指数增强二号私募证券投资基金', '聚宽天风中证500指数增强1号私募证券投资基金',
                     '聚宽中证500指数增强十九号私募证券投资基金',
                     '聚宽广兴定制二号1期私募证券投资基金', '汇富聚宽量化对冲一号私募证券投资基金', '聚宽定制二十八号私募证券投资基金', '聚宽招盈对冲A期私募证券投资基金',
                     '聚宽光远中证500指数增强一号私募证券投资基金', '聚宽安盈一号私募证券投资基金', '聚宽定制三号私募证券投资基金', '聚宽定制二十七号私募证券投资基金',
                     '聚宽广思中证500指数增强七号私募证券投资基金', '聚宽誉瑞私募证券投资基金', '聚宽信淮500指数增强B期私募证券投资基金', '聚宽定制四十八号私募证券投资基金',
                     '聚宽定制四十六号私募证券投资基金', '聚宽定制三十七号私募证券投资基金', '聚宽对冲二号私募证券投资基金', '聚宽中证500指数增强十二号私募证券投资基金',
                     '聚宽广兴定制一号私募证券投资基金', '聚宽信淮500指数增强E期私募证券投资基金', '聚宽中证500指数增强八号私募证券基金',
                     '聚宽中证500指数增强十八号私募证券投资基金',
                     '聚宽定制六十二号私募证券投资基金', '聚宽信淮500指数增强A期私募证券投资基金', '聚宽定制四十号1期私募证券投资基金', '聚宽全市场增强十一号私募证券投资基金',
                     '聚宽招盈灵活对冲1号私募证券投资基金', '聚宽广思中证500指数增强六号私募证券投资基金', '聚宽定制二十九号私募证券投资基金', '聚宽全市场增强一号私募证券投资基金',
                     '聚宽广兴定制一号1期私募证券投资基金', '聚宽信淮500指数增强1号3期私募证券投资基金', '宁波梅山保税港区百度鹏寰投资合伙企业（有限合伙）',
                     '聚宽创享一号私募证券投资基金',
                     '聚宽中证500指数增强五号私募证券投资基金', '聚宽定制二十一号私募证券投资基金', '聚宽行者全市场增强私募证券投资基金', '聚宽定制二十五号私募证券投资基金',
                     '聚宽安乾中证500指数增强三号私募证券投资基金', '聚宽中证500指数增强六号私募证券投资基金', '聚宽定制三十三号私募证券投资基金', '聚宽定制四十九号私募证券投资基金',
                     '百度鹏寰资产管理（北京）有限公司', '聚宽中证500指数增强十号私募证券投资基金', '聚宽广思中证500指数增强八号私募证券投资基金',
                     '聚宽300指数增强一号私募证券投资基金',
                     '聚宽中证1000增强二号私募证券投资基金', '聚宽华彩一号私募证券投资基金', '聚宽中证500指数增强三号私募证券投资基金', '聚宽全市场增强三号私募证券投资基金',
                     '业海通聚宽私募证券投资基金', '聚宽万和中证500指数增强一号私募证券投资基金', '聚宽信淮500指数增强C期私募证券投资基金',
                     '聚宽中证500指数增强十一号私募证券投资基金',
                     '聚宽定制十三号私募证券投资基金', '聚宽全市场增强二号私募证券投资基金', '聚宽城盈尊享1号私募证券投资基金', '聚宽汇鑫指数增强私募证券投资基金',
                     '聚宽定制六十一号私募证券投资基金', '聚宽厚德信达指数增强私募证券投资基金', '聚宽航富中证500指数增强二号私募证券投资基金', '达孜县百瑞翔创业投资管理有限责任公司',
                     '聚宽招盈一号私募证券投资基金', '聚宽广思中证500指数增强一号私募证券投资基金', '聚宽中证500指数增强九号私募证券投资基金', '聚宽对冲一号私募证券投资基金',
                     '聚宽广思中证500指数增强三号私募证券投资基金', '聚宽中证500指数增强十六号私募证券投资基金', '聚宽安乾中证500指数增强一号私募证券投资基金',
                     '聚宽天成进取三号私募证券投资基金', '聚宽财享一号中证500指数增强私募证券投资基金', '聚宽中证1000指数增强一号私募证券投资基金',
                     '聚宽信淮500指数增强1号2期私募证券投资基金', '聚宽全市场增强五号私募证券投资基金', '中文在线（天津）文化教育产业股权投资基金合伙企业（有限合伙）',
                     '成都太合三新心乐投资合伙企业（有限合伙）'],
    '重庆小康控股有限公司': ['北京中凌兴盛投资中心（有限合伙）', '太和县澜峰医疗健康产业发展基金（有限合伙）', '小康投资（美国）股份有限公司', '重庆渝安康欣企业管理合伙企业（有限合伙）'],
    '大运集团有限公司': ['山西运豪企业管理合伙企业（有限合伙）', '山西成运企业管理合伙企业（有限合伙）', '山西德运企业管理合伙企业（有限合伙）', '山西佳运企业管理合伙企业（有限合伙）'],
    '蔚来控股有限公司': ['蔚来能源投资（湖北）有限公司'],
    '广州汽车工业集团有限公司': ['广州盈锭股权投资基金管理有限公司', '深圳广证盈乾创新技术产业壹号投资基金（有限合伙）', '广东广祺伍号股权投资合伙企业（有限合伙）',
                     '广东广祺瑞海股权投资合伙企业（有限合伙）',
                     '上海星巢创业投资中心（有限合伙）', '广东广祺柒号股权投资合伙企业（有限合伙）', '广州德福股权投资基金合伙企业（有限合伙）', '广东广祺叁号股权投资合伙企业（有限合伙）',
                     '上海盈饶创业投资管理有限公司', '广州福沃德私募基金管理有限公司', '广州盈蓬投资管理有限公司', '广州盈霈投资管理有限公司',
                     '深圳前海合鑫广盈壹号汽车产业投资合伙企业（有限合伙）',
                     '广州汽车集团财务有限公司', '众诚汽车保险股份有限公司', '北京国汽智联投资管理有限公司', '广州盈越同行创业投资合伙企业（有限合伙）',
                     '广东广祺瑞庸贰号股权投资合伙企业（有限合伙）', '广东广祺中庸股权投资合伙企业（有限合伙）', '广东广祺辰途瑞芯股权投资合伙企业（有限合伙）',
                     '广东广祺辰途叁号股权投资合伙企业（有限合伙）', '广州广祺辰途创业投资合伙企业（有限合伙）', '广东广祺辰途肆号股权投资合伙企业（有限合伙）',
                     '广东广祺肆号股权投资合伙企业（有限合伙）', '台州银祺股权投资基金合伙企业（有限合伙）', '广州自缝资产管理有限公司', '广州智造创业投资企业（有限合伙）',
                     '广州广祺壹号创业投资合伙企业（有限合伙）', '广汉普智创业投资基金合伙企业（有限合伙）', '广州盈越创业投资合伙企业（有限合伙）',
                     '广东广祺瑞庸壹号股权投资合伙企业（有限合伙）',
                     '广东广祺瑞电股权投资合伙企业（有限合伙）', '广东广祺瑞宸股权投资合伙企业（有限合伙）', '广州博研汽车一号创业投资合伙企业（有限合伙）',
                     '新余广新慧通股权投资中心（有限合伙）',
                     '广祺辰途贰号股权投资（佛山）合伙企业（有限合伙）', '广东广祺铁发股权投资合伙企业（有限合伙）', '广东广祺瑞智股权投资合伙企业（有限合伙）',
                     '广东广祺瑞薪股权投资合伙企业（有限合伙）', '新余广新创电股权投资咨询中心（有限合伙）', '广东广祺瑞电贰号股权投资合伙企业（有限合伙）',
                     '郑州广祺纾困产业发展基金（有限合伙）',
                     '广东广祺瑞腾股权投资合伙企业（有限合伙）', '广东广祺陆号股权投资合伙企业（有限合伙）', '广东广祺瑞钰股权投资合伙企业（有限合伙）',
                     '广州广祺欣旗管理咨询合伙企业（有限合伙）',
                     '广东广祺亿创壹号股权投资合伙企业（有限合伙）', '共青城广祺玖璞贰壹股权投资合伙企业（有限合伙）', '广东广祺中汇壹号股权投资合伙企业（有限合伙）',
                     '广东广祺诺延壹号股权投资合伙企业（有限合伙）'],
    '比亚迪股份有限公司': ['横琴和谐鼎泰股权投资企业（有限合伙）', '深圳比亚迪电动汽车投资有限公司', '汕头市比亚迪投资管理有限公司', '深圳市比亚迪投资管理有限公司', '深圳市比亚迪光伏投资有限公司',
                  '深圳市弗迪创业投资有限公司', '汕头市比亚迪云达投资合伙企业（有限合伙）', '汕头市比亚迪云地投资合伙企业（有限合伙）'],
    '威马智慧出行科技（上海）股份有限公司': ['湖北黄冈长江威马股权投资基金管理有限公司', '湖北长江威马黄冈股权投资基金合伙企业（有限合伙）'],
    '江苏九洲投资集团有限公司': ['江苏九洲投资集团创业投资有限公司', '江苏常州武商创业投资合伙企业（有限合伙）', '常州九洲创星创业投资合伙企业（有限合伙）', '常州智慧时代投资合伙企业（有限合伙）',
                     '常州九洲众创投资合伙企业（有限合伙）', '常州九洲创投园管理有限公司'], '安徽江淮汽车集团控股有限公司': ['安徽江汽投资有限公司'],
    '力帆科技（集团）股份有限公司': ['重庆力帆资产管理有限公司'],
    '宜宾发展控股集团有限公司': ['芜湖瑞庆投资有限公司', '宜宾市高端成长型产业投资引导基金合伙企业（有限合伙）', '宜宾朗泰科技创新股权投资合伙企业（有限合伙）', '宜宾市智能终端产业基金（有限合伙）',
                     '四川省川南经济区一体化发展投资基金（有限合伙）', '宜宾康慧电子信息产业股权投资合伙企业（有限合伙）', '宜宾朗泰新兴产业投资基金（有限合伙）',
                     '北方新兴（宜宾）创业投资合伙企业（有限合伙）', '宜宾市汽车产业发展投资有限责任公司', '宜宾市源进汽车产业园合伙企业（有限合伙）',
                     '宜宾市新兴产业发展投资基金管理有限责任公司',
                     '宜宾晨道新能源产业股权投资合伙企业（有限合伙）', '宜宾市兴戎投资管理有限公司', '宜宾新一代信息产业投资基金（有限合伙）', '宜宾远瓴创业投资有限公司',
                     '宜宾临港国富新兴产业股权投资基金管理中心（有限合伙）', '宜宾市清源水务投资有限公司', '宜宾安鸿投资有限公司', '四川省宜宾五粮液集团财务有限公司',
                     '宜宾市商业银行股份有限公司',
                     '宜宾普什资产管理有限公司', '宜宾五粮液基金管理有限公司', '宜宾五粮液乡村振兴发展基金（有限合伙）', '宜宾五粮液新零售股权投资基金（有限合伙）',
                     '宜宾五粮液茶产业发展基金（有限合伙）', '宜宾人才创新创业股权投资合伙企业（有限合伙）', '宜宾市教育投资发展有限公司', '宜宾市投资集团有限责任公司',
                     '宜宾市铁路产业投资有限公司',
                     '四川联合酒类投资管理有限公司', '宜宾临港港成建设投资有限责任公司', '宜宾三江汇股权投资合伙企业（有限合伙）', '宜宾哈工股权投资合伙企业（有限合伙）',
                     '宜宾临港产业引导投资中心（有限合伙）', '四川远瓴产业投资集团有限公司', '宜宾临港汇智新兴产业股权投资中心（有限合伙）', '宜宾临港新兴产业创投基金（有限合伙）',
                     '四川港荣美成投资发展集团有限公司', '宜宾临港投资建设集团有限公司', '宜宾市科教产业投资集团有限公司', '宜宾市新兴产业投资集团有限公司',
                     '宜宾市国资产业投资合伙企业（有限合伙）',
                     '宜宾市三江城市投资发展有限公司', '宜宾川红投资管理有限公司', '宜宾市城市和交通建设投资集团有限公司', '四川新宜建设投资集团有限公司',
                     '四川港荣投资发展集团有限公司',
                     '四川蜀南文化旅游健康产业投资集团有限公司', '宜宾港信资产管理有限公司', '宜宾五商股权投资基金（有限合伙）', '港信壹号私募投资基金', '宜宾三江投资建设集团有限公司',
                     '宜宾市汽车产业投资基金（有限合伙）'],
    '奇瑞汽车股份有限公司': ['万瀚投资管理（上海）有限公司', '芜湖奇瑞资本管理有限公司', '芜湖市瑞丞战新产业贰号基金合伙企业（有限合伙）', '芜湖兴瑞资产管理有限公司',
                   '芜湖同瑞投资中心（有限合伙）',
                   '合肥瑞丞私募基金管理有限公司', '上海瑞尚投资管理有限公司'],
    '浙江吉利控股集团有限公司': ['上海吉利兆圆国际投资有限公司', '北京吉利万源国际投资有限公司', '吉利（天津）私募基金管理有限公司', '宁波吉利蓝色计划企业管理合伙企业（有限合伙）',
                     '湖北吉沄长江投资管理有限公司', '成都厚同龙雏智能产业投资基金合伙企业（有限合伙）', '上海厚同瑞吉股权投资管理合伙企业（有限合伙）',
                     '宁波易捷股权投资基金管理合伙企业（有限合伙）',
                     '浙江众尖投资有限公司', '北京吉利凯盛国际投资有限公司', '成都兆圆新能源汽车投资有限公司', '北京亦杭万源国际投资有限公司', '浙江厚同股权投资管理有限公司',
                     '沃尔沃汽车（亚太）投资控股有限公司', '湖北吉沄长江产业基金合伙企业（有限合伙）', '西安吉祥汽车产业合伙企业（有限合伙）'],
    '小米科技有限责任公司': ['上海骄锃企业管理咨询合伙企业（有限合伙）', '北京睿创投资管理中心（有限合伙）', '北京小米智造股权投资基金合伙企业（有限合伙）', '天津金米投资合伙企业（有限合伙）',
                   '天津捌米企业管理合伙企业（有限合伙）', '天津柒米企业管理合伙企业（有限合伙）', '小米产业投资管理有限公司', '湖北小米长江产业投资基金管理有限公司',
                   '天津玖米企业管理合伙企业（有限合伙）', '天津拾肆米企业管理合伙企业（有限合伙）', '天津拾伍米企业管理合伙企业（有限合伙）', '天津贰米企业管理合伙企业（有限合伙）',
                   '天津陆米企业管理合伙企业（有限合伙）', '天津拾米企业管理合伙企业（有限合伙）', '天津众米企业管理合伙企业（有限合伙）', '天津叁米企业管理合伙企业（有限合伙）',
                   '天津拾贰米企业管理合伙企业（有限合伙）', '海南极目创业投资有限公司', '上海籽月企业管理咨询合伙企业（有限合伙）', '湖北嘉月股权投资合伙企业（有限合伙）',
                   '天津肆米企业管理合伙企业（有限合伙）', '天津金星创业投资有限公司', '天津拾壹米企业管理合伙企业（有限合伙）', '重庆小米创业投资有限公司',
                   '天津伍米企业管理合伙企业（有限合伙）',
                   '小米私募股权基金管理有限公司', '天津拾叁米企业管理合伙企业（有限合伙）', '瀚星创业投资有限公司', '天津壹米企业管理合伙企业（有限合伙）',
                   '湖北小米长江产业基金合伙企业（有限合伙）'], '浙江大华技术股份有限公司': ['宁波华晏创玺创业投资合伙企业（有限合伙）', '浙江大华投资管理有限公司'],
    '江铃汽车集团有限公司': ['南昌市鼎沃投资管理中心（有限合伙）', '南昌市鼎皓投资管理中心（有限合伙）', '南昌市鼎富基金管理有限公司', '深圳华义鼎新股权投资管理有限公司',
                   '启赋安泰（常州）新材料产业基金合伙企业（有限合伙）', '常州悦石科泰思投资合伙企业（有限合伙）', '南昌市鼎嘉投资管理中心（有限合伙）', '南昌市青英投资基金（有限合伙）',
                   '南昌市鼎瑞投资管理中心（有限合伙）', '北京国汽智联投资管理有限公司', '江铃汽车集团财务有限公司', '江铃汽车集团上海股权投资有限公司', '南昌市江铃鼎盛投资管理有限公司',
                   '南昌市鼎治汽车产业投资管理中心（有限合伙）', '上海信铃福元投资合伙企业（有限合伙）'], '江苏金彭集团有限公司': ['徐州盛彭新能源产业发展合伙企业（有限合伙）'],
    '江苏悦达集团有限公司': ['陕西悦达汉唐投资有限公司', '江苏悦达耀源矿业投资有限公司', '南京捷仁股权投资合伙企业（有限合伙）', '杭州维思投资合伙企业（有限合伙）',
                   '杭州捷乘投资管理合伙企业（有限合伙）',
                   '嘉兴天枢海容创业投资合伙企业（有限合伙）', '苏州工业园区启明融智创业投资合伙企业（有限合伙）', '苏州启明融合创业投资合伙企业（有限合伙）',
                   '盐城悦善元兴股权投资合伙企业（有限合伙）',
                   '荆州善达产业投资基金（有限合伙）', '盐城道基富汇投资合伙企业（有限合伙）', '悦达醴泉投资管理（上海）有限公司', '北京紫荆华融股权投资有限公司',
                   '北京金信华创股权投资中心（有限合伙）', '江苏悦达文化产业基金一期（有限合伙）', '江苏悦新文化产业投资有限公司', '江苏悦达善达股权投资基金管理有限公司',
                   '江苏悦达金泰基金管理有限公司', '宁波悦达善达投资管理有限公司', '江苏悦达私募基金管理有限公司', '江苏悦达善达紫荆沿海股权投资母基金二期（有限合伙）',
                   '江苏悦达善达紫荆沿海股权投资母基金一期（有限合伙）', '江苏悦达中小企业绿色发展创业投资基金（有限合伙）', '江苏达泰悦达大数据创业投资基金（有限合伙）',
                   '盐城市中科盐发创业投资企业（有限合伙）', '盐城悦宏共赢私募投资基金（有限合伙）', '盐城悦宏文化基金管理中心（有限合伙）', '盐城鹰石如观股权投资合伙企业（有限合伙）',
                   '江苏悦达创业投资有限公司', '盐城阿特斯新能源产业投资基金（有限合伙）', '盐城中韩产业园世曼凯投资基金（有限合伙）', '盐城悦善元达投资合伙企业（有限合伙）',
                   '盐城善达元丰股权投资合伙企业（有限合伙）', '上海金浦文创股权投资基金合伙企业（有限合伙）', '江苏悦达百特能源投资有限公司', '上海申丰投资发展有限公司',
                   '杭州维思捷鼎股权投资合伙企业（有限合伙）', '杭州捷麟投资管理合伙企业（有限合伙）', '财通基金-悦达醴泉定增3号资产管理计划', '悦达醴泉-悦顺3号证券投资基金',
                   '悦达善达收益互换定增一号基金', '悦达醴泉悦昇4号私募基金', '悦达醴泉悦昇3号私募基金', '大成创新资本-招商银行-悦达醴泉资产管理计划',
                   '华润信托-恒远悦达1期集合资金信托计划',
                   '盐城盐新善达新能源汽车产业投资基金（有限合伙）', '中海信托-悦达醴泉悦昇1号集合资金信托计划', '湖南悦达发展投资有限公司', '江苏宁石投资有限公司',
                   '中海信托-悦达醴泉悦顺6号集合资金信托计划', '北京歌萨尔金投资管理有限责任公司', '盐城悦善产业投资基金（有限合伙）', '江苏悦达集团财务有限公司',
                   '国联安-鑫享-悦达醴泉1号',
                   '外贸信托-悦达醴泉1期证券投资集合资金信托计划', '盐城元润新能源产业投资基金（有限合伙）', '悦达善达定增基金八号', '悦达善达定增私募基金十号', '悦达善达定增基金六号',
                   '嘉兴悦达悦顺成长股权投资基金合伙企业（有限合伙）', '悦达醴泉-悦顺1号新三板投资基金', '宁波悦善永通投资合伙企业（有限合伙）', '宁波悦善广成私募基金',
                   '悦达醴泉-悦享1号投资基金',
                   '悦达善达定增基金七号', '银华聚泉收益分级资产管理计划', '悦达善达定增私募基金九号', '悦达善达定增基金二号', '银华悦达醴泉1号收益分级资产管理计划',
                   '悦达善达定增结构化私募基金十一号', '江苏悦达健康投资控股有限公司', '国投信托雨燕1号-悦达醴泉1期集合资金信托计划', '嘉兴元康善达创业投资合伙企业（有限合伙）',
                   '盐城大丰区天城产业投资基金（有限合伙）', '悦达醴泉强债1号私募证券投资基金', '盐城市城南新区大数据产业创投基金（有限合伙）', '东吴基金润享富通50号资产管理计划',
                   '盐城经济技术开发区光谷产业投资基金（有限合伙）', '悦达醴泉-悦顺4号私募投资基金', '悦达醴泉悦顺5号私募证券投资基金', '国投信托雨燕2号-悦达醴泉2期单一资金信托',
                   '农银汇理资产-悦达醴泉债券1号资产管理计划', '悦达善达定增基金一号', '悦达善达睿康投资基金', '阜宁县悦阜特色产业投资基金（有限合伙）',
                   '盐城大丰区丰泰新兴产业投资基金（有限合伙）',
                   '华润信托-恒远悦达2期集合资金信托计划', '银华悦达醴泉2号收益分级资产管理计划', '盐城经济技术开发区东方金泰高新技术创投基金（有限合伙）', '悦达善达睿胜投资基金',
                   '江苏中韩盐城产业园投资有限公司', '盐城塞力斯善达医疗健康产业一号投资基金（有限合伙）', '建湖县宏创新兴产业基金（有限合伙）',
                   '外贸信托-丽天4号集合资金信托计划第1期信托单元',
                   '悦达善达睿利投资基金', '盐城经济技术开发区元嘉半导体产业投资基金（有限合伙）', '江苏悦达沿海股权投资中心有限公司', '悦达醴泉悦昇2号私募投资基金',
                   '国联安-悦达醴泉2号资产管理计划', '国联安-悦达醴泉3号资产管理计划', '悦达醴泉-悦顺2号证券投资基金', '东吴基金润享富通51号资产管理计划',
                   '杭州捷真投资管理合伙企业（有限合伙）', '天津捷元资产管理合伙企业（有限合伙）', '南京捷隆股权投资合伙企业（有限合伙）', '盐城维思捷信股权投资基金（有限合伙）',
                   '杭州捷逸股权投资合伙企业（有限合伙）', '南京捷之股权投资合伙企业（有限合伙）', '盐城中韩一号投资基金（有限合伙）', '杭州捷荣投资管理合伙企业（有限合伙）',
                   '盐城悦达新兴产业投资基金（有限合伙）', '深圳善达煜明投资合伙企业（有限合伙）', '盐城经济技术开发区韩风名城投资基金（有限合伙）',
                   '盐城经济技术开发区步凤港投资基金（有限合伙）'],
    '中国第一汽车集团有限公司': ['南京创熠一旗力合新技术创业投资合伙企业（有限合伙）', '鼎佳汽车私募基金管理（北京）有限公司', '鑫安汽车保险股份有限公司', '红旗投资管理（吉林）有限公司',
                     '一汽非洲投资有限公司',
                     '一汽财务有限公司', '北京国汽智联投资管理有限公司', '一汽资本控股有限公司', '南京领行股权投资合伙企业（有限合伙）', '南京领行共勤股权投资有限公司',
                     '吉林省红旗智网新能源汽车基金投资管理中心（有限合伙）', '一汽股权投资（天津）有限公司'],
    '中国长安汽车集团有限公司': ['中国长安汽车集团深圳投资有限公司', '中国长安汽车集团合肥投资有限公司', '中国长安汽车集团杭州投资有限公司', '万友汽车投资有限公司',
                     '中国长安汽车集团合肥投资有限公司高新区长福分公司'],
    '东风汽车集团有限公司': ['襄阳东博新能源汽车二期基金合伙企业（有限合伙）', '襄阳东风汉江新能源汽车产业基金合伙企业（有限合伙）', '襄阳东风聚通投资基金合伙企业（有限合伙）', '上海嘉华投资有限公司',
                   '东风汽车投资（武汉）有限公司', '南京领行股权投资合伙企业（有限合伙）', '南京领行共勤股权投资有限公司', '北京国汽智联投资管理有限公司',
                   '东风交银辕憬汽车产业股权投资基金（武汉）合伙企业（有限合伙）', '东风汽车财务有限公司', '深圳智网私募股权基金管理有限公司', '中金知行（武汉）产业基金合伙企业（有限合伙）',
                   '宁波博观诚远股权投资合伙企业（有限合伙）', '辕憬（武汉）投资管理有限公司', '信之风（武汉）股权投资基金合伙企业（有限合伙）',
                   '信之风（武汉）私募基金管理合伙企业（有限合伙）',
                   '武汉东风鸿泰投资管理有限公司', '上海科泰投资有限公司', '信风投资（上海）有限公司', '东风资产管理有限公司'],
    '天际汽车科技集团有限公司': ['绍兴新咖投资管理合伙企业（有限合伙）', '绍兴原拓投资管理有限公司'],
    '广东小鹏汽车科技有限公司': ['广州小鹏汽车投资有限公司', '肇庆小鹏新能源投资有限公司', '广州小鹏汽车投资咨询合伙企业（有限合伙）', '广州鲲鹏创新投资合伙企业（有限合伙）',
                     '广州鲲鹏科创一号创业投资合伙企业（有限合伙）'],
    '海马投资集团有限公司': ['海田投资咨询服务有限公司', '深圳海马第一基金管理有限公司', '深圳海马第二基金管理有限公司', '深圳海马第三基金管理有限公司', '海保人寿保险股份有限公司',
                   '海马（上海）投资有限公司', '上海一雁投资有限公司', '上海兰涛投资有限公司', '海马（深圳）资本管理有限公司', '上海旺海投资有限公司',
                   '长兴海马股权投资合伙企业（有限合伙）',
                   '上海文康投资有限公司', '海马财务有限公司', '新乡青雁股权投资合伙企业（有限合伙）', '上海楚君投资有限公司'],
    '北京汽车集团有限公司': ['深圳井冈山新能源投资管理有限公司', '景德镇井冈山北汽创新发展投资中心（有限合伙）', '北汽银建投资有限公司', '北京国汽智联投资管理有限公司',
                   '北京安鹏昌达资产管理有限公司',
                   '共青城行达投资管理合伙企业（有限合伙）', '深圳安鹏智慧投资基金企业（有限合伙）', '新余润芳投资管理中心（有限合伙）', '北京安鹏行远新能源私募基金管理有限公司',
                   '中投万方（北京）投资基金管理有限公司', '北京恒源永康资产管理有限公司', '北京顺源永康资产管理有限公司', '北京新宇联资产管理有限公司',
                   '中航建银航空产业股权投资（天津）有限公司',
                   '深圳市安鹏股权投资基金企业（有限合伙）', '深圳前海车联网产业投资基金（有限合伙）', '南昌市井冈山北汽一号投资管理中心（有限合伙）',
                   '深圳安鹏汽车分享股权投资企业（有限合伙）',
                   '深圳市安鹏股权投资基金管理有限公司', '山东安鹏股权投资基金管理有限公司', '廊坊安鹏股权投资基金合伙企业（有限合伙）', '合肥北汽中合汽车产业股权投资合伙企业（有限合伙）',
                   '深圳前海安鹏资本管理中心（有限合伙）', '景德镇安鹏行远产业创业投资中心（有限合伙）', '安鹏资本（深圳）有限公司', '深圳安鹏资本创新有限公司',
                   '江苏安鹏投资管理有限公司',
                   '中青北汽（南京）产业投资管理有限公司', '深圳安鹏智慧出行产业投资合伙企业（有限合伙）', '都鹏（上海）企业管理合伙企业（有限合伙）', '青岛北汽投资管理有限公司',
                   '深圳安鹏汽车轻量化一期投资中心（有限合伙）', '景德镇北投安鹏产业创业投资中心（有限合伙）', '江苏疌泉安鹏先进制造产业投资基金（有限合伙）',
                   '深圳市安鹏致远投资合伙企业（有限合伙）',
                   '深圳市安鹏道远投资合伙企业（有限合伙）', '深圳安鹏创投基金企业（有限合伙）', '青岛安鹏中熔股权投资基金合伙企业（有限合伙）', '北京汽车投资有限公司',
                   '中发联（北京）技术投资有限公司', '北京国创未来私募基金管理有限公司', '景德镇北汽安鹏新能源汽车创业投资中心（有限合伙）', '安鹏5号投资基金',
                   '井冈山北汽瑞盈1号私募投资基金',
                   '鹰潭聚鹏新能源投资中心（有限合伙）', '景德镇井冈山北汽瓷都产业投资中心（有限合伙）', '安鹏新动能二号私募股权投资基金', '安鹏新动能一号私募股权投资基金',
                   '宜春市安鹏创业股权投资中心（有限合伙）', '共青城行远壹号投资中心（有限合伙）', '九江经和城乡发展中心（有限合伙）', '苏州行达石湖产业投资合伙企业（有限合伙）',
                   '新余卓曜投资管理中心（有限合伙）', '深圳井冈山新能源投资中心（有限合伙）', '井冈山北汽（景德镇）新能源投资中心（有限合伙）', '珠海北汽华金产业股权投资基金（有限合伙）',
                   '北京安鹏行远新能源产业投资中心（有限合伙）', '北京汽车教育投资有限公司', '景德镇安鹏金磁材料创业投资中心（有限合伙）', '井冈山北汽瑞盈2号私募股权投资基金',
                   '共青城信航投资有限公司', '北京北油新源资产管理有限公司', '安鹏新三板2号投资基金', '深圳市安鹏道达投资合伙企业（有限合伙）', '深圳安鹏天使投资中心（有限合伙）',
                   '苏州安行石湖投资咨询有限公司', '北汽汇金（武汉）投资基金管理有限公司', '深圳市安鹏知行投资合伙企业（有限合伙）', '井冈山北汽瑞盈3号私募股权投资基金',
                   '深圳市博锋投资企业（有限合伙）', '海宁鑫鸿一号股权投资合伙企业（有限合伙）', '深圳安鹏创新投资基金（有限合伙）', '鹰潭欣鹏新能源投资中心（有限合伙）',
                   '靖江北汽华达汽车产业并购基金（有限合伙）', '嘉兴安鹏投资管理有限公司', '深圳安鹏融智车芯投资合伙企业（有限合伙）', '安鹏一号私募股权投资基金',
                   '广东北智车联股权投资合伙企业（有限合伙）', '深圳安鹏汽车后市场产业基金（有限合伙）', '北京海纳川投资有限公司', '安鹏二号私募股权投资基金',
                   '德州北汽汇金资产管理有限公司',
                   '九江工业产业投资中心（有限合伙）', '深圳市本源晶鸿股权投资基金企业（有限合伙）', '景德镇安鹏创联创业投资中心（有限合伙）', '南昌玖沐新世纪产业投资合伙企业（有限合伙）',
                   '北京汽车集团产业投资有限公司', '北京汽车集团财务有限公司', '景德镇安鹏汽车产业创业投资合伙企业（有限合伙）', '石家庄秦方股权投资基金中心（有限合伙）',
                   '鹰潭安鹏新能源产业发展中心（有限合伙）', '广东睿鹏智慧交通股权投资合伙企业（有限合伙）', '景德镇井冈山北汽恒远投资中心（有限合伙）',
                   '景德镇井冈山北汽联合产业投资中心（有限合伙）',
                   '深圳安鹏智达投资合伙企业（有限合伙）', '广东弘文万联壹号股权投资合伙企业（有限合伙）', '潍坊安鹏新动能转换创业投资基金合伙企业（有限合伙）',
                   '南宁市万坤空间投资中心（有限合伙）',
                   '南宁市方睿亿家企业管理中心（有限合伙）', '北京中骏安鹏投资管理中心（有限合伙）', '南宁市万倥股权投资管理中心（有限合伙）', '共青城凯易东元投资管理合伙企业（有限合伙）',
                   '清能多赢（武汉）股权投资基金管理中心（有限合伙）', '景德镇井冈山北汽顺通基建投资中心（有限合伙）', '沧州开发区北汽产业园投资中心（有限合伙）',
                   '南平金兴公共和社会资本合作投资合伙企业（有限合伙）'],
    '华晨汽车集团控股有限公司': ['民生投资信用担保有限公司', '沈阳金杯申华汽车投资有限公司', '申华东投新能源投资有限公司', '北京长翔新能源投资有限公司', '合峰（湖南）投资咨询有限公司',
                     '上海明友泓福汽车投资有限公司', '中非华晨投资有限公司', '辽宁正国投资发展有限公司', '上海华晨资产管理有限公司', '上海华安投资有限公司',
                     '陕西申华投资管理有限公司',
                     '上海华安投资有限公司云南风帆实业有限公司', '华晨汽车投资（大连）有限公司']}


class ZhPevcPoc3(ExcelBase):
    """
    招行PEVC数据POC
    """

    def __init__(self):
        super(ZhPevcPoc3, self).__init__()
        self.holder = CompanyHolder()
        self.category = CompanyCategory()
        self.alias = CompanyAlias()
        self.area = CompanyArea()
        self.ind = CompanyGBIndustry()

    def process(self, *args, **kwargs):
        result, inv_list = list(), set()
        # name_dict = dict()
        inv_dict = self.query_inv_data()
        # for node, name in name_map.items():
        #     name_dict.setdefault(name, list()).append(node)
        for name, node_list in name_dict.items():
            valid_names = set()
            for node in node_list:
                valid_names.add(node.replace("(", "（").replace(")", "）").strip())
            valid_names.add(name.replace("(", "（").replace(")", "）").strip())  # 添加集团公司
            alias_cur_dict, cur_map = self.query_alias_data(list(valid_names))
            for _, name_set in alias_cur_dict.items():
                for alias_name in name_set:
                    inv_item_list = inv_dict.get(alias_name, list())
                    if not inv_item_list:
                        continue
                    for inv_item in inv_item_list:
                        cur_name = cur_map.get(alias_name, alias_name)
                        inv_item["inv_name"] = cur_name
                        inv_item["jt_name"] = name
                        inv_cmp = inv_item["companyFullName"].replace("(", "（").replace(")", "）").strip()
                        inv_item["companyFullName"] = inv_cmp
                        inv_list.add(inv_cmp)
                        result.append(inv_item)

        gs_dict = self.query_gs_data(list(inv_list))
        ind_dict = self.ind.process(list(inv_list))
        area_dict = self.area.process(list(inv_list))
        for item in result:
            inv_cmp = item["companyFullName"]
            gs_item = gs_dict.get(inv_cmp)
            if gs_item:
                item.update(gs_item)
            ind_item = ind_dict.get(inv_cmp)
            if ind_item:
                item.update(ind_item)
            area_item = area_dict.get(inv_cmp)
            if area_item:
                item.update(area_item)
        self.save_date(result)

    def save_date(self, result):
        field_cfg = {
            'jt_name': ('集团客户名称', 0),
            'companyFullName': ('被投企业名称', 1),
            'inv_name': ('参与资本', 2),
            'investmentTurn': ('投资轮次', 3),
            'estiblishDate': ('成立日期', 4),
            'provinceName': ('所属省', 5),
            'cityName': ('所属市', 6),
            'district': ('所属区', 7),
            'ind_m': ('门类', 8),
            'ind_d': ('大类', 9),
            'ind_z': ('中类', 10),
            'updateTime': ('投资时间', 11),
        }
        self._excel_name = self.name_add_date("集团客户及下属企业投融资资讯表.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def query_gs_data(self, names):
        result = dict()
        name_str = "','".join(names)
        sql_statement = """SELECT compName, estiblishDate, regCapital from sy_cd_ms_base_gs_comp_info_new 
                WHERE dataStatus!=3 and compName in ('{}');"""
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            name = item["compName"]
            result.setdefault(name, item)
        return result

    def query_inv_data(self):
        result = dict()
        sql_statement = """SELECT id, investmentTurn, investmentName, companyFullName, updateTime from investment_event where 
        id > {} ORDER BY id ASC limit 1000;"""
        for items in self._query_sql_iter_by_id(sql_statement, "db_collect_data_143"):
            for item in items:
                try:
                    inv_name_list = json.loads(item["investmentName"] or "[]")
                except:
                    print(item["investmentName"])
                    continue
                for inv_item in inv_name_list:
                    inv_name = inv_item["orgname"]
                    result.setdefault(inv_name, list()).append(item)
        return result

    def query_alias_data(self, name_list):
        alias_cur_dict, cur_map = dict(), dict()
        for name in name_list:
            query_schema = {
                "db_name": "raw_data",
                "collection_name": "company_alias_name",
                "query_condition": {"$or": [{"alias_name": name}, {"cur_name": name}], "is_valid": True},
                "query_field": {"_id": 0, "alias_name": 1, "cur_name": 1}}
            query_result = self._data_server.call("query_item", query_schema)
            if query_result:
                for item in query_result:
                    cur_name = item.get("cur_name")
                    alias_name = item.get("alias_name")
                    cur_map.setdefault(alias_name, cur_name)

                    alias_cur_dict.setdefault(name, set())
                    alias_cur_dict[name].add(cur_name)
                    alias_cur_dict[name].add(alias_name)
            else:
                alias_cur_dict.setdefault(name, set())
                alias_cur_dict[name].add(name)

            query_schema = {
                "db_name": "raw_data",
                "collection_name": "investor_sname_info",
                "query_condition": {"cname": name},
                "query_field": {"cname": 1, "sname": 1}}
            query_result = self._data_server.call("query_item", query_schema)
            for item in query_result:
                cname = item["cname"]
                sname = item["sname"]
                cur_map.setdefault(sname, cname)
                alias_cur_dict[name].add(sname)
        return alias_cur_dict, cur_map

    def query_inv_cmp(self, name):
        sql_statement = f"""select entName from sy_cs_me_trad_inv_event_flow_se where pkId in (
        select pkId from sy_cs_me_trad_inv_event_flow_se where entName='{name}' and typeCode=2 and dataStatus!=3)
         and typeCode=1 and dataStatus!=3;"""
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list

    def query_ctl_info(self, name):
        sql_statement = f"""
        SELECT ctlerName, nodeName, ctlPath, ctlRatio from sy_cd_ms_rela_ctl_info where ctlerName='{name}'"""
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    zh_pevc_poc = ZhPevcPoc3()
    zh_pevc_poc.process()
