# -*- coding: utf-8 -*-
"""
@Time ： 2023/1/16 15:00
@Auth ： liry
@File ：jt_def.py
@IDE ：PyCharm
readme:交行银行复用函数
注意：
dev:
pred:
"""


import pandas as pd
import numpy as np
import glob
import math
from datetime import datetime
import digital_huitu
pd.set_option('expand_frame_repr', False)
calc = digital_huitu.CalcData()



# 将数值列表转换成浮点数列表
def get_indication_digital(indicationValue):
    """
        将数字型字符列表转换成浮点数列表,且将空值替换成负无穷
    """
    try:
        indicationValue_list = [float(i) if abs(float(i)) >= 0 else -np.inf for i in indicationValue]
    except:
        indicationValue_list = []
        for i in indicationValue:
            if str(i) == 'nan':
                indicationValue_list.append(-np.inf)
            elif i is None:
                indicationValue_list.append(-np.inf)
            elif str(i) == 'null':
                indicationValue_list.append(-np.inf)
            else:
                try:
                    indicationValue_list.append(float(i))
                except:
                    if ',' in i:
                        indicationValue_list.append(float(str(i).replace(',', '')))
                    else:
                        indicationValue_list.append(-np.inf)
    return indicationValue_list


# 将字符串列表转换成浮点数列表
def get_indication_str(insert_list, str_score_dic, calc_lei):
    """
    将字符串列表转换成浮点数列表
    """
    indicationValue_list = []
    for value in insert_list:
        if value in str_score_dic.keys():
            indicationValue_list.append(str_score_dic[value])
        elif str(value) == "nan":
            indicationValue_list.append(0.0)
        elif str(value) == '':
            indicationValue_list.append(0.0)
        elif value is None:
            indicationValue_list.append(0.0)
        elif str(value) == 'null':
            indicationValue_list.append(0.0)
        elif value is pd.NaT:
            indicationValue_list.append(0.0)
        else:
            indicationValue_list.append(calc_lei)
    return indicationValue_list


# 设置indicationvalue的df数据上下限索引内缩
def set_df_index1(df, indexVal_value, col_name):
    min_value = math.floor(len(df) * indexVal_value / 100)
    max_value = len(df) - min_value
    mid_calc_data_list = list(df[col_name])
    for i in range(len(df)):
        try:
            if mid_calc_data_list[min_value] == mid_calc_data_list[min_value + 1]:
                if (min_value >= 0) and (min_value < max_value):
                    min_value = min_value + 1
            if mid_calc_data_list[max_value] == mid_calc_data_list[max_value - 1]:
                if min_value <= max_value - 1:
                    max_value = max_value - 1
                else:
                    continue
        except IndexError:
            continue
    return min_value, max_value

# 设置indicationvalue的df数据上下限索引外扩
def set_df_index(df, indexVal_value, col_name):
    min_value = math.ceil(len(df) * indexVal_value / 100)
    max_value = len(df) - min_value
    mid_array = np.array(sorted(list(df[col_name])))
    before_value = mid_array[min_value-1]
    after_value = mid_array[max_value]
    before_array = mid_array[mid_array < before_value]
    after_array = mid_array[mid_array > after_value]
    if len(before_array) == 0:
        b = 0
    else:
        before_max = np.max(before_array)
        b = list(np.where(np.in1d(mid_array, [before_max])))[0][-1] + 1
    if len(after_array) == 0:
        a = len(df)
    else:
        after_min = np.min(after_array)
        a = list(np.where(np.in1d(mid_array, [after_min])))[0][0]
    return b, a


# 半衰期计算
def bans_calc(insert_list, insert_value=0.5, str_dic=None):
    '''
    半衰期的计算
    传入样例： ['2021:2,2022:2,2023:0','2021:2,2022:2,2023:1','2021:2,2022:2,2023:3'], 0.5, {2: 100, 3: 60}
    输出样例： [75.0, 75.0, 135.0]
    以最近一年为基准，进行半衰期计算
    可添加衰减比例值
    '''
    result_list = []
    for i in insert_list:
        trans_dic = eval('{' + str(i) + '}')

        key_list = list(trans_dic.keys())
        key_max = max(key_list)
        power_value = 0
        bs_value = 0

        ok_number = 0
        if str_dic != None:
            for m in range(3):
                if int(trans_dic[int(key_max) - m]) in str_dic.keys():
                    t_value = str_dic[int(trans_dic[int(key_max) - m])]
                else:
                    t_value = 0
                if t_value == 0:
                    ok_number += 1
                bs_value += t_value * np.power(insert_value, power_value)
                power_value += 1
        else:
            for m in range(3):
                t_value = float(trans_dic[int(key_max) - m])
                if t_value == 0:
                    ok_number += 1
                bs_value += t_value * np.power(insert_value, power_value)
                power_value += 1
        if ok_number > 2:
            result_list.append(np.nan)  # 数量大于2按异常处理
            continue
        result_list.append(bs_value)
    return result_list

# 几何平均，因指标方法不对，待调整
def jh_mean_calc(insert_list, year_value=0):
    '''
    几何平均的计算
    传入样例： ['2021:2,2022:2,2023:0','2021:2,2022:2,2023:1','2021:2,2022:2,2023:3']
    输出样例： [0.0, 0.7071067811865476, 1.224744871391589]
    以最近一年为基准，进行几何平均
    '''
    result_list = []
    for i in insert_list:
        trans_dic = eval('{' + str(i) + '}')
        key_list = list(trans_dic.keys())
        key_max = max(key_list)
        if year_value == 0:
            key_min = min(key_list)
        else:
            key_min = max(key_list) - year_value
        out_value = np.sqrt(trans_dic[key_max] / trans_dic[key_min])
        result_list.append(out_value)
    return result_list



# boll计算
def boll_calc1(insert_list, year_value, ok_year):
    '''
        boll的计算
        传入样例：['2018:7,2019:5,2020:0,2021:1','2018:4,2019:0,2020:0,2021:7'], 4,0
        输出样例：[20, 0]
        以最近一年为基准，进行boll计算
    '''
    score_list = [20, 40, 60, 80, 100]  # boll设定分值段
    year_now = int(str(datetime.now()).split(' ')[0].split('-')[0])
    result_list = []
    for i in insert_list:
        trans_dic = eval('{' + str(i) + '}')
        if ok_year == 0:
            key_max = year_now - 1
        else:
            key_max = year_now - 2


        dta = trans_dic[key_max]  # 当前年值

        if dta == 0:  # 异常填补为nan
            result_list.append(np.nan)
            continue

        calc_list = []
        ok_value = 0  # 为0数
        for m in range(1, year_value, 1):
            add_value = trans_dic[key_max - m]
            if add_value == 0:
                ok_value += 1
            if ok_value > 1:
                calc_list = []
                break
            calc_list.append(add_value)
        if len(calc_list) == 0:  # 异常填补为0
            result_list.append(np.nan)
            continue

        calc_array = np.array([float(i) for i in calc_list])
        try:  # 未有零时跳过处理
            where_value = np.where(np.in1d(calc_array, [0]))[0][0] + 1
            if where_value == 1:
                array_value = calc_array[1] + (calc_array[1] - calc_array[2])
                if array_value < 0:
                    array_value = 0
                calc_array[0] = array_value
            elif where_value == 2:
                array_value = (calc_array[0] + calc_array[2]) / 2
                if array_value < 0:
                    array_value = 0
                calc_array[1] = array_value
            elif where_value == 3:
                array_value = calc_array[1] - (calc_array[0] - calc_array[1])
                if array_value < 0:
                    array_value = 0
                calc_array[2] = array_value
        except:
            pass

        mean_value = np.mean(calc_array)
        std_value = np.std(calc_array)

        if dta < mean_value - 3 * std_value:
            boll_value = score_list[0]

        elif (dta >= mean_value - 3 * std_value) & (dta < mean_value - 1 * std_value):
            boll_value = score_list[1]
        elif (dta >= mean_value - 1 * std_value) & (dta < mean_value + 1 * std_value):
            boll_value = score_list[2]
        elif (dta >= mean_value + 1 * std_value) & (dta < mean_value + 3 * std_value):
            boll_value = score_list[3]
        else:
            boll_value = score_list[4]
        result_list.append(boll_value)
    return result_list

# boll计算
def boll_calc(insert_list, year_value, ok_year):
    '''
        boll的计算
        传入样例：['2018:7,2019:5,2020:0,2021:1','2018:4,2019:0,2020:0,2021:7'], 4,0
        输出样例：[20, 0]
        以最近一年为基准，进行boll计算
    '''
    score_list = [20, 40, 60, 80, 100]  # boll设定分值段
    year_now = int(str(datetime.now()).split(' ')[0].split('-')[0])
    result_list = []
    for i in insert_list:
        trans_dic = eval('{' + str(i) + '}')
        if ok_year == 0:
            key_max = year_now - 1
        else:
            key_max = year_now - 2


        dta = trans_dic[key_max]  # 当前年值

        if dta == 0:  # 异常填补为nan
            result_list.append(np.nan)
            continue

        calc_list = []
        ok_value = 0  # 为0数
        for m in range(1, year_value, 1):
            add_value = trans_dic[key_max - m]
            if add_value == 0:
                ok_value += 1
            if ok_value > 1:
                calc_list = []
                break
            calc_list.append(add_value)
        if len(calc_list) == 0:  # 异常填补为0
            result_list.append(np.nan)
            continue

        calc_array = np.array([float(i) for i in calc_list])
        try:  # 未有零时跳过处理
            where_value = np.where(np.in1d(calc_array, [0]))[0][0] + 1
            if where_value == 1:
                array_value = calc_array[1] + (calc_array[1] - calc_array[2])
                if array_value < 0:
                    array_value = 0
                calc_array[0] = array_value
            elif where_value == 2:
                array_value = (calc_array[0] + calc_array[2]) / 2
                if array_value < 0:
                    array_value = 0
                calc_array[1] = array_value
            elif where_value == 3:
                array_value = calc_array[1] - (calc_array[0] - calc_array[1])
                if array_value < 0:
                    array_value = 0
                calc_array[2] = array_value
        except:
            pass

        mean_value = np.mean(calc_array)
        std_value = np.std(calc_array)

        if dta < mean_value - 3 * std_value:
            boll_value = score_list[0]

        elif (dta >= mean_value - 3 * std_value) & (dta < mean_value - 1 * std_value):
            boll_value = score_list[1]
        elif (dta >= mean_value - 1 * std_value) & (dta < mean_value + 1 * std_value):
            boll_value = score_list[2]
        elif (dta >= mean_value + 1 * std_value) & (dta < mean_value + 3 * std_value):
            boll_value = score_list[3]
        else:
            boll_value = score_list[4]
        result_list.append(boll_value)
    return result_list


# skew方法
def skew_calc(df, polardb_tableName):
    trans_df = df.copy()
    he_df = pd.DataFrame()
    he_df['compcode'] = trans_df['compcode']
    he_df[f'{polardb_tableName}_before'] = trans_df[polardb_tableName]
    he_df[polardb_tableName] = trans_df[polardb_tableName]
    he_df.index = pd.Series(range(len(he_df)))

    # skew_value = abs(he_df.skew(axis=0)[polardb_tableName])  # 确认skew值
    # 手动计算行业赛道
    calc_list = list(he_df[polardb_tableName])
    std_value = np.std(calc_list)
    mean_value = np.mean(calc_list)
    print(mean_value, std_value,np.median(calc_list))
    add_value = 0
    for i in calc_list:
        add_value += np.power((i - mean_value) / std_value, 3)
    skew_value = abs(add_value / len(calc_list))

    if skew_value == 0:
        pass
    elif (skew_value > 0) & (skew_value <= 0.5):
        he_df[polardb_tableName] = pd.Series([np.sqrt(i) for i in trans_df[polardb_tableName]])
        # he_df[f'{polardb_tableName}_sqrt'] = he_df[polardb_tableName]
    elif (skew_value > 0.5) & (skew_value <= 2):
        he_df[polardb_tableName] = pd.Series([np.log(i) for i in trans_df[polardb_tableName]])
        # he_df[f'{polardb_tableName}_ln'] = he_df[polardb_tableName]
    else:
        he_df[polardb_tableName] = pd.Series([np.log10(i) for i in trans_df[polardb_tableName]])
        # he_df[f'{polardb_tableName}_log10'] = he_df[polardb_tableName]


    # kurt_value = he_df.kurt(axis=0)[polardb_tableName]  # 确认kurt值
    # 手算峰度
    mean_value0 = np.mean(he_df[polardb_tableName])
    std_value0 = np.std(he_df[polardb_tableName])
    n = len(he_df)
    kurt_value = np.sum([np.power((i - mean_value0) / std_value0, 4)
                         for i in he_df[polardb_tableName]]) / n - 3


    print('偏度：',skew_value, '峰度：',kurt_value)
    if kurt_value < -1:
        he_df[polardb_tableName] = pd.Series([np.power(i, 1 / math.e) for i in he_df[polardb_tableName]])
        # he_df[f'{polardb_tableName}_峰度值转换'] = he_df[polardb_tableName]
    result_df = pd.DataFrame()
    result_df['compcode'] = df['compcode']
    result_df = pd.merge(result_df, he_df, on='compcode', how='left')
    return result_df


# zscore标准化
def zscore_calc(df, polardb_tableName):
    '''
    z_score标准化
    传入数据为列数据大于零
    '''
    df.index = pd.Series(range(len(df)))
    calc_list = list(df[polardb_tableName])
    # print(np.mean(calc_list), np.std(calc_list))
    before_list1, zscore_list, after_list1 = calc.standard_calc(calc_list)
    # df[f'{polardb_tableName}_zscore'] = pd.Series(zscore_list)
    before_list2, logistic_list, after_list2 = calc.logistic_score(1, zscore_list)
    # df[f'{polardb_tableName}_logistic'] = pd.Series(logistic_list)
    df[polardb_tableName] = pd.Series([round(i, 4) * 100 for i in logistic_list])
    return df


# minmax标准化
def minmax_calc(df, polardb_tableName, indexVal_value, hou=0):
    '''
        minmax标准化
        传入数据为列数据大于零
    '''
    df.index = pd.Series(range(len(df)))
    calc_list = list(df[polardb_tableName])

    # 用于判断单柱数据
    data_min = min(calc_list)
    data_max = max(calc_list)

    if data_max == data_min:
        df[polardb_tableName] = pd.Series([50 for i in calc_list])
        return df

    min_value, max_value = set_df_index(df, indexVal_value, polardb_tableName)

    if hou > 0:
        min_value = 0

    df_mid = df.iloc[min_value: max_value]
    if len(df_mid) != 0:  # 防止向上取整后一条数据不留
        df_mid.index = pd.Series(range(len(df_mid)))
        log_list = list(df_mid[polardb_tableName])
        # print(sorted(log_list))
        before_list1, minmax_list, after_list1 = calc.minmax_calc(log_list)
        df_mid['minmax'] = pd.Series(minmax_list) * 100
        df_mid[polardb_tableName] = pd.Series([i/100 * (100 - 2*indexVal_value)+indexVal_value for i in df_mid['minmax']])

        del(df[polardb_tableName])
        df = pd.merge(df, df_mid, on='compcode', how='left')
        df = df.drop_duplicates()
        df.index = pd.Series(range(len(df)))

    df[polardb_tableName].iloc[:min_value] = 0
    df[polardb_tableName].iloc[max_value:] = 100
    return df



# 综合指标minmax后稀缺映射
def minmax_score_old(insert_list, fugai_value):
    before_list1, minmax_list, after_list1 = calc.minmax_calc(insert_list)
    # out_list = list(np.array(minmax_list) * 100)
    end_value = get_fg_score(fugai_value)
    out_list = [i * (100 - end_value) + end_value for i in minmax_list]
    return out_list


# 综合指标minmax后稀缺映射
def minmax_score(df, polardb_tableName, indexVal_value, fugai_value, ok_fg=1):
    df.index = pd.Series(range(len(df)))
    df['原值'] = df[polardb_tableName].copy()
    calc_list = list(df[polardb_tableName])
    if len(calc_list) == 0:
        return pd.DataFrame()
    # 用于判断单柱数据
    data_min = min(calc_list)
    data_max = max(calc_list)
    if data_max == data_min:
        df[polardb_tableName] = pd.Series([50 for i in calc_list])
        insert_list = list(df[polardb_tableName])
        before_list2, minmax_list2, after_list2 = calc.minmax_calc(insert_list)
        end_value = get_fg_score(fugai_value)
        out_list = [i * (100 - end_value) + end_value for i in minmax_list2]
        df['score'] = pd.Series(out_list)
        return df

    min_value, max_value = set_df_index(df, indexVal_value, polardb_tableName)
    df_mid = df.iloc[min_value: max_value]
    if len(df_mid) != 0:  # 防止向上取整后一条数据不留
        df_mid.index = pd.Series(range(len(df_mid)))
        log_list = list(df_mid[polardb_tableName])
        before_list1, minmax_list, after_list1 = calc.minmax_calc(log_list)
        df_mid['minmax'] = pd.Series(minmax_list, index=df_mid.index)
        df_mid['minmax'] = pd.Series(minmax_list) * 100
        df_mid[polardb_tableName] = pd.Series(
            [i / 100 * (100 - 2 * indexVal_value) + indexVal_value for i in df_mid['minmax']])

        del (df[polardb_tableName])
        try:
            df = pd.merge(df, df_mid, on='compcode', how='left')
        except:  #针对行业赛道合并使用
            df = pd.merge(df, df_mid, on='bigCode', how='left')
        df = df.drop_duplicates()
        df.index = pd.Series(range(len(df)))
    df[polardb_tableName].iloc[:min_value] = 0
    df[polardb_tableName].iloc[max_value:] = 100

    if ok_fg == 1:
        insert_list = list(df[polardb_tableName])
        max_i = np.max(insert_list)
        end_value = get_fg_score(fugai_value)
        out_list = [i * (max_i - end_value) / 100 + end_value for i in insert_list]
        df['score'] = pd.Series(out_list)
    else:
        df['score'] = df[polardb_tableName].copy()
    return df


# 算术平均法计算得到的权重向量
def cal_weight_by_arithmetic_method(array):
    # 求矩阵的每列的和
    col_sum = np.sum(array, axis=0)
    # 将判断矩阵按照列归一化
    array_normed = array / col_sum
    # 计算权重向量
    array_weight = np.sum(array_normed, axis=1) / array.shape[0]
    # 打印权重向量
    print("算术平均法计算得到的权重向量为：\n", array_weight)
    # 返回权重向量的值
    return array_weight

# 根据权重比例表获得各sheet表内的权重分数
def get_weight_dict(bz_file, zhibiao_chinese_list):
    result_dict = {}
    for zhibiao_chinese in zhibiao_chinese_list:
        zhibiao_dic = {}
        fn = pd.read_excel(bz_file, sheet_name=zhibiao_chinese)
        zhibiao_list = list(fn.get('Unnamed: 0'))
        del (fn['Unnamed: 0'])
        for zhibiao in zhibiao_list:
            fn[zhibiao] = pd.Series(list(map(lambda x: eval(str(x)), fn.get(zhibiao))))

        fn_array = np.transpose(np.array(fn))
        weight_list = cal_weight_by_arithmetic_method(fn_array)
        for i in range(len(zhibiao_list)):
            zhibiao_dic[zhibiao_list[i]] = weight_list[i]
        result_dict[zhibiao_chinese] = zhibiao_dic
    print(len(result_dict))
    return result_dict


# 进行投射转换
def get_fg_score(fg_value):
    """根据大于零覆盖度获取覆盖度底值"""
    lin_list = [0, 0.4, 0.5, 0.7, 0.9, 1.0]
    score_list = [60, 50, 30, 10, 1]
    lin_value = 0  # 常量初始化
    for i in range(5):
        if (fg_value > lin_list[i]) & (fg_value <= lin_list[i + 1]):
            lin_value = score_list[i]
    return lin_value
