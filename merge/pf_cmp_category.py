# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/02/02
from core.excel_base import ExcelBase
from moduler.company_category import CompanyCategory


class PFCategory(ExcelBase):
    def __init__(self):
        super(PFCategory, self).__init__()
        self.category = CompanyCategory()
        self.cat_map = {
            # "研发实力": {
            #     "SYAO80000": "拥有企业重点实验室", "SYAO80001": "拥有企业重点实验室", "SYAO80002": "拥有企业重点实验室", "SYAO80003": "拥有企业重点实验室",
            #     "SYAO50000": "拥有工业设计中心", "SYAO50001": "拥有工业设计中心", "SYAO50002": "拥有工业设计中心", "SYAO50003": "拥有工业设计中心",
            #     "SYAOD0000": "拥有企业技术中心", "SYAOD0001": "拥有企业技术中心", "SYAOD0002": "拥有企业技术中心", "SYAOD0003": "拥有企业技术中心",
            #     "SYAO90000": "拥有院士专家工作站", "SYAO90001": "拥有院士专家工作站", "SYAO90002": "拥有院士专家工作站", "SYAO90003": "拥有院士专家工作站",
            #     "SYAO70000": "拥有工程技术研究中心", "SYAO70001": "拥有工程技术研究中心", "SYAO70002": "拥有工程技术研究中心",
            #     "SYAO70003": "拥有市级工程技术研究中心",
            #     "SYAOC0000": "新型研发机构", "SYAOC0001": "新型研发机构", "SYAOC0002": "新型研发机构", "SYAOC0003": "新型研发机构",
            #     "SYAOF0000": "拥有高新技术企业研究开发中心", "SYAOF0001": "拥有高新技术企业研究开发中心", "SYAOF0002": "拥有高新技术企业研究开发中心",
            #     "SYAOF0003": "拥有高新技术企业研究开发中心"},
            # "科技成果转化项目": {
            #     "SYAOA0000": "科技成果转化项目", "SYAOA0001": "科技成果转化项目", "SYAOA0002": "科技成果转化项目", "SYAOA0003": "科技成果转化项目"},
            "高新技术企业": {
                "SYAO10000": "是否高新技术企业"},
            # "科技型中小企业": {
            #     "SYAO20000": "是否科技型中小企业"},
            # "技术创新示范企业": {
            #     "SYAO30000": "是否技术创新示范企业", "SYAO30001": "是否技术创新示范企业", "SYAO30002": "是否技术创新示范企业",
            #     "SYAO30003": "是否技术创新示范企业"},
            # "专精特新小巨人企业": {
            #     "SYAO40000": "是否专精特新小巨人", "SYAO40001": "是否专精特新小巨人", "SYAO40002": "是否专精特新小巨人", "SYAO40003": "是否专精特新小巨人"},
            # "服务型制造示范企业": {
            #     "SYAOB0000": "是否服务型制造示范企业",
            #     "SYAOB0001": "是否服务型制造示范企业",
            #     "SYAOB0002": "是否服务型制造示范企业",
            #     "SYAOB0003": "是否服务型制造示范企业", },
            # "制造业单项冠军": {
            #     "SYAOG0000": "是否制造业单项冠军",
            #     "SYAOG0001": "是否制造业单项冠军",
            #     "SYAOG0002": "是否制造业单项冠军",
            #     "SYAOG0003": "是否制造业单项冠军", },
            # "政府供应商": {
            #     "SYAN30000": "是否政府供应商", },
            # "央企供应商": {
            #     "SYAN20000": "是否央企供应商", },
            # "上市公司供应商": {
            #     "SYAN10000": "是否上市公司供应商", },
        }

    def process(self, *args, **kwargs):
        name_list = self.read_excel_data(self._in_file_path + "/江西省已私募融资企业20210207Gpoc.xlsx", "江西省已私募融资企业")
        names = list({item["公司名称"].replace("(", "（").replace(")", "）").strip() for item in name_list})
        # names = ["金川集团股份有限公司"]
        result = self.query_category(names)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        for excel, data_list in result.items():
            field_cfg = self.gen_field_cfg(excel)
            excel_name = "江西_{}.xlsx".format(excel)
            self._excel_name = self.name_add_date(excel_name)
            self.save_to_excel(field_cfg, {"Sheet": data_list})
            print(f"{excel_name} finish.")

    def read_excel_data(self, file_name, sheet="Sheet"):
        data_list = self._extract_data(file_name, sheet)
        return data_list

    def gen_field_cfg(self, excel_name):
        result = {'name': ('公司名称', 0)}
        cat_map = self.cat_map[excel_name]
        for idx, v in enumerate(list(set(cat_map.values()))):
            result.update({v: (v, idx + 1)})
        return result

    def query_category(self, names):
        result = dict()
        for idx in range(0, len(names), 50):
            name_list = names[idx:idx + 50]
            cat_dict = self.category.run(name_list)
            for name in name_list:
                cat_set = set(cat_dict.get(name, dict()).get("category", list()))
                for excel_name, raw_cat_map in self.cat_map.items():
                    tmp_dict = dict()
                    tmp_dict["name"] = name
                    result.setdefault(excel_name, list())
                    white_cat = set(raw_cat_map.keys())
                    tmp_result_cat = white_cat & cat_set
                    if tmp_result_cat:
                        for tmp_cat in tmp_result_cat:
                            cat_zh_name = raw_cat_map[tmp_cat]
                            tmp_dict[cat_zh_name] = "是"
                    for n in set(raw_cat_map.values()):
                        if n not in tmp_dict:
                            tmp_dict[n] = "否"
                    result[excel_name].append(tmp_dict)
        return result


if __name__ == '__main__':
    p = PFCategory()
    p.process()
