# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022-9-19
import json
from collections import defaultdict
from core.excel_base import ExcelBase
from moduler.company_holder import CompanyHolder


class PX(ExcelBase):
    def __init__(self):
        super(PX, self).__init__()
        self.cat = {"PrivateFund", "UnrecordedPrivateFundCompany",
                    "UnrecordedFund", "PrivateFundCompany",
                    "OthersCompany"}

    def process(self, *args, **kwargs):
        result = defaultdict(list)
        px_name = self.__gen_spt_info()
        num = 0
        for item in self.query_org_data():
            cat = json.loads(item["category"])
            if not (set(cat) & self.cat):
                num += 1
                continue
            px_id = item["spectrumId"]
            item["px_name"] = px_name[px_id]
            result["机构"].append(item)
        print(num)
        num = 0

        # for item in self.query_fund_data():
        #     px_id = item["spectrumId"]
        #     cat = json.loads(item["category"])
        #     if not (set(cat) & self.cat):k
        #         continue
        #     item["px_name"] = px_name[px_id]
        #     result["基金"].append(item)
        self.save_data_excel(result)

    def __gen_spt_info(self):
        result = dict()
        query_condition = dict()
        offset = None
        while True:
            if offset:
                query_condition["_id"] = {"$gt": offset}
            query_item = {
                "db_name": "raw_data",
                "collection_name": "investor_spectrum_info_v2",
                "query_condition": query_condition,
                "query_field": {
                    '_id': 1, "feature_id": 1, "spectrum_name": 1, "keyword_1": 1},
                "sort_field": [("_id", 1)],
                "limit_n": 1000}
            query_result = self._data_server.call("query_item", query_item)
            if not query_result:
                break
            for item in query_result:
                result[item["feature_id"]] = item["spectrum_name"]
            offset = query_result[-1]["_id"]
        return result

    def save_data_excel(self, result):
        field_cfg = {
            'px_name': ('谱系名称', 0),
            'cName': ('名称', 1),
            'legalRepresent': ('法定代表人', 2),
            'registerCapital': ('注册资本', 3),
            'establishDate': ('成立日期', 4),
            'regStatus': ("运作状态", 5)
        }
        self._excel_name = self.name_add_date("谱系测试数据.xlsx")
        self.save_to_excel(field_cfg, result)

    def query_org_data(self):
        sql = """
        select * from dwd_ms_cn_px_invo_info
        """
        query_schema = dict(db_key="section_data_63306", sql_statement=sql)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        print(len(result_list))
        return result_list

    def query_fund_data(self):
        sql = """
            select * from dwd_ms_cn_px_pdt_info
            """
        query_schema = dict(db_key="section_data_63306", sql_statement=sql)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        print(len(result_list))
        return result_list


if __name__ == '__main__':
    p = PX()
    p.process()
