# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022-11-1
from core.excel_base import ExcelBase


class PXCtl(ExcelBase):
    def __init__(self):
        super(PXCtl, self).__init__()
        self.schema_db = "seeyii_assets_database"
        self.schema_table = "sy_cd_ms_rela_cta_info"
        self.schema_db_key = "polardb_seeyii_assets_database"

    def process(self, *args, **kwargs):
        # url_list = self.read_excel_data(self._in_file_path + "/央企名单.xlsx", "Sheet1")
        url_list = ["上海新致软件股份有限公司", "上海复深蓝软件股份有限公司", "上海合胜计算机科技股份有限公司",
                    "神州数码系统集成服务有限公司", "福建创昱达信息技术有限公司",
                    "上海奥诃信息技术有限公司", "上海菲耐得信息科技有限公司", "上海中软华腾软件系统有限公司", "阿里云计算有限公司",
                    "上海捷鑫网络科技股份有限公司", "瑞羲（上海）信息技术有限公司",
                    "CSC科技（北京）有限"]
        alias_set, cur_map = self.query_alias_data(url_list)
        print(f"alias_set={len(alias_set)}")
        raw_data = self.query_ctl_cmp(list(alias_set), cur_map)
        field_cfg = self.import_data_cfg()
        self._excel_name = self.name_add_date("{}.xlsx".format(self.schema_table))
        self.save_to_excel(field_cfg, {"sheet1": raw_data})

    def query_alias_data(self, name_list):
        alias_set, cur_map = set(), dict()
        for i in range(0, len(name_list), 50):
            names = name_list[i: i + 50]
            query_schema = {
                "db_name": "raw_data",
                "collection_name": "company_alias_name",
                "query_condition": {"$or": [{'alias_name': {"$in": names}}, {"cur_name": {"$in": names}}],
                                    "is_valid": True},
                "query_field": {"_id": 0, "alias_name": 1, "cur_name": 1}}
            query_result = self._data_server.call("query_item", query_schema)
            if query_result:
                for item in query_result:
                    cur_name = item.get("cur_name")
                    alias_name = item.get("alias_name")
                    cur_map.setdefault(alias_name, cur_name)
                    alias_set.add(cur_name)
                    alias_set.add(alias_name)
        alias_set |= set(name_list)
        return alias_set, cur_map

    def query_ctl_cmp(self, name_list, cur_map):
        result = list()
        for i in range(0, len(name_list), 10):
            names = name_list[i: i + 10]
            sql = """
                    select * from sy_cd_ms_rela_cta_info
                    where dataStatus!=3 and ctlerName in ({})"""
            query_schema = dict(db_key="polardb_seeyii_assets_database",
                                sql_statement=sql.format(','.join([f'{item!r}' for item in names])))
            result_list = self._data_server.call("query_sql_item", query_schema) or list()
            for item in result_list:
                ctler_name = item["ctlerName"]
                item["ctlerName"] = cur_map.get(ctler_name, ctler_name)
                result.append(item)
        print(f"query_ctl_cmp={len(result)}")
        return result

    def import_data_cfg(self):
        data_dict = dict()
        item_list = self.query_table_schema()
        for idx, item in enumerate(item_list):
            data_dict.setdefault(item["column_name"],
                                 (item["column_comment"] if item["column_comment"] else item["column_name"], idx))
        return data_dict

    def query_table_schema(self):
        data_list = list()
        sql = """select column_name, column_comment 
        from information_schema.columns where table_schema ='{}' 
         and table_name = '{}';"""
        query_schema = dict(db_key=self.schema_db_key, sql_statement=sql.format(self.schema_db, self.schema_table))
        result_list = self._data_server.call("query_sql_item", query_schema)
        for item in result_list:
            data_list.append(item)
        return data_list

    def read_excel_data(self, file_name, sheet="Sheet"):
        result = set()
        data_list = self._extract_data(file_name, sheet)
        for item in data_list:
            result.add(item["name"])
        return list(result)


if __name__ == '__main__':
    p = PXCtl()
    p.process()
