# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/05/12
from core.excel_base import ExcelBase
from moduler.company_alias_name import CompanyAlias
from moduler.company_area_info import CompanyArea
from moduler.company_gb_industry import CompanyGBIndustry
from moduler.company_base_info import CompanyGS


class JHProject(ExcelBase):
    def __init__(self):
        super(JHProject, self).__init__()
        self.base = CompanyGS()
        self.gb_ind = CompanyGBIndustry()
        self.alias = CompanyAlias()
        self.area = CompanyArea()

    def process(self):
        result = list()
        raw_itemss = self.read_excel_data(self._in_file_path + "/绿色产业产品服务.xlsx", "绿色产业产品服务")
        for idx in range(0, len(raw_itemss), 100):
            raw_items = raw_itemss[idx: idx + 100]
            names = {i["公司名称"].replace("(", "（").replace(")", "）").strip() for i in raw_items}
            cur_name_dict = self.alias.run(list(names))

            items_dict = dict()
            for r_item in raw_items:
                name = r_item["公司名称"].replace("(", "（").replace(")", "）").strip()
                cur_name = cur_name_dict.get(name, name)
                items_dict.setdefault(cur_name, list())
                items_dict[cur_name].append(r_item)

            base_dict = self.base.run(list(items_dict.keys()))
            gb_dict = self.gb_ind.run(list(items_dict.keys()))
            area_dict = self.area.run(list(items_dict.keys()))
            for name, raw_item_list in items_dict.items():
                for i_raw in raw_item_list:
                    i_raw["公司名称"] = name
                    i_raw.update(gb_dict.get(name, dict()))
                    i_raw.update(base_dict.get(name, dict()))
                    i_raw.update(area_dict.get(name, dict()))
                    result.append(i_raw)
            print(idx)
        self.save_data_excel(result)

    def save_data_excel(self, result):
        field_cfg = {
            '公司名称': ('公司名称', 0),
            '命中源': ('命中源', 1),
            '关键词': ('关键词', 2),
            '标签名称': ('标签名称', 3),
            'creditCode': ('统一信用代码', 4),
            'provinceName': ('注册地（省）', 5),
            'cityName': ('注册地（市）', 6),
            'regCapital': ('注册资本（万元）', 7),
            'ind_m': ('国民经济行业门类', 8),
            'ind_d': ('国民经济行业大类', 9),
            'ind_z': ('国民经济行业中类', 10)
        }
        self._excel_name = self.name_add_date("绿色产业产品服务.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def read_excel_data(self, file_name, sheet="Sheet"):
        data_list = self._extract_data(file_name, sheet)
        return data_list


if __name__ == '__main__':
    p = JHProject()
    p.process()
