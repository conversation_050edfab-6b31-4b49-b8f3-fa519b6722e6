
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000301014',
     " with source_dat AS ( select date_format(sldate, 'yyyy-MM-dd') as eventdate,srcurl as url,compname AS eventsubject, compcode AS subjectcode,projectname,explortype,acquisitmethod,if(productscale regexp '亿立方米/年', string(bigint(substr(productscale, 0, length(productscale) - length('亿立方米/年')))*10000), regexp_replace(productscale, '万吨/年|万立方米/年', '')) as productscale from seeyii_data_house.dwd_mm_cn_prop_oaomrtp where filedate in (select max(filedate) from seeyii_data_house.dwd_mm_cn_prop_oaomrtp) and sldate is not NULL and compcode is not NULL AND datastatus !=3 AND isvalid=1 ), des_dat as ( select eventsubject,concat(concat('100120','&#&',if(explortype is null, '', explortype),'&#&','3'),'@@',concat('100121','&#&',if(productscale is null, '', productscale),'&#&','3')) as eigenvalue,subjectcode,eventdate ,'SJ000301014' as eventtype ,url ,CAST(null AS STRING) as expiredate ,concat(eventdate,'，公司将采矿权出让' ,if((projectname is null) or (projectname =''), '' ,concat('，项目名称为',projectname)) ,if((explortype is null) or (explortype =''), '' ,concat('，开采主矿种为',explortype)) ,if((acquisitmethod is null) or (acquisitmethod =''), '' ,concat('，取得方式为',acquisitmethod)) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,{fileDate} as filedate from source_dat ), ret_dat as ( select eventsubject,eigenvalue,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate, expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue,null as retrovalue,datastatus,modifytime,filedate from ret_dat ",
     map("tempview1",""),"7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000301015',
     " with source_dat AS ( select date_format(sldate, 'yyyy-MM-dd') as eventdate,srcurl as url ,compname AS eventsubject, compcode AS subjectcode,projectname,explortype,acquisitmethod from seeyii_data_house.dwd_mm_cn_prop_taottpoerio where filedate in (select max(filedate) from seeyii_data_house.dwd_mm_cn_prop_taottpoerio) and sldate is not NULL and compcode is not NULL AND datastatus !=3 AND isvalid=1 ), des_dat as ( select eventsubject,concat(concat('100122','&#&',explortype,'&#&','3')) as eigenvalue,subjectcode,eventdate ,'SJ000301015' as eventtype ,url ,CAST(null AS STRING) as expiredate ,concat(eventdate,'，公司将探矿权出让' ,if((projectname is null) or (projectname =''), '' ,concat('，项目名称为',projectname)) ,if((explortype is null) or (explortype =''), '' ,concat('，勘查矿种为',explortype)) ,if((acquisitmethod is null) or (acquisitmethod =''), '' ,concat('，取得方式为',acquisitmethod)) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,{fileDate} as filedate from source_dat ), ret_dat as ( select eventsubject,eigenvalue,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate, expiredate ,null as property1,null as property2,null as property3,null as property4,eigenvalue,null as retrovalue,datastatus,modifytime,filedate from ret_dat ",
     map("tempview1",""),"7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000106100',
     " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_ckxkz where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_ckxkz where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(validDate, 'yyyy-MM-dd') as eventdate,concat(concat('100123','&#&',mineraltype,'&#&','3')) as eigenvalue, date_format(invalidDate, 'yyyy-MM-dd') as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106100' as eventtype,url,mineralType,miningRightName from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获得采矿许可证') as a, if((miningRightName is null) or (miningRightName =''),'',concat('，矿业权名称为',miningRightName)) as b, if((mineralType is null) or (mineralType =''), '', concat('，矿种为', mineralType)) as c, '。' d from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c,d) as desc1 from add_desc_df) insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue,null as retrovalue, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
     map("tempview1",""),"7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000106101',
     " with sour_df as (select * from seeyii_data_house.dwd_me_buss_per_kcxkz where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_kcxkz where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(validDate, 'yyyy-MM-dd') as eventdate,concat(concat('100124','&#&',mineraltype,'&#&','3')) as eigenvalue, date_format(invalidDate, 'yyyy-MM-dd') as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106101' as eventtype,url,mineralType,miningRightName from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获得采矿许可证') as a, if((miningRightName is null) or (miningRightName =''),'',concat('，矿业权名称为',miningRightName)) as b, if((mineralType is null) or (mineralType =''), '', concat('，矿种为', mineralType)) as c, '。' d from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c,d) as desc1 from add_desc_df) insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue, null as retrovalue,1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
     map("tempview1",""),"7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000106102',
     " with sour_df as (select * from seeyii_data_house.dwd_mm_cn_prop_yqckqdj where filedate in (select max(filedate) from seeyii_data_house.dwd_mm_cn_prop_yqckqdj where modifytime is not null) and datastatus!=3), next_df AS ( SELECT date_format(noticeDate, 'yyyy-MM-dd') as eventdate,concat(concat('100125','&#&',`area`,'&#&','3')) as eigenvalue, date_format(invalidDate, 'yyyy-MM-dd') as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106102' as eventtype,url,prjType,projectName from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获得油气采矿权') as a, if((projectName is null) or (projectName =''),'',concat('，油气田名称为',projectName)) as b, if((prjType is null) or (prjType =''), '', concat('，项目类型为', prjType)) as c, '。' d from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c,d) as desc1 from add_desc_df) insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue, null as retrovalue,1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
     map("tempview1",""),"7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000106103',
     " with sour_df as ( select * from seeyii_data_house.dwd_mm_cn_prop_yqtkqdj where filedate in (select max(filedate) from seeyii_data_house.dwd_mm_cn_prop_yqtkqdj) and datastatus!=3 ), next_df AS ( SELECT date_format(noticeDate, 'yyyy-MM-dd') as eventdate, date_format(invalidDate, 'yyyy-MM-dd') as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106103' as eventtype,url, projectName,prjType ,concat( concat('100126','&#&',`area`,'&#&','3') ) as eigenvalue from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获得油气探矿权') as a, if((projectName is null) or (projectName =''),'',concat('，油气田名称为',projectName)) as b, if((prjType is null) or (prjType =''), '', concat('，项目类型为', prjType)) as c, '。' d from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c,d) as desc1 from add_desc_df) insert into {ku}.{tb} partition (filedate={fileDate}) select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue ,null as retrovalue,1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
     map("tempview1",""),"7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000102057',
     " with source_dat AS ( select date_format(reldate, 'yyyy-MM-dd') as eventdate,url ,compname AS eventsubject, compcode AS subjectcode,batch,productname from seeyii_data_house.dwd_me_buss_per_xnyqctg_date where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_per_xnyqctg_date) and reldate is not NULL and compcode is not NULL AND datastatus !=3 AND isvalid=1 ), des_dat as ( select eventsubject,concat(concat('100131','&#&',productname,'&#&','3')) as eigenvalue,subjectcode,eventdate ,'SJ000102057' as eventtype ,url ,CAST(null AS STRING) as expiredate ,concat(eventdate,'，公司产品获得新能源汽车推广应用推荐车型目录认证' ,if((productname is null) or (productname =''), '' ,concat('，产品名称为',productname)) ,if((batch is null) or (batch =''), '' ,concat('，批次为',batch)) , '。' ) as desc1 ,'1' as datastatus ,date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime ,{fileDate} as filedate from source_dat ), ret_dat as ( select eventsubject,eigenvalue,subjectcode,eventdate,expiredate,url, eventtype,desc1,datastatus,filedate,modifytime ,fingerId_row(concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid from des_dat ) insert into {ku}.{tb} partition (filedate) select eventid,subjectcode,eventsubject,eventtype,desc1 as `desc`, url,eventdate, expiredate ,null as property1,null as property2,null as property3,null as property4 ,eigenvalue ,null as retrovalue,datastatus,modifytime,filedate from ret_dat ",
     map("tempview1",""),"7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000106114',
     " with sour_df AS ( select * from seeyii_data_house.dwd_me_buss_core_inter_log where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_core_inter_log) and datastatus!=3 ), next_df AS ( SELECT date_format(publishDate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, compcode as subjectcode, compname as eventsubject, 'SJ000106114' as eventtype,url,batchNum,trafficField, accessCounArea ,concat( concat('100132','&#&',trafficField,'&#&','3') ) as eigenvalue from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获评国际物流运输重点联系企业') as a, if((batchNum is null) or (batchNum =''),'',concat('，批次为',batchNum)) as b, if((trafficField is null) or (trafficField =''),'',concat('，运输领域为',trafficField)) as c, if((accessCounArea is null) or (accessCounArea =''),'',concat('，通达国家和地区为',accessCounArea)) as d, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,c,d,e) as desc1 from add_desc_df) insert into {ku}.{tb} partition (filedate={fileDate}) select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue, null as retrovalue,1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
     map("tempview1",""),"7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000106116',
     " with sour_df as (select * from seeyii_data_house.dwd_me_buss_core_e_commerce where filedate in (select max(filedate) from seeyii_data_house.dwd_me_buss_core_e_commerce where modifytime is not null) and datastatus!='3'), next_df AS ( SELECT date_format(publishDate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate,concat(concat('100133','&#&',classifyShow,'&#&','3')) as eigenvalue, compcode as subjectcode, compname as eventsubject, 'SJ000106116' as eventtype,url,classifyShow from sour_df ), add_desc_df as ( select *, concat(eventdate, '，公司获评跨境电商统计调查重点监测企业') as a, if((classifyShow is null) or (classifyShow =''),'',concat('，业务类型为',classifyShow)) as b, '。' e from next_df where subjectcode is not NULL and eventdate is not NULL ), final_df as (select *, concat_ws('',a,b,e) as desc1 from add_desc_df) insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,if(eventtype is NULL ,'#', eventtype) ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4, eigenvalue,null as retrovalue,1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
     map("tempview1",""),"7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000102058',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject,country, date_format(publishdate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, sourceurl as url ,concat( concat('100134','&#&',country,'&#&','3') ) as eigenvalue from seeyii_data_house.dwd_me_buss_chinca_core_list where filedate in (select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_chinca_core_list) and datastatus != 3 and publishdate is not null and publishdate != '' and compcode is not null ), next_df AS ( select *, if((country is null) or (country =''),'',concat('，国别为',country)) as a from base_df ), final_df AS ( select *, concat(eventdate,'，公司获得重点国别市场中国承包商推荐名单认证',a,'。') as desc1 from next_df ) insert into {ku}.{tb} partition (filedate={fileDate}) select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000102058' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000102058' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue,null as retrovalue, 1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
     map("tempview1",""),"7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000106120',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject, case when rawtype == '1' then '展览场馆' when rawtype == '2' then '展览服务单位' when rawtype == '3' then '展览组织单位' end as rawtype,batch, case when rawlevel == '1' then '国家级' when rawlevel == '2' then '省级' when rawlevel == '3' then '市级' end as rawlevel, date_format(publishdate, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, url from seeyii_data_house.dwd_me_buss_core_exhi_list where filedate in (select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_core_exhi_list) and datastatus != 3 and publishdate is not null and publishdate != '' and compcode is not null ), next_df AS ( select *,concat(concat('100135','&#&',rawtype,'&#&','7')) as eigenvalue, if((rawtype is null) or (rawtype =''),'',concat('，类型为',rawtype)) as a0, if((batch is null) or (batch =''),'',concat('，批次为',batch)) as a1, if((rawlevel is null) or (rawlevel =''),'',concat('，级别为',rawlevel)) as a2 from base_df ), final_df AS ( select *, concat(eventdate,'，公司获评展览业重点联系企业',a0,a1,a2,'。') as desc1 from next_df ) insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000106120' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000106120' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue, null as retrovalue,1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
     map("tempview1",""),"7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000106127',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject,filmname,filmtype, date_format(pubtime, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, url ,concat( concat('100136','&#&',filmtype,'&#&','3') ) as eigenvalue from seeyii_data_house.dwd_me_buss_per_dybalx where filedate in (select max(filedate) as filedate from seeyii_data_house.dwd_me_buss_per_dybalx) and datastatus != 3 and isvalid = 1 and pubtime is not null and pubtime != '' and compcode is not null ), next_df AS ( select *, if((filmname is null) or (filmname =''),'',concat('，片名为',filmname)) as a0, if((filmtype is null) or (filmtype =''),'',concat('，影片类型为',filmtype)) as a1 from base_df ), final_df AS ( select *, concat(eventdate,'，公司获得电影备案立项',a0,a1,'。') as desc1 from next_df ) insert into {ku}.{tb} partition (filedate={fileDate}) select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000106127' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000106127' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue, null as retrovalue,1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
     map("tempview1",""),"7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000307001',
     " with base_df AS ( select compcode as subjectcode, compname as eventsubject,projectname,proclass, date_format(proapptime, 'yyyy-MM-dd') as eventdate, CAST(null AS STRING) as expiredate, url,concat(concat('100139','&#&',if(totalinvestment is null, '', totalinvestment),'&#&','3'),'@@',concat('100140','&#&',if(proclass is null, '', proclass),'&#&','3')) as eigenvalue from ( select *,row_number()over(partition by id order by filedate desc) as rnumber from seeyii_data_house.dwd_me_buss_cons_work_basic ) as tb where rnumber = 1 and datastatus != 3 and isvalid = 1 and proapptime is not null and proapptime != '' and compcode is not null ), next_df AS ( select *, if((projectname is null) or (projectname =''),'',concat('，项目名称为',projectname)) as a0, if((proclass is null) or (proclass =''),'',concat('，项目分类为',proclass)) as a1 from base_df ), final_df AS ( select *, concat(eventdate,'，公司新增建筑工程项目立项',a0,a1,'。') as desc1 from next_df ) insert into {ku}.{tb} partition (filedate='{fileDate}') select fingerId_row( concat_ws(',' ,if(eventsubject is NULL ,'#', eventsubject) ,if(subjectcode is NULL ,'#', subjectcode) ,'SJ000307001' ,if(desc1 is NULL ,'#', desc1) ,if(url is NULL ,'#', url) ,if(eventdate is NULL ,'#', eventdate) ,if(expiredate is NULL ,'#', expiredate) ) ) as eventid, subjectcode,eventsubject,'SJ000307001' as eventtype, desc1 as desc, url,eventdate,expiredate, null as property1,null as property2,null as property3,null as property4,eigenvalue, null as retrovalue,1 as datastatus, date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss') as modifytime from final_df ",
     map("tempview1",""),"7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    
    INSERT INTO seeyii_data_oppo.dwd_me_buss_event_oppo_config PARTITION(filedate = '20240203' )
    SELECT  'SJ000208003',
     " with subsist_list as ( select id, relieveDate as eventdate, ipType, tsSubType, ipName, invenName from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc ) AND datastatus != 3 ), sk_stock as ( select id, compcode, compname from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc_se where filedate in ( select max(filedate) from seeyii_data_house.dwd_me_buss_ip_tst_rpc_ecc_se ) AND datastatus != 3 and typecode = 1 ), join_tb as ( select a.compcode as subjectcode, a.compname as eventsubject, b.eventdate, ipType, tsSubType, ipName, invenName, concat( '100162', '&#&', ipType, '&#&', '3' ) as eigenvalue from sk_stock as a join subsist_list as b on a.id = b.id ), next_df as ( select *, CAST(null AS STRING) as url, CAST(null AS STRING) as expiredate, concat( eventdate, '，公司专利权质押合同解除登记' ) as a, if( (ipType is null) or (ipType = ''), '', concat('，专利类型为', ipType) ) as b, if( (tsSubType is null) or (tsSubType = ''), '', concat( '，细分事务分类为', tsSubType ) ) as c, if( (ipName is null) or (ipName = ''), '', concat('，专利名称为', ipName) ) as d, if( (eventsubject is null) or (eventsubject = ''), '', concat('，质权人为', eventsubject) ) as e, if( (invenName is null) or (invenName = ''), '', concat('，发明名称为', invenName) ) as f, '。' g from join_tb where eventdate is not NULL ), final_df as ( select *, concat_ws('', a, b, c, d, e, f, g) as desc1 from next_df ) insert into {ku}.{tb} partition (filedate = '{fileDate}') select fingerId_row( concat_ws( ',', if( eventsubject is NULL, '#', eventsubject ), if( subjectcode is NULL, '#', subjectcode ), 'SJ000208003', if(desc1 is NULL, '#', desc1), if(url is NULL, '#', url), if(eventdate is NULL, '#', eventdate), if( expiredate is NULL, '#', expiredate ) ) ) as eventid, subjectcode, eventsubject, 'SJ000208003' as eventtype, desc1 as desc, url, eventdate, expiredate, null as property1, null as property2, null as property3, null as property4, eigenvalue, 1 as datastatus, date_format( current_timestamp(), 'yyyy-MM-dd HH:mm:ss' ) as modifytime from final_df ",
     map("tempview1",""),"7", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
    