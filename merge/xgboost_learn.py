# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2024/1/29
import xgboost as xgb
from xgboost import XGBRegressor as XGBR
from sklearn.ensemble import RandomForestRegressor as RFR  # 随机森林
from sklearn.linear_model import LinearRegression as LinearR  # 线性回归
from sklearn.datasets import load_boston
# KFold 交叉验证  CVS 交叉验证分数返回的 R^2
from sklearn.model_selection import KFold, cross_val_score as CVS, train_test_split as TTS
from sklearn.metrics import mean_squared_error as MSE  # 均方误差
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from time import time
import datetime




import pandas as pd
import numpy as np

data_url = "http://lib.stat.cmu.edu/datasets/boston"
raw_df = pd.read_csv(data_url, sep="\s+", skiprows=22, header=None)
data = np.hstack([raw_df.values[::2, :], raw_df.values[1::2, :2]])
target = raw_df.values[1::2, 2]

data = pd.read_csv('/Users/<USER>/Downloads/jupyter/训练与测试数据集.csv')
data = data.iloc[:, 2:]
print(data)
x = data.drop(['score1'], axis=1)
y = data['score1']
# random_state 随机因子
# test_size 测试比例 大于 1 就是测试数量
train_data, test_data, train_label, test_label = TTS(x, y, test_size=0.25, random_state=4)
# n_estimators 建多少颗树
reg = XGBR(n_estimators=100).fit(train_data, train_label)
# 预测
print(reg.predict(test_data))

CVS(reg, train_data, train_label, cv=5, scoring="neg_mean_squared_error").mean()

# 如果开启参数 slient 在数据巨大，预料到算法运行会非常缓慢的时候可以使用这参数来监控训练的进度
reg = XGBR(n_estimators=100, verbosity=1).fit(train_data, train_label)
print(reg)

CVS(reg, train_data, train_label, cv=5, scoring="neg_mean_squared_error")


def plot_learning_curve(estimator, title, X, y,
                        ax=None,  # 选择子图
                        ylim=None,  # 設置級坐标的取值范围
                        cv=None,  # 交义验证
                        n_jobs=None  # 發``定索要使用的线程
                        ):
    from sklearn.model_selection import learning_curve
    import matplotlib.pyplot as plt
    import numpy as np

    train_sizes, train_scores, test_scores = learning_curve(estimator, X, y
                                                            , shuffle=True
                                                            , cv=cv,
                                                            # , random_state=420
                                                            n_jobs=n_jobs)
    if ax == None:
        ax = plt.gca()
    else:
        ax = plt.figure()
    ax.set_title(title)
    if ylim is not None:
        ax.set_ylim(*ylim)
    ax.set_xlabel("Training examples")
    ax.set_ylabel("score")
    ax.grid()  # 绘制网格，不是必须
    ax.plot(train_sizes, np.mean(train_scores, axis=1), 'o-', color="r", label="Training score")
    ax.plot(train_sizes, np.mean(test_scores, axis=1), 'o-', color="g", label="Test score")
    ax.legend(loc="best")
    return ax


dfull = xgb.DMatrix(x, y)
param1 = {'silent': True  # 并非默认
    , 'obj': 'reg:linear'  # 并非默认
    , "subsample": 1
    , "max_depth": 6
    , "eta": 0.3
    , "gamma": 0
    , "lambda": 1
    , "alpha": 0
    , "colsample_bytree": 1
    , "colsample_bylevel": 1
    , "colsample_bynode": 1
    , "nfold": 5}
num_round = 200
time0 = time()
cvresult1 = xgb.cv(param1, dfull, num_round)
print(datetime.datetime.fromtimestamp(time() - time0).strftime("%M:%S:%f"))
fig, ax = plt.subplots(1, figsize=(15, 10))
# ax.set_ylim(top=5)
ax.grid()
ax.plot(range(1, 201), cvresult1.iloc[:, 0], c="red", label="train,original")
ax.plot(range(1, 201), cvresult1.iloc[:, 2], c="orange", label="test,original")
ax.legend(fontsize="xx-large")
plt.show()
