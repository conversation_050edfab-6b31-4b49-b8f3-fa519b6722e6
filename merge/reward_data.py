# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/6/2
from core.excel_base import ExcelBase
from moduler.company_area_info import CompanyArea


class RewardData(ExcelBase):
    def __init__(self):
        super(RewardData, self).__init__()
        self.area = CompanyArea()

    def process(self):
        tmp_result = self.query_cmp()
        names = {i["compName"] for i in tmp_result}
        area_dict = self.area.run(list(names))
        result = list()
        for item in tmp_result:
            area_category = item.get("areaCategory", "")
            if area_category == "0":
                item["areaCategory"] = ""
            if area_category == "1":
                item["areaCategory"] = "国家级"
            if area_category == "2":
                item["areaCategory"] = "省级"
            if area_category == "3":
                item["areaCategory"] = "市级"
            name = item["compName"]
            area = area_dict.get(name, dict()).get("provinceName")
            if area == '天津市':
                result.append(item)
        self.save_data_excel(result)

    def query_cmp(self):
        result = list()
        sql = """SELECT * FROM dwd_me_buss_rewd WHERE id>'{}' order by id ASC limit 1000;"""
        for result_list in self._query_sql_iter_by_id(sql, "db_seeyii"):
            for item in result_list:
                data_status = item["dataStatus"]
                if data_status == 3:
                    continue
                result.append(item)
        return result

    def save_data_excel(self, result):
        field_cfg = {
            'compName': ('公司名称', 0),
            'rewardSources': ('奖励来源', 1),
            'rewardName': ('奖励名称', 2),
            'rewardProject': ('奖励项目', 3),
            'rewardYear': ('奖励年份', 4),
            'areaCategory': ('奖励级别', 5),
        }
        self._excel_name = self.name_add_date("政府奖励.xlsx")
        self.save_to_excel(field_cfg, {"Sheet": result})


if __name__ == '__main__':
    p = RewardData()
    p.process()
