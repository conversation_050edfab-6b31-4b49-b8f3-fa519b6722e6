# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2023/2/23
import json
from copy import deepcopy
import pandas as pd
import numpy
from cfg.config import source_file_path
from core.excel_base import ExcelBase


class ParsePath(ExcelBase):
    def __init__(self):
        super(ParsePath, self).__init__()
        self._in_file_path = source_file_path

    def process(self, *args, **kwargs):
        items = self.read_excel_data(self._in_file_path + "/无标题.xlsx", "Sheet1")
        sb = self.read_excel_data(self._in_file_path + "/无标题.xlsx", "双百")
        sb_dict = dict()
        for i in sb:
            sb_dict.setdefault(i["compName"], i["类型"])

        kg = self.read_excel_data(self._in_file_path + "/无标题.xlsx", "科改")
        for i in kg:
            sb_dict.setdefault(i["compName"], i["类型"])

        result_list = list()
        for item in items:
            name = item["compName"]
            print(name)
            path = json.loads(item["actlPath"])["links"]
            item_dict = dict()
            for link in path:
                target_name = link["target_name"]
                item_dict.setdefault(target_name, list()).append(link)
            result_link = list()
            start_link = [name]
            self.recursive(name, item_dict, start_link, result_link)
            # print("\n".join(result_link))

            item["link"] = "\n".join(result_link)
            sb_fl = sb_dict.get(name)
            if sb_fl:
                item["分类"] = sb_fl
            result_list.append(item)
        self.save_data(result_list)


    def save_data(self, result):
        field_cfg = {
            'compName': ('compName', 0),
            'creditCode': ('creditCode', 1),
            'actlName': ('actlName', 2),
            'actlPath': ('actlPath', 3),
            'actlRatio': ('actlRatio', 4),
            '分类': ('分类', 5),
            'link': ('link', 6),
        }
        self._excel_name = self.name_add_date("实控人.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def recursive(self, name, item_dict, start_link, result):
        new_item_list = item_dict.get(name)
        if not new_item_list:
            result.append(self.link_process(start_link))
            return
        for new_item in new_item_list:
            source_name = new_item["source_name"]
            new_link = deepcopy(start_link)
            new_link.append(str(round(new_item["actl_ratio"] * 100, 2)) + "%")
            new_link.append(source_name)
            self.recursive(source_name, item_dict, new_link, result)


    def link_process(self, link):
        l = ""
        for idx, item in enumerate(link):
            if idx % 2 == 0:
                l += item
            else:
                l += "<--"
                l += item + "--"
        return l

    def read_excel_data(self, file_name, sheet="Sheet1"):
        data_list = self._extract_data(file_name, sheet)
        return data_list


if __name__ == '__main__':
    p = ParsePath()
    p.process()
