# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/7/21
"""
1、核心企业（200强、上市公司、央级单位、省属单位）
2、兄弟企业，即核心企业的大股东 所投资（穿透1层，且该股东是大股东）的企业，作为一个交付物excel的一个sheet，字段有“核心企业名称、兄弟企业名称、穿透链条”
//大股东是自然人时，不再查找该公司的兄弟企业
3、核心企业，向上穿透，找出大股东（无限层穿透、每层找出当前层级大股东），作为一个交付物excel的一个sheet，字段有“核心企业名称、股东名称、穿透链条”
4、核心企业+兄弟企业，向下穿透，找出子公司（穿透3层、且该股东是大股东）的企业，作为一个交付物excel的一个sheet，字段有“核心/兄弟企业名称、投资企业、穿透链条”

备注1：大股东为持有当前公司股份所占比例最大的股东，可能存在多个；
备注2：若所有股东持股比例均为0，则不存在大股东。

核心企业测试名单如下
200强：大汉控股集团有限公司、湖南琴岛文化传播有限公司
上市公司：爱尔眼科医院集团股份有限公司、湖南国科微电子股份有限公司
在湘央企：中铁城建集团有限公司
省属国企：湖南湘投控股集团有限公司
"""
from copy import deepcopy
from collections import defaultdict
from core.excel_base import ExcelBase
from moduler.company_a_and_xsb_holder import CompanyAandXsbShareholder
from moduler.company_holder import CompanyHolder
from moduler.company_invester import CompanyInvest
from pprint import pprint

class CSPOC(ExcelBase):
    def __init__(self):
        super(CSPOC, self).__init__()
        self.a_xsb_holder = CompanyAandXsbShareholder()
        self.inv_cmp = CompanyInvest()
        self.gs_holder = CompanyHolder()
        self.a_xsb_names = {i["cname"] for i in self.query_company_category()}

    def process(self):
        names = list({
            "沈阳亮晶投资管理有限公司", "湖南爱尔健康产业发展有限公司", "湖南谦城医疗管理有限公司", "湖南爱尔物业投资发展有限公司", "国科志芯（上海）物联科技有限公司", "国科恒芯（上海）半导体有限公司",
            "湖南国科集成电路产业园有限公司", "长沙芯时代私募股权基金管理有限公司", "常州集成电路生态产业园有限公司", "中铁十六局集团有限公司", "中铁建珠海投资开发有限公司", "中铁建华北投资发展有限公司",
            "南京市江北新区广联管廊建设有限公司", "中铁建长江投资有限公司", "中铁建中原投资建设有限公司", "中铁十九局集团有限公司", "北京华北投新机场北线高速公路有限公司", "中铁建海南建设发展有限公司",
            "中铁市政（厦门）投资管理有限公司", "中铁建城市建设投资有限公司", "中铁第一勘察设计院集团有限公司", "中铁十五局集团有限公司", "中铁建雄安投资发展有限公司", "石家庄润石生态保护管理服务有限公司",
            "广东省航盛建设集团有限公司", "中国铁建港航局集团有限公司", "徐州中铁建投资发展有限公司", "中铁二十三局集团有限公司", "中铁十八局集团有限公司", "中铁建北方投资建设有限公司",
            "中国土木工程集团有限公司", "中铁二十局集团有限公司", "石家庄嘉盛管廊工程有限公司", "中铁建西北投资建设有限公司", "中国铁建投资集团有限公司", "昆明轨道交通五号线建设运营有限公司",
            "石家庄嘉泰管廊运营有限公司", "中非莱基投资有限公司", "成都中铁建昆仑轨道工程有限公司", "重庆大内高速公路有限公司", "中铁建重庆投资集团有限公司", "四川天府机场高速公路有限公司",
            "中铁建黄河投资建设有限公司", "中铁二十一局集团有限公司", "中铁建东方投资建设有限公司", "青岛蓝色硅谷城际轨道交通有限公司", "中铁二十二局集团有限公司", "北京兴延高速公路有限公司",
            "中铁建华南建设有限公司", "中铁十一局集团有限公司", "中铁十二局集团有限公司", "中国铁建电气化局集团有限公司", "中铁建融城发展有限公司", "中国铁建财务有限公司",
            "呼和浩特市地铁二号线建设管理有限公司", "中铁建设集团有限公司", "中铁建发展集团有限公司", "中铁十七局集团有限公司", "广西国投资产管理有限公司", "中铁二十四局集团有限公司",
            "中国铁建高新装备股份有限公司", "中铁物资集团有限公司", "成都中铁建成资轨道交通发展有限公司", "中铁第五勘察设计院集团有限公司", "中铁建华东建设发展有限公司", "中铁建南方建设投资有限公司",
            "中铁磁浮交通投资建设有限公司", "中铁建东南投资建设有限公司", "中国铁建重工集团股份有限公司", "中铁十四局集团有限公司", "中铁海峡建设集团有限公司", "北京中铁天瑞机械设备有限公司",
            "中国铁建国际集团有限公司", "中铁第四勘察设计院集团有限公司", "广德铁建蓝海辉路投资中心（有限合伙）", "中铁建南沙投资发展有限公司", "芜湖长江隧道有限责任公司", "中铁建商务管理有限公司",
            "中铁建国际投资有限公司", "中铁建资本控股集团有限公司", "中铁建华南投资有限公司", "重庆铁建成长高速公路合伙企业（有限合伙）", "中铁建城市开发有限公司", "中铁建康养产业发展有限公司",
            "中铁二十五局集团有限公司", "中国铁建房地产集团有限公司", "中铁上海设计院集团有限公司", "中国铁建昆仑投资集团有限公司", "云南玉临高速公路建设有限责任公司", "中铁建（无锡）工程科技发展有限公司",
            "中国铁建大桥工程局集团有限公司", "中铁建北部湾建设投资有限公司", "北京力寰机械产品服务部", "北京铁道部中联物资供销公司", "北京《铁道建筑技术》杂志社有限公司", "北京中铁诚昊鑫经贸公司",
            "北京中铁出租汽车公司", "北京博大致远房地产开发有限公司", "北京英格索兰机械产品服务部", "北京市中铁高谊科技开发公司", "北京铁建通电信器材销售中心", "中国铁道建筑总公司昆明机械厂经营部",
            "广东潮揭高速公路有限公司", "天津市富康畅达商贸有限公司", "咸阳中铁路桥有限公司", "中国铁建股份有限公司", "中国铁道建筑总公司昆明机械厂石咀储运货场", "北京海石丰工业科技开发公司",
            "成都中铁建筑工程公司", "北京铁能节能技术开发中心", "北京沃尔沃建筑设备产品服务部", "北京北方中铁工程公司", "昆明凤凰建筑工程队", "隆昌工务器材厂", "广州中咨城轨工程咨询有限公司",
            "北京金兴工程造价咨询中心", "中国铁道建筑总公司深圳工程部", "北京汇鑫楼酒店", "北京中铁建第三招待所", "中国铁道建筑总公司深圳东部供水项目部", "北京市中总建物资销售中心",
            "北京市兴中铁汽车配件经营部", "北京通达京承高速公路有限公司", "中铁建锦鲤资产管理有限公司", "上海枫亭水质净化有限公司", "湖南稀土金属材料研究院有限责任公司", "长丰集团有限责任公司",
            "湖南省机械工业设计研究院有限公司", "湖南环达公路桥梁建设总公司", "湖南省湘水集团有限公司", "湖南省煤业集团有限公司", "湖南省国有资产管理集团有限公司", "湖南兴湘投资控股集团有限公司",
            "湖南省皮革集团公司", "湖南省现代农业产业控股集团有限公司", "湖南省道路运输管理局招待所", "湖南省农垦公司", "湖南第一工业设计研究院有限公司", "湖南铁合金集团有限公司",
            "湖南省交通水利建设集团有限公司", "湖南省娄底化工总厂", "湘电集团有限公司", "湖南省二轻供销总公司", "湖南省粮油运输公司", "湖南鑫牛资产管理集团有限公司", "湖南建工控股集团有限公司",
            "华菱控股集团有限公司", "湖南省化学矿山工业公司", "湖南省轻工盐业集团有限公司", "湖南高新创业投资集团有限公司", "湖南发展资产管理集团有限公司", "湖南省高速公路集团有限公司",
            "长沙建设机械研究院有限责任公司", "湖南省化工汽车运输公司", "湖南海利高新技术产业集团有限公司", "湖南轨道交通控股集团有限公司", "湖南有色金属研究院有限责任公司",
            "湖南有色冶金劳动保护研究院有限责任公司", "湖南省湘绣研究所有限公司", "湖南湘科控股集团有限公司", "湖南省工艺美术研究所有限公司", "湖南黄金集团有限责任公司", "湖南省粮油食品经营公司",
            "大汉控股集团有限公司",
            "湖南琴岛文化传播有限公司",
            "爱尔眼科医院集团股份有限公司",
            "湖南国科微电子股份有限公司",
            "中铁城建集团有限公司",
            "湖南湘投控股集团有限公司"
            "中国铁建港航局集团有限公司"
            "中铁十六局集团有限公司"
        })
        names = ["广东小鹏汽车科技有限公司"]
        result = list()
        for name in names:
            link = [name]
            finger = set()
            self.inv_and_max_holder(name, name, link, result, finger, num=1)
        self.save_data_xd(result)

    def save_data_xd(self, result):
        field_cfg = {
            'name': ('核心/兄弟企业名称', 0),
            'inv_name': ('投资企业', 1),
            'link': ('穿透链条', 2)}
        self._excel_name = self.name_add_date("投资企业.xlsx")
        # self.save_to_excel(field_cfg, {"sheet1": result})
        pprint(result)

    def d_holder_cmp(self, names):
        # 大股东
        result = defaultdict(list)
        for i in names:
            if i in self.a_xsb_names:
                holder_data = self.a_xsb_holder.run([i])
            else:
                holder_data = self.gs_holder.run([i])
            holder_list = holder_data.get(i) or list()
            d_holder_list = self.max_holder(holder_list)  # 大股东数据
            for d_holder in d_holder_list:
                holder_name = d_holder["shareholder_name"]
                if len(holder_name) < 4:  # 大股东是自然人时，不再查找该公司的兄弟企业
                    continue
                d_radio = str(round(d_holder["holdRatio"] * 100, 2))
                result[holder_name].append(
                    {"name": i, "holder": holder_name, "radio": d_radio,
                     "path": f"{holder_name}-->{d_radio}-->{i}"})
        return result

    def link_process(self, link):
        l = ""
        for idx, item in enumerate(link):
            if idx % 2 == 0:
                l += item
            else:
                l += "--"
                l += item + "-->"
        return l

    def inv_and_max_holder(self, raw, name, link, result, finger, num):
        if len(name) < 4 or name in finger or num > 3:
            result.append({"name": raw, "inv_name": name, "link": self.link_process(link)})
            return
        finger.add(name)
        inv_dict = self.inv_cmp.run([name])
        inv_names = {i["inv_name"] for i in inv_dict.get(name) or list()}
        inv_holder_dict = self.d_holder_cmp(list(inv_names))
        if not inv_holder_dict and num > 1:
            result.append({"name": raw, "inv_name": name, "link": self.link_process(link)})
        elif num > 1:
            result.append({"name": raw, "inv_name": name, "link": self.link_process(link)})
        for inv_item in inv_holder_dict.get(name) or list():
            new_link = deepcopy(link)
            inv_name = inv_item["name"]
            radio = inv_item["radio"]
            new_link.append(radio + "%")
            new_link.append(inv_name)
            self.inv_and_max_holder(raw, inv_name, new_link, result, finger, num + 1)

    @staticmethod
    def max_holder(holder_list):
        # 大股东
        result = defaultdict(list)
        for item in holder_list:
            ratio = float(item.get("holdRatio")) or 0.0
            result[ratio].append(item)
        if not result:
            return []
        max_ratio = max(result)
        if max_ratio == 0 or not max_ratio:
            return []
        else:
            return result[max_ratio]

    def query_company_category(self):
        query_schema = {
            "db_name": "category_data",
            "collection_name": "company_category",
            "query_condition": {"category": {"$in": ["ThirdCompany", "ACompany"]}, "is_valid": True},
            "query_field": {"cname": 1}}
        query_result = self._data_server.call("query_item", query_schema) or list()
        return query_result


if __name__ == '__main__':
    p = CSPOC()
    p.process()
