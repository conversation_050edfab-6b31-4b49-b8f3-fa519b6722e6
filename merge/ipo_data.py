# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/02/24

import decimal
import json
import os
import xlsxwriter
from collections import defaultdict
from datetime import datetime, date
from cfg.config import out_file_path
from itertools import chain
from moduler.base_info_api_thread import BaseInfoMaster
from utils.datetime_util import DatetimeUtil

from core.excel_base import ExcelBase


class IPO(ExcelBase):
    def __init__(self):
        super(IPO, self).__init__()
        self.base_info = BaseInfoMaster
        self._excel_name = self.name_add_date("拟IPO数据.xlsx")
        self.market = {
            "101": "主板",
            "201": "中小板",
            "301": "创业板",
            "302": "创业板",
            "401": "科创板"}
        self.stat = {
            "101": "启动上市辅导",
            "102": "完成辅导验收",
            "103": "终止上市辅导",
            "201": "已受理",
            "202": "已问询",
            "203": "已反馈",
            "204": "预披露",
            "205": "更新预披露",
            "206": "中止",
            "207": "终止（审核不通过）",
            "208": "终止（撤回）",
            "209": "发审会通过",
            "210": "发审会未通过",
            "211": "暂缓表决",
            "212": "上市委会议通过",
            "213": "上市委会议未通过",
            "214": "暂缓审议",
            "215": "复审委会议通过",
            "216": "复审委会议未通过",
            "217": "提交注册",
            "218": "注册生效",
            "219": "不予注册",
            "220": "终止注册",
            "221": "证监会核准",
            "222": "证监会不予核准",
            "223": "补充审核",
            "301": "已发行上市",
            "302": "发行失败",
            "303": "发行暂缓"}
        self.exchange_map = {
            "101": "上交所",
            "201": "深交所"}
        self.list_fetch = [
            "id",
            "projId",
            "compName",
            "shortName",
            "finaAmount",
            "reguInstitution",
            "preName",
            "brokName",
            "acctName",
            "lawName",
            ("exchange", "exchange", lambda x: self.exchange_map[str(x)]),
            ("market", "market", lambda x: self.market[str(x)]),
            "acceDate",
            ("currStat", "currStat", lambda x: self.stat[str(x)]),
            "statDate",
            "fingerId",
            "dataStatus",
            "createTime",
            "modifyTime"]

    def process(self, *args, **kwargs):
        result = dict()
        ipo_dict = self.query_ipo_data()
        result["拟IPO公司"] = list(chain.from_iterable(ipo_dict.values()))
        result["公司基本信息"] = list(self.base_info.run(list(ipo_dict.keys())).values())
        self.save_data_excel(result)

    def query_ipo_data(self):
        result = defaultdict(list)
        sql_statement = """SELECT compName, market,currStat, statDate from dws_me_trad_ipo_base where dataStatus !=3"""
        query_schema = dict(db_key="seeyii_db", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            item_result = self.fetch_dict(item, self.list_fetch)
            result[item_result["compName"]].append(item_result)
        return result

    def save_data_excel(self, result):
        field_cfg = {
            "拟IPO公司": {
                "compName": ("公司名称", 0),
                "market": ("板块", 1),
                "currStat": ("最新状态", 2),
                "statDate": ("最新状态时间", 3)},
            "公司基本信息": {
                'companyName': ('公司名称', 0),
                'credit_code': ('统一信用代码', 1),
                'legal_person_name': ('法定代表人', 2),
                'reg_capital': ('注册资本', 3),
                'establish_date': ('成立时间', 4),
                'reg_status': ('注册状态', 5),
                'company_org_type': ('公司组织类型', 6),
                'reg_province': ('省', 7),
                'reg_city': ('市', 8),
                'reg_district': ('区', 9),
                'reg_location': ('注册地址', 10),
                'postal_address': ('办公地址', 11),
                'phone_number': ('联系电话', 12),
                'business_scope': ('经营范围', 13),
                'company_nature': ('性质标签', 14),
                'market': ('所属市场', 15),
                'company_business': ('业务亮点', 16)}}
        output_file_path = os.path.join(out_file_path, self._excel_name)
        xls_file = xlsxwriter.Workbook(output_file_path)
        for sheet in ["拟IPO公司", "公司基本信息"]:
            xls_sheet = xls_file.add_worksheet(name=sheet)
            self._save_to_excel(field_cfg[sheet], xls_sheet, result[sheet], sheet)
        xls_file.close()
        print("finished.")

    @staticmethod
    def _save_to_excel(field_dict, xls_sheet, item_list, sheet):
        for title in field_dict.values():
            xls_sheet.write(0, title[1], title[0])
        row_no = 1
        for result_item in item_list:
            for field_name, title in field_dict.items():
                field_value = result_item.get(field_name, "")
                if isinstance(field_value, decimal.Decimal):
                    field_value = float(field_value)
                if isinstance(field_value, datetime):
                    field_value = DatetimeUtil.date_to_str(field_value)
                if isinstance(field_value, date):
                    field_value = field_value.strftime("%Y-%m-%d")
                if not isinstance(field_value, str):
                    field_value = json.dumps(field_value, ensure_ascii=False)
                if field_value == "null":
                    continue
                xls_sheet.write(row_no, title[1], field_value)
            row_no += 1
        print("{} finished.".format(sheet))


if __name__ == '__main__':
    p = IPO()
    p.process()
