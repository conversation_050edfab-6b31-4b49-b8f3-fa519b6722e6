# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/25

from itertools import chain

from cfg.config import out_file_path
from core.excel_base import ExcelBase
from moduler.company_mysql_licence import MysqlLicence
from moduler.company_mysql_reward import MysqlReward
from moduler.company_mysql_standar import MysqlStandar


class LicenceAndReward(ExcelBase):
    def __init__(self):
        super(LicenceAndReward, self).__init__()
        self.licence = MysqlLicence()
        self.reward = MysqlReward()
        self.stand = MysqlStandar()

    def process(self, *args, **kwargs):
        name_list = self.read_excel_data(self._in_file_path + "/浦发_公司名录_0202.xlsx", "Sheet1")
        names = list({item["公司名称"].replace("(", "（").replace(")", "）").strip() for item in name_list})
        licence = list(chain.from_iterable(self.licence.run(names).values()))
        reward = list(chain.from_iterable(self.reward.run(names).values()))
        stand = list(chain.from_iterable(self.stand.run(names).values()))
        # print(licence, reward, stand)
        self.save_data_excel(licence, reward, stand)

    def save_data_excel(self, licence, reward, stand):
        licence_field_cfg = {
            'compName': ('公司名称', 0),
            'licenceBeginDate': ('生效日期', 1),
            'licenceCategory': ('证照分类', 2),
            'licenceType': ('证照类型', 3),
            'publishOrg': ('发布机关', 4),
            'details': ('证照详情', 5),
        }
        lic_excel_name = out_file_path + "/" + self.name_add_date("浦发银行_证照.xlsx")
        self.save_to_excel(licence_field_cfg, {"sheet1": licence}, output_file=lic_excel_name)

        reward_field_cfg = {
            'compName': ('公司名称', 0),
            'rewardSources': ('奖励来源', 1),
            'rewardName': ('奖励名称', 2),
            'rewardProject': ('奖励项目', 3),
            'rewardYear': ('奖励年份', 4),
            'areaCategory': ('奖励级别', 5)}
        r_excel_name = out_file_path + "/" + self.name_add_date("浦发银行_政府奖励.xlsx")
        self.save_to_excel(reward_field_cfg, {"sheet1": reward}, output_file=r_excel_name)

        stand_field_cfg = {
            'draftOrg': ('起草单位', 0),
            'standardName': ('标准名称', 1),
            'standardType': ('标准类型', 2),
            'teamName': ('标准团体', 3),
            'relatedProducts': ('相关产品', 4),
            'publishDate': ('发布时间', 5),
            'details': ('详情', 6),
        }
        s_excel_name = out_file_path + "/" + self.name_add_date("浦发银行_标准.xlsx")
        self.save_to_excel(stand_field_cfg, {"sheet1": stand}, output_file=s_excel_name)

    def read_excel_data(self, file_name, sheet="Sheet"):
        data_list = self._extract_data(file_name, sheet)
        return data_list


if __name__ == '__main__':
    p = LicenceAndReward()
    p.process()
