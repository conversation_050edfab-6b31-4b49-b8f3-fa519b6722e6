# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/02/02
from core.excel_base import ExcelBase


class PFGDZC(ExcelBase):
    """
    固定资产投资项目 128mysql-db_seeyii-ext_build_project
    """
    def __init__(self):
        super(PFGDZC, self).__init__()

    def process(self):
        result = list()
        name_list = self.read_excel_data(self._in_file_path + "/浦发_公司名录_0202.xlsx", "Sheet1")
        names = {item["公司名称"].replace("(", "（").replace(")", "）").strip() for item in name_list}
        # names = ["云霄县兴云建设发展有限公司", "国家能源集团宁夏煤业有限责任公司", "固定资产投资项目"]
        name_set = self.query_mysql_data()
        for name in names:
            if name in name_set:
                result.append({"name": name, "固定资产投资项目": "是"})
            else:
                result.append({"name": name, "固定资产投资项目": "否"})
        self.save_data_excel(result)

    def read_excel_data(self, file_name, sheet="Sheet"):
        data_list = self._extract_data(file_name, sheet)
        return data_list

    def save_data_excel(self, result):
        field_cfg = {
            'name': ('公司名称', 0),
            '固定资产投资项目': ('固定资产投资项目', 1)}
        self._excel_name = self.name_add_date("浦发银行_固定资产投资项目.xlsx")
        self.save_to_excel(field_cfg, {"Sheet": result})

    def query_mysql_data(self):
        result = set()
        sql_statement = """
        SELECT id,  ownerCompany from ext_build_project where id > {} ORDER BY id ASC limit 1000;
        """
        obj_id, data_list = None, list()
        while True:
            if obj_id is None:
                query_schema = dict(db_key="db_seeyii_128", sql_statement=sql_statement.format(0))
            else:
                query_schema = dict(db_key="db_seeyii_128", sql_statement=sql_statement.format(obj_id))
            result_list = self._data_server.call("query_sql_item", query_schema)
            if len(result_list) == 0:
                break
            for n in result_list:
                result.add(n["ownerCompany"])
            obj_id = result_list[-1]["id"]
        return result


if __name__ == '__main__':
    p = PFGDZC()
    p.process()
