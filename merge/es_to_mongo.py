# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/9/2
from elasticsearch import Elasticsearch
from elasticsearch.helpers import scan, bulk

from core.excel_base import ExcelBase

es_user, es_pwd = "shiye_es", "shiye1805A"
NODE_CFGS = [
    # {"host": "*************",
    #  "port": 9200},
    {"host": "*************",
     "port": 9200}]


class EsToMongo(ExcelBase):
    def __init__(self):
        super(EsToMongo, self).__init__()
        self.__es = Elasticsearch(
            NODE_CFGS, http_auth=(es_user, es_pwd))

    def process(self):
        self.extract_stock_data()

    def extract_stock_data(self):
        num = 0
        search_body = {
            "query": {"match_all": {}}}
        raw_data = scan(
            self.__es, search_body, scroll="10m", index="company_reward",
            doc_type="reward", timeout="10m")
        for item in raw_data:
            num += 1
            raw_item = item["_source"]
            self._insert_data(raw_item)
            print(num)

    def _insert_data(self, item):
        update_schema = {
            "db_name": "raw_data",
            "collection_name": "reward_intermediate_export_lqp",
            "insert_data": item}
        self._data_server.call("insert_item", update_schema)


if __name__ == '__main__':
    p = EsToMongo()
    p.process()
