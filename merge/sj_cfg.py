# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/05/08

import re

a = """




"""

if "create" in a:
    name = re.findall("create\s+temporary\s+table\s+(.*?)\s+as", a)[0]
    sql = re.findall(r"as\s+(.*?)\s+with", a)[0]
    tt = a.replace(sql, '')
    ff = re.findall("(.*)[\s\n\t]+with", tt)[0]
    wa = tt.replace(ff, '')
    a = wa.replace("with", "with %s as (%s), "%(name,sql))

b = re.sub("[\n\t\s]+", " ", a)
# print(b)
c  = b.replace("\"", "'").replace("seeyii_gs", 'seeyii_data_house').replace("kf1_bus", 'seeyii_emr_bus').replace(
    ";", '').replace("-- 保存明细表数据", '').replace("cmntert_dev", 'seeyii_data_tert_dev').replace("cmntert", 'seeyii_data_tert').replace(
    "dwd_me_buss_event_oppo_mid","{ku}.{tb}").replace("${bizdate}", "{fileDate}").replace("\\", "\\\\").replace("|'",'').replace(
    "dwd_me_buss_event_oppo_wide_table_mid","{ku}.{tb}").replace("dwd_me_risk_event_param_mid","{ku}.{tb}").replace(
    "dwd_me_risk_event_v2_2023_param","seeyii_data_oppo.dwd_me_risk_event_v2_2023_param")

if "--" in c:
    raise Exception("查看sql中的 -- ")

d = re.search("'SJ.*?'", c).group(0)

e = """
INSERT INTO seeyii_data_oppo_dev.dwd_me_buss_event_oppo_config_lqp PARTITION(filedate = '20231113' )
SELECT  {},
 "{}",
 map("tempview1",""),"9", date_format(current_timestamp(),'yyyy-MM-dd HH:mm:ss');
""".format(d,c)
# print(e)
with open("./template_out.txt", 'a', encoding='utf-8') as f:
    f.write(e)

