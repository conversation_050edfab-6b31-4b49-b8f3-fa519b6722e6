# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date: 2021/7/21
"""
1、核心企业（200强、上市公司、央级单位、省属单位）
2、兄弟企业，即核心企业的大股东 所投资（穿透1层，且该股东是大股东）的企业，作为一个交付物excel的一个sheet，字段有“核心企业名称、兄弟企业名称、穿透链条”
//大股东是自然人时，不再查找该公司的兄弟企业
3、核心企业，向上穿透，找出大股东（无限层穿透、每层找出当前层级大股东），作为一个交付物excel的一个sheet，字段有“核心企业名称、股东名称、穿透链条”
4、核心企业+兄弟企业，向下穿透，找出子公司（穿透3层、且该股东是大股东）的企业，作为一个交付物excel的一个sheet，字段有“核心/兄弟企业名称、投资企业、穿透链条”

备注1：大股东为持有当前公司股份所占比例最大的股东，可能存在多个；
备注2：若所有股东持股比例均为0，则不存在大股东。

核心企业测试名单如下
200强：大汉控股集团有限公司、湖南琴岛文化传播有限公司
上市公司：爱尔眼科医院集团股份有限公司、湖南国科微电子股份有限公司
在湘央企：中铁城建集团有限公司
省属国企：湖南湘投控股集团有限公司
"""

from copy import deepcopy
from collections import defaultdict
from core.excel_base import ExcelBase
from moduler.company_a_and_xsb_holder import CompanyAandXsbShareholder
from moduler.company_holder import CompanyHolder
from moduler.company_invester import CompanyInvest


class CSPOC(ExcelBase):
    def __init__(self):
        super(CSPOC, self).__init__()
        self.a_xsb_holder = CompanyAandXsbShareholder()
        self.inv_cmp = CompanyInvest()
        self.gs_holder = CompanyHolder()
        self.a_xsb_names = {i["cname"] for i in self.query_company_category()}

    def process(self):
        names = [
            "大汉控股集团有限公司",
            "湖南琴岛文化传播有限公司",
            "爱尔眼科医院集团股份有限公司",
            "湖南国科微电子股份有限公司",
            "中铁城建集团有限公司",
            "湖南湘投控股集团有限公司"
        ]
        result, finger = list(), set()
        num = 0
        for i in names:
            link = [i]
            self.d_holder_cmp(i, i, finger, link, result, num)
        self.save_data_xd(result)

    def save_data_xd(self, result):
        field_cfg = {
            'name': ('核心企业名称', 0),
            'sh_name': ('股东名称', 1),
            'link': ('穿透链条', 2)}
        self._excel_name = self.name_add_date("大股东.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def d_holder_cmp(self, raw, name, finger, link, result, num):
        if len(name) < 4 or name in finger:
            result.append({"name": raw, "sh_name": name, "link": self.link_process(link)})
            return
        finger.add(name)
        if name in self.a_xsb_names:
            holder_data = self.a_xsb_holder.run([name])
        else:
            holder_data = self.gs_holder.run([name])
        holder_list = holder_data.get(name) or list()
        d_holder_list = self.max_holder(holder_list)  # 大股东数据
        if not d_holder_list:
            result.append({"name": raw, "sh_name": name, "link": self.link_process(link)})
        for d_holder in d_holder_list:
            holder_name = d_holder["shareholder_name"]
            radio = str(round(d_holder["holdRatio"] * 100, 2))
            new_link = deepcopy(link)
            new_link.append(radio + "%")
            new_link.append(holder_name)
            self.d_holder_cmp(raw, holder_name, finger, new_link, result, num)

    def link_process(self, link):
        l = ""
        for idx, item in enumerate(link):
            if idx % 2 == 0:
                l += item
            else:
                l += "<--"
                l += item + "--"
        return l

    @staticmethod
    def max_holder(holder_list):
        # 大股东
        result = defaultdict(list)
        for item in holder_list:
            ratio = float(item.get("holdRatio")) or 0.0
            result[ratio].append(item)
        if not result:
            return []
        max_ratio = max(result)
        if max_ratio == 0 or not max_ratio:
            return []
        else:
            return result[max_ratio]

    def query_company_category(self):
        query_schema = {
            "db_name": "category_data",
            "collection_name": "company_category",
            "query_condition": {"category": {"$in": ["ThirdCompany", "ACompany"]}, "is_valid": True},
            "query_field": {"cname": 1}}
        query_result = self._data_server.call("query_item", query_schema) or list()
        return query_result


if __name__ == '__main__':
    p = CSPOC()
    p.process()
