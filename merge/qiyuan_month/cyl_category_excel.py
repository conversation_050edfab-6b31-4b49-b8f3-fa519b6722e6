# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/12

from core.excel_base import ExcelBase
from moduler.company_category import CompanyCategory
from moduler.company_industry_and_cyl import CompanyIndustryAndCYL


class QiYuan(ExcelBase):
    def __init__(self):
        super(<PERSON><PERSON>uan, self).__init__()
        self.cyl_ind_map = CompanyIndustryAndCYL()
        self.category_map = CompanyCategory()
        self.category_list = [
            "SYAO2000", "SYAO3000", "SYAO4000",
            "SYAO5000", "SYAO6000", "SYAO7000",
            "SYAO8000", "SYAO9000", "SYAOB000",
            "SYAOC000", "SYAOD000", "SYAOF000"]
        self.__db_key = "seeyii"
        self.file_name = self._in_file_path + "/companies.xlsx"

    def process(self, *args, **kwargs):
        result, all_ind_map, num = list(), dict(), 0
        names_dict = self.read_excel_data(self.file_name, "companies")
        names_list = list({item["name"].replace("(", "（").replace(")", "）").strip() for item in names_dict})
        # names_list = ["中科寒武纪科技股份有限公司"]
        for idx in range(0, len(names_list), 50):
            names = names_list[idx: idx + 50]
            alias_map, cur_map = self.query_alias_data(names)
            for name in names:
                is_cur = True
                cur_name = cur_map.get(name, name)
                query_name = [cur_name]
                cyl_data = self.cyl_ind_map.run(query_name)  # 公司行业及产业链数据
                if not cyl_data:  # 曾用名查
                    query_name = list(alias_map[name])
                    if len(query_name) != 1:
                        cyl_data = self.cyl_ind_map.run(query_name)
                        is_cur = False
                category_info = self.category_map.run(query_name)

                ind_set, cyl_set = set(), set()
                for _, cyl_list in cyl_data.items():
                    for cyl in cyl_list:
                        ind_name = cyl.get("IndustryName")
                        ind_id = cyl.get("industryId")
                        if ind_name and ind_id:
                            ind_set.add(ind_name)
                            all_ind_map.setdefault(ind_id, ind_name)  # 整合全部行业
                        cyl_name = cyl.get("cyl_name")
                        if cyl_name:
                            cyl_set.add(cyl_name)
                        if not is_cur:  # 多个曾用名查查出多个行业只去一个行业
                            print(name, query_name)
                            break

                cat_list = list()
                for n, i in category_info.items():
                    cat_list.extend(i.get("category", list()))

                tmp_item = dict()
                tmp_item["name"] = cur_name
                for c, m in {"ACompany": "A股公司", "ThirdCompany": "三板公司", "FourthCompany": "四板公司",
                             "OthersCompany": "非挂牌上市公司"}.items():
                    if c in ",".join(cat_list):
                        tmp_item["market"] = m

                for category in self.category_list:
                    if category in ",".join(cat_list):
                        tmp_item[category] = "是"
                    else:
                        tmp_item[category] = "否"
                if cyl_set:
                    tmp_item["cyl"] = ','.join(list(cyl_set))
                if ind_set:
                    tmp_item["ind"] = ','.join(list(ind_set))
                result.append(tmp_item)
                num += 1;
                print(num)
        # industry_list = self.ind_data_process(all_ind_map)
        tmp_result = {"sheet1": result}
        self.save_data_excel(tmp_result)

    def query_alias_data(self, name_list):
        alias_cur_dict, cur_map = dict(), dict()
        for name in name_list:
            query_schema = {
                "db_name": "raw_data",
                "collection_name": "company_alias_name",
                "query_condition": {"$or": [{"alias_name": name}, {"cur_name": name}], "is_valid": True},
                "query_field": {"_id": 0, "alias_name": 1, "cur_name": 1}}
            query_result = self._data_server.call("query_item", query_schema)
            if query_result:
                for item in query_result:
                    cur_name = item.get("cur_name")
                    alias_name = item.get("alias_name")
                    cur_map.setdefault(alias_name, cur_name)
                    alias_cur_dict.setdefault(name, set())
                    alias_cur_dict[name].add(cur_name)
                    alias_cur_dict[name].add(alias_name)
            else:
                alias_cur_dict.setdefault(name, set())
                alias_cur_dict[name].add(name)
        return alias_cur_dict, cur_map

    def save_data_excel(self, result):
        field_cfg = {
            'name': ('公司名称', 0),
            'cyl': ('产业链名称', 1),
            'ind': ('行业名称', 2),
            'SYAO2000': ('是否科技型中小企业', 3),
            'SYAO3000': ('是否技术创新示范企业', 4),
            'SYAO4000': ('是否专精特新/小巨人企业', 5),
            'SYAO5000': ('是否拥有工业设计中心', 6),
            'SYAO6000': ('是否技术先进型服务企业', 7),
            'SYAO7000': ('是否拥有工程技术研究中心', 8),
            'SYAO8000': ('是否拥有企业重点实验室', 9),
            'SYAO9000': ('是否拥有院士专家工作站', 10),
            'SYAOB000': ('是否服务型制造示范企业', 11),
            'SYAOC000': ('是否新型研发机构', 12),
            'SYAOD000': ('是否拥有企业技术中心', 13),
            'SYAOF000': ('是否拥有高新技术企业研究开发中心', 14),
            'IndustryName': ('行业名称', 15),
            'compName': ('公司名称', 16),
            'market': ('市场', 17),
        }
        self._excel_name = self.name_add_date("启元POC数据.xlsx")
        self.save_to_excel(field_cfg, result)

    def read_excel_data(self, file_name, sheet="sheet1"):
        data_list = self._extract_data(file_name, sheet)
        return data_list

    def ind_data_process(self, all_ind_map):
        result, filter = list(), set()
        ind_id_list = list(set(all_ind_map.keys()))
        for ind_id in ind_id_list:
            for data_list in self.query_industry_data(ind_id):
                for item in data_list:
                    industryId = item["industryId"]
                    compName = item["compName"]
                    _str = compName + str(industryId)
                    if _str in filter:
                        continue
                    filter.add(_str)
                    result.append(item)
        return result

    def query_industry_data(self, ind_id):
        sql_statement_list = [
            """
            SELECT id, compName, industryId, IndustryName FROM `sq_comp_all_industry`  WHERE 
            id>{} and industryId=%s ORDER BY id ASC limit 1000;""" % ind_id,
            """
            SELECT a.id, companyname as compName, industryId, IndustryName FROM 
            `sq_sk_basicinfo` a, sq_comp_industry b  WHERE  a.secucode=b.secucode 
            and industryId=%s and a.id>{} ORDER BY a.id ASC limit 1000;
            """ % ind_id]
        for sql_statement in sql_statement_list:
            obj_id, data_list, num = None, list(), 0
            while True:
                if obj_id is None:
                    query_schema = dict(db_key=self.__db_key, sql_statement=sql_statement.format(0))
                else:
                    query_schema = dict(db_key=self.__db_key, sql_statement=sql_statement.format(obj_id))
                result_list = self._data_server.call("query_sql_item", query_schema)
                num += 1
                if len(result_list) == 0:
                    break
                yield result_list
                obj_id = result_list[-1]["id"]


if __name__ == '__main__':
    p = QiYuan()
    p.process()
