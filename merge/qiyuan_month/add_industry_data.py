# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/18
from core.excel_base import ExcelBase
from moduler.company_category import CompanyCategory


class AddIndustry(ExcelBase):
    """
    通过国标行业映射到视野行业
    """

    def __init__(self):
        super(AddIndustry, self).__init__()
        self.file_name = self._in_file_path + "/没有行业的公司名单.xlsx"
        self.code_map_ind = self.query_mysql_ind()
        self.category_map = CompanyCategory()

    def process(self, *args, **kwargs):
        result = list()
        gb_map = dict()
        gm_industry = self.read_excel_data(self._in_file_path + "/国民经济行业与视野对应表20210120.xlsx", "中类表")
        for gb_ind in gm_industry:
            if not gb_ind["中类"]:
                continue
            z_class = str(int(gb_ind["中类"])).zfill(3)
            gb_map.setdefault(z_class, list())
            gb_map[z_class].append(gb_ind)

        for item in [{'公司名称': '上海临景生物科技有限公司', },
                     {'公司名称': '上海咏胜医药科技有限公司', },
                     {'公司名称': '上海戈沃生物科技有限公司', },
                     {'公司名称': '上海杏晟医疗管理合伙企业（有限合伙）', },
                     {'公司名称': '上海琛健生物科技有限公司', },
                     {'公司名称': '上海百赛生物技术股份有限公司', },
                     {'公司名称': '上海美志医药科技有限公司', },
                     {'公司名称': '上海裕诣企业管理合伙企业（有限合伙）', },
                     {'公司名称': '乐明药业（苏州）有限公司', },
                     {'公司名称': '乐普生物科技有限公司', },
                     {'公司名称': '云南生物谷创新药物投资有限公司', },
                     {'公司名称': '佛山泰奇生物科技有限公司', },
                     {'公司名称': '兰州佛慈医药产业发展集团有限公司', },
                     {'公司名称': '内蒙古福泽生物科技有限公司', },
                     {'公司名称': '勤浩医药（苏州）有限公司', },
                     {'公司名称': '北京ABB电力系统有限公司', },
                     {'公司名称': '北京东方通网信科技有限公司', },
                     {'公司名称': '北京中水科工程集团有限公司', },
                     {'公司名称': '北京依威克斯生物科技有限公司', },
                     {'公司名称': '北京嘉荷品牌管理有限公司', },
                     {'公司名称': '北京国能昌运高技术配煤有限公司', },
                     {'公司名称': '北京宝御科技有限公司', },
                     {'公司名称': '北京振东朗迪制药有限公司', },
                     {'公司名称': '北京昇腾边缘云计算有限公司', },
                     {'公司名称': '北京界面精准网络科技有限公司', },
                     {'公司名称': '北京纳通医疗科技控股有限公司', },
                     {'公司名称': '北京英鸿光大生物技术有限公司', },
                     {'公司名称': '北京融联瑞达科技有限公司', },
                     {'公司名称': '北京长江迈医药科技有限责任公司', },
                     {'公司名称': '十堰市益菲生物科技有限公司', },
                     {'公司名称': '十堰布瑞斯干细胞生物医药有限公司', },
                     {'公司名称': '厦门市博瑞来医药科技有限公司', },
                     {'公司名称': '厦门康昱信生物科技有限公司', },
                     {'公司名称': '厦门本素药业有限公司', },
                     {'公司名称': '厦门蜗金生物科技有限公司', },
                     {'公司名称': '吉林省博大伟业制药有限公司', },
                     {'公司名称': '吉林省杭盖秱博生物科技有限公司', },
                     {'公司名称': '吉林金宝药业股份有限公司', },
                     {'公司名称': '四川诚富投资管理有限公司', },
                     {'公司名称': '国家能源集团新能源有限责任公司', },
                     {'公司名称': '国能智深控制技术有限公司', },
                     {'公司名称': '国能销售集团华北能源贸易有限公司', },
                     {'公司名称': '国能锅炉压力容器检验有限公司', },
                     {'公司名称': '天津尚德药缘科技股份有限公司', },
                     {'公司名称': '天津招商天合医药科技发展合伙企业（有限合伙）', },
                     {'公司名称': '宁夏泰胜生物科技有限公司', },
                     {'公司名称': '宁波希图零点生物科技有限公司', },
                     {'公司名称': '宁波明勤伟丽生物科技有限公司', },
                     {'公司名称': '宁波百奥维骨生物科技有限公司', },
                     {'公司名称': '安徽诺全药业有限公司', },
                     {'公司名称': '安徽银创生物科技股份有限公司', },
                     {'公司名称': '安达生物药物开发（深圳）有限公司', },
                     {'公司名称': '宜春大海龟生命科学有限公司', },
                     {'公司名称': '宝狮视讯科技集团有限公司', },
                     {'公司名称': '宿迁芙瑞达玫瑰科技有限公司', },
                     {'公司名称': '山东海能生物工程有限公司', },
                     {'公司名称': '广东佰鸿生物技术集团有限公司', },
                     {'公司名称': '广州承葛生物科技有限公司', },
                     {'公司名称': '广州昕生医学材料有限公司', },
                     {'公司名称': '广州钮健生物科技有限公司', },
                     {'公司名称': '康宏药源（河南）科技有限公司', },
                     {'公司名称': '张家港市瑞龙大健康产业投资中心（有限合伙）', },
                     {'公司名称': '攀宝科技集团有限公司', },
                     {'公司名称': '新疆灿坤生物科技有限公司', },
                     {'公司名称': '新线科技有限公司', },
                     {'公司名称': '昆仑数智科技有限责任公司', },
                     {'公司名称': '普世华康江苏医疗技术有限公司', },
                     {'公司名称': '普瑞金（深圳）生物技术有限公司', },
                     {'公司名称': '杭州捷诺飞科技股份有限公司', },
                     {'公司名称': '杭州邦顺制药有限公司', },
                     {'公司名称': '武汉亿诺瑞生物制药有限公司', },
                     {'公司名称': '武汉伊莱瑞特生物科技股份有限公司', },
                     {'公司名称': '武汉军谷坊科技开发有限公司', },
                     {'公司名称': '武汉华大吉诺因生物科技有限公司', },
                     {'公司名称': '武汉安奈吉食品饮料有限公司', },
                     {'公司名称': '武汉康湃特生物科技有限公司', },
                     {'公司名称': '武汉红郡源科技有限公司', },
                     {'公司名称': '武汉翼博济生生物科技有限公司', },
                     {'公司名称': '水发合众清源（北京）环境科技有限公司', },
                     {'公司名称': '江苏佰澳达生物科技有限公司', },
                     {'公司名称': '江苏斯微特医药科技有限公司', },
                     {'公司名称': '江苏月明堂生物科技有限公司', },
                     {'公司名称': '河北泽华生物科技有限公司', },
                     {'公司名称': '河南牧翔生物科技有限公司', },
                     {'公司名称': '河南省亚弘生物科技有限公司', },
                     {'公司名称': '浙江丽水利民生物科技有限公司', },
                     {'公司名称': '浙江自贸区霍普金斯生物科技有限公司', },
                     {'公司名称': '浙江财和生物科技有限公司', },
                     {'公司名称': '深圳市瑞华制药技术有限公司', },
                     {'公司名称': '深圳市盛景基因生物科技有限公司', },
                     {'公司名称': '深圳市红土康方投资合伙企业（有限合伙）', },
                     {'公司名称': '温州市西尔诺医药有限公司', },
                     {'公司名称': '温州广源瑞生物科技有限公司', },
                     {'公司名称': '湖北蒲禾生物科技有限公司', },
                     {'公司名称': '湖南中净生物科技有限公司', },
                     {'公司名称': '烟台市洁泉生物科技有限公司', },
                     {'公司名称': '烟台芥子生物技术有限公司', },
                     {'公司名称': '烟台赛泽生物技术有限公司', },
                     {'公司名称': '珠海宇繁生物科技有限责任公司', },
                     {'公司名称': '理昂生态能源股份有限公司', },
                     {'公司名称': '真臻（宿迁）生物科技有限公司', },
                     {'公司名称': '瞭望者科技集团有限公司', },
                     {'公司名称': '福建医联康护信息技术有限公司', },
                     {'公司名称': '维度（西安）生物医疗科技有限公司', },
                     {'公司名称': '网鼎深思科技（北京）有限公司', },
                     {'公司名称': '自贡荣华生物科技有限公司', },
                     {'公司名称': '芜湖市东旭威宇医疗器械有限公司', },
                     {'公司名称': '苏州先康科技有限公司', },
                     {'公司名称': '苏州医本生命科技有限公司', },
                     {'公司名称': '苏州吴中生物医药产业园投资有限公司', },
                     {'公司名称': '苏州宇测生物科技有限公司', },
                     {'公司名称': '蚌埠安颂生物科技有限公司', },
                     {'公司名称': '蚌埠莱博欣生物技术有限责任公司', },
                     {'公司名称': '财邦生物科技宜兴有限公司', },
                     {'公司名称': '贵州利盛健达生物科技有限公司', },
                     {'公司名称': '贵州好司特生物科技有限公司', },
                     {'公司名称': '贵州阜康仁生物医药科技有限公司', },
                     {'公司名称': '赛乐基因科技（北京）有限公司', },
                     {'公司名称': '重庆海王生物工程有限公司', },
                     {'公司名称': '铁科检测有限公司', },
                     {'公司名称': '铜川药王健康产业有限公司', },
                     {'公司名称': '长春莱瑞福生物科技有限公司', },
                     {'公司名称': '长春金荷药业有限公司', },
                     {'公司名称': '陕西通用纳米科技有限公司', },
                     {'公司名称': '青岛华融汇通生物科技有限公司', },
                     {'公司名称': '青岛大地同年生物科技有限公司', },
                     {'公司名称': '青岛金泰克生物科技有限公司', },
                     {'公司名称': '青梧桐（吉林省）生物科技有限公司', },
                     {'公司名称': '青海青藏高原健康科技有限公司', },
                     {'公司名称': '飞览天下文化发展集团有限公司'}, ]:
            ind = item.get("行业名称")
            if not ind:
                name = item["公司名称"]
                raw_cat = self.category_map.run([name])
                cat_list = list()
                for n, i in raw_cat.items():
                    cat_list.extend(i.get("category", list()))

                for c, m in {"ACompany": "A股公司", "ThirdCompany": "三板公司", "FourthCompany": "四板公司",
                             "OthersCompany": "非挂牌上市公司"}.items():
                    if c in ",".join(cat_list):
                        item["市场"] = m

                gb_dict = self.query_cmp_ind([name])
                if gb_dict:
                    sy_inds, sy3_inds, level_list, gb_list = list(), list(), list(), list()
                    for name, gb in gb_dict.items():
                        gb_list.append(str(gb))
                        sy_ind_list = gb_map.get(str(gb), list())
                        for sy_ind in sy_ind_list:
                            sy2 = sy_ind["视野二级行业"]
                            sy_inds.append(sy2)
                            sy3 = sy_ind["视野三级行业"]
                            sy3_inds.append(sy3)
                            sy_level = str(sy_ind["级别"])
                            level_list.append(sy_level)
                    if sy_inds:
                        item["行业名称"] = ",".join(sy_inds)
                        item["等级"] = ",".join(level_list)
                        item["视野三级行业"] = ",".join(sy3_inds)
                    if gb_list:
                        item["国标行业"] = ",".join(gb_list)
            result.append(item)
        tmp_result = {"sheet1": result}
        self.save_data_excel(tmp_result)

    def save_data_excel(self, result):
        field_cfg = {
            '公司名称': ('公司名称', 0),
            '产业链名称': ('产业链名称', 1),
            '行业名称': ('行业名称', 2),
            '是否科技型中小企业': ('是否科技型中小企业', 3),
            '是否技术创新示范企业': ('是否技术创新示范企业', 4),
            '是否专精特新/小巨人企业': ('是否专精特新/小巨人企业', 5),
            '是否拥有工业设计中心': ('是否拥有工业设计中心', 6),
            '是否技术先进型服务企业': ('是否技术先进型服务企业', 7),
            '是否拥有工程技术研究中心': ('是否拥有工程技术研究中心', 8),
            '是否拥有企业重点实验室': ('是否拥有企业重点实验室', 9),
            '是否拥有院士专家工作站': ('是否拥有院士专家工作站', 10),
            '是否服务型制造示范企业': ('是否服务型制造示范企业', 11),
            '是否新型研发机构': ('是否新型研发机构', 12),
            '是否拥有企业技术中心': ('是否拥有企业技术中心', 13),
            '是否拥有高新技术企业研究开发中心': ('是否拥有高新技术企业研究开发中心', 14),
            '市场': ('市场', 15),
            '等级': ('等级', 16),
            '视野三级行业': ('视野三级行业', 17),
            '国标行业': ('国标行业', 18),
        }
        self._excel_name = self.name_add_date("add启元POC数据.xlsx")
        self.save_to_excel(field_cfg, result)

    def query_cmp_ind(self, names):
        result = dict()
        name_str = "','".join(names)
        sql_statement = """SELECT compName, categoryCode from sy_cd_ms_ind_comp_gm 
        where compName in ('{}') and dataStatus !=3;"""
        query_schema = dict(db_key="tidb_135", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            result.setdefault(item["compName"], item["categoryCode"])
        return result

    def query_mysql_ind(self):
        t_map = {3: "ind_m", 4: "ind_d", 5: "ind_z"}
        result = dict()
        sql_statement = """SELECT constCode, constValue, constValueDesc from sy_cd_mt_sys_const 
        where constCode in (3, 4, 5) and dataStatus !=3;"""
        query_schema = dict(db_key="tidb_135", sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        for item in result_list:
            new_item = dict()
            const_value = item.get("constValue")
            constCode = item["constCode"]
            field = t_map[constCode]
            constValueDesc = item.get("constValueDesc")
            new_item[field] = constValueDesc
            result.setdefault(const_value, dict())
            result[const_value].setdefault(field, constValueDesc)
        return result

    def read_excel_data(self, file_name, sheet="Sheet"):
        data_list = self._extract_data(file_name, sheet)
        return data_list


if __name__ == '__main__':
    p = AddIndustry()
    p.process()
