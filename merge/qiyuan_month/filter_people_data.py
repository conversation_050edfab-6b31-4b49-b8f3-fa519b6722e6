# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/02/19
from core.excel_base import ExcelBase


class PeopleData(ExcelBase):
    def __init__(self):
        super(PeopleData, self).__init__()

    def process(self):
        name_dict = self.people_merge_ind()
        for name, _ in name_dict.items():
            if self.query_cmp_industry([name]):
                print(name)

    def people_merge_ind(self):
        result = dict()
        file_name = self._in_file_path + "/启元POC数据整理20210126.xlsx"
        excel_data = self.read_excel_data(file_name, "Sheet1")
        for item in excel_data:
            ind = item.get("行业名称")
            name = item.get("公司名称")
            if ind and name:
                result.setdefault(name, ind)
        return result

    def read_excel_data(self, file_name, sheet="sheet1"):
        data_list = self._extract_data(file_name, sheet)
        return data_list

    def query_cmp_industry(self, names):
        sql_statement = """
        SELECT compName, industryId, IndustryName FROM `db_seeyii`.`sq_comp_all_industry` f WHERE f.compname in ({})
        """
        name_str = ','.join(['{!r}'.format(name) for name in names])
        query_schema = dict(db_key="db_seeyii_128", sql_statement=sql_statement.format(name_str))
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list


if __name__ == '__main__':
    p = PeopleData()
    p.process()
