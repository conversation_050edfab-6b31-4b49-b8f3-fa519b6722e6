# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/02/09
import re
from copy import deepcopy
from core.excel_base import ExcelBase


class NameFilter(ExcelBase):
    """测试脚本，与数据导出无关"""

    def __init__(self):
        super(NameFilter, self).__init__()
        self.file_name = self._in_file_path + "/待更新公司名单.xlsx"

    def process(self, *args, **kwargs):
        name_set = set()
        names_dict = self.read_excel_data(self.file_name, "guarantee_company - 副本")
        new_names = deepcopy(names_dict)
        num = 0
        for item in names_dict:
            name = item["name"].replace("(", "（").replace(")", "）").strip()
            if name in name_set:
                pass
                # print(name)
            else:
                name_set.add(name)
            if not self._name_suffix_process(name):
                print(name)
                num += 1
        print(f"过滤了={num}家")
        name_list = list(filter(self._name_suffix_process, list(name_set)))
        print(f"过滤前的数量={len(name_set)}, 过滤后的数量={len(list(name_list))}")
        alias_map, cur_map = self.query_alias_data(name_list)
        last_names = set()
        print(f"曾用名" + "#" * 30)
        for i in name_list:
            cur_name = cur_map.get(i)
            if cur_name:
                print(i, cur_name)
                # if cur_name in last_names:
                #     print("!" * 30)
                #     print(i)
                #     print("!" * 30)
                last_names.add(cur_name)
            else:
                # if i in last_names:
                #     print("!" * 30)
                #     print(i)
                #     print("!" * 30)
                last_names.add(i)
        print(f"替换括号的名单" + "#" * 30)
        for it in new_names:
            n = it["name"]
            if "(" in n or ")" in n:
                if n.replace("(", "（").replace(")", "）").strip() in last_names:
                    print(n)
        print(f"过完曾用名后名单数量={len(last_names)}")

    def query_alias_data(self, name_list):
        alias_cur_dict, cur_map = dict(), dict()
        for name in name_list:
            query_schema = {
                "db_name": "raw_data",
                "collection_name": "company_alias_name",
                "query_condition": {"$or": [{"alias_name": name}, {"cur_name": name}], "is_valid": True},
                "query_field": {"_id": 0, "alias_name": 1, "cur_name": 1}}
            query_result = self._data_server.call("query_item", query_schema)
            if query_result:
                for item in query_result:
                    cur_name = item.get("cur_name")
                    alias_name = item.get("alias_name")
                    cur_map.setdefault(alias_name, cur_name)
                    alias_cur_dict.setdefault(name, set())
                    alias_cur_dict[name].add(cur_name)
                    alias_cur_dict[name].add(alias_name)
            else:
                alias_cur_dict.setdefault(name, set())
                alias_cur_dict[name].add(name)
        return alias_cur_dict, cur_map

    def read_excel_data(self, file_name, sheet="sheet1"):
        data_list = self._extract_data(file_name, sheet)
        return data_list

    @staticmethod
    def _name_suffix_process(name):
        # 后缀：
        pattern = re.compile(r"(公司|有限合伙|\（有限合伙\）|普通合伙|\（普通合伙\）|投资中心|合伙企业|投资管理部)$")
        if pattern.findall(name):
            return name
        else:
            return None


if __name__ == '__main__':
    p = NameFilter()
    p.process()
