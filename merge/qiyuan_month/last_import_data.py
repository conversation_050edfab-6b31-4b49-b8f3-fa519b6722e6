# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/01/12
import decimal
import json
import os
import re
import xlsxwriter

from datetime import datetime, date

from cfg.config import out_file_path
from core.excel_base import ExcelBase
from moduler.company_category import CompanyCategory
from moduler.company_in_industry_return_cyl import CompanyInIndustryReturnCYL
from moduler.company_industry_and_cyl import CompanyIndustryAndCYL
from utils.datetime_util import DatetimeUtil


class QiYuan(ExcelBase):
    """
    加上人工整理的行业最后跑批
    """

    def __init__(self):
        super(QiYuan, self).__init__()
        self.cyl_ind_map = CompanyIndustryAndCYL()
        self.category_map = CompanyCategory()
        self.ind_and_cyl = CompanyInIndustryReturnCYL()
        self.category_list = [
            "SYAO2000", "SYAO3000", "SYAO4000",
            "SYAO5000", "SYAO6000", "SYAO7000",
            "SYAO8000", "SYAO9000", "SYAOB000",
            "SYAOC000", "SYAOD000", "SYAOF000"]
        self.__db_key = "seeyii"
        self.file_name = self._in_file_path + "/待更新公司名单.xlsx"

    def process(self, *args, **kwargs):
        name_set = set()
        result, all_ind_map, industry_list, num = list(), dict(), list(), 0
        merge_ind_dict = self.people_merge_ind()  # 人工整理的行业
        names_dict = self.read_excel_data(self.file_name, "guarantee_company - 副本")
        names_list = list({item["name"].replace("(", "（").replace(")", "）").strip() for item in names_dict})
        # names_list = ["云赛智联股份有限公司"]
        for idx in range(0, len(names_list), 50):
            names = list(filter(self._name_suffix_process, names_list[idx: idx + 50]))
            alias_map, cur_map = self.query_alias_data(names)  # 曾用名
            for name in names:
                tmp_item = dict()
                cur_name = cur_map.get(name, name)  # 获取当前名称
                sg_raw_ind = merge_ind_dict.get(cur_name)  # 人工整理的行业
                if sg_raw_ind:
                    sg_ind_dict = self.ind_and_cyl.run(sg_raw_ind)
                    if sg_ind_dict:
                        tmp_item["ind"] = sg_ind_dict["IndustryName"]
                        rg_cyl = sg_ind_dict.get("cyl_name")
                        if rg_cyl:
                            tmp_item["cyl"] = rg_cyl
                        all_ind_map.setdefault(sg_ind_dict["industryId"], sg_ind_dict["IndustryName"])
                        # 人工整理的行业添加到行业sheet中
                        industry_list.append({"IndustryName": sg_ind_dict["IndustryName"],
                                              "compName": cur_name, "is_zy": "主营"})
                    else:
                        print(sg_raw_ind)
                    category_info = self.category_map.run([cur_name])
                else:
                    is_cur = True
                    query_name = [cur_name]
                    cyl_data = self.cyl_ind_map.run(query_name)
                    if not cyl_data:
                        query_name = list(alias_map[name])
                        if len(query_name) != 1:
                            cyl_data = self.cyl_ind_map.run(query_name)
                            is_cur = False
                    category_info = self.category_map.run(query_name)

                    ind_set, cyl_set = set(), set()
                    for _, cyl_list in cyl_data.items():
                        for cyl in cyl_list:
                            ind_name = cyl.get("IndustryName")
                            ind_id = cyl.get("industryId")
                            if ind_name and ind_id:
                                ind_set.add(ind_name)
                                all_ind_map.setdefault(ind_id, ind_name)  # 整合全部行业
                            cyl_name = cyl.get("cyl_name")
                            if cyl_name:
                                cyl_set.add(cyl_name)
                            if not is_cur:
                                print(name, query_name)
                                break
                    if cyl_set:
                        tmp_item["cyl"] = ','.join(list(cyl_set))
                    if ind_set:
                        tmp_item["ind"] = ','.join(list(ind_set))

                cat_list = list()
                for n, i in category_info.items():
                    cat_list.extend(i.get("category", list()))
                tmp_item["name"] = cur_name
                if cur_name in name_set:
                    continue
                name_set.add(cur_name)
                for category in self.category_list:
                    if category == "SYAO8000":
                        if "SYAO80000" in ",".join(cat_list):
                            tmp_item[category] = "未评级"
                        elif "SYAO80001" in ",".join(cat_list):
                            tmp_item[category] = "市级"
                        elif "SYAO80002" in ",".join(cat_list):
                            tmp_item[category] = "省级"
                        elif "SYAO80003" in ",".join(cat_list):
                            tmp_item[category] = "国家级"
                        else:
                            tmp_item[category] = "否"
                    else:
                        if category in ",".join(cat_list):
                            tmp_item[category] = "是"
                        else:
                            tmp_item[category] = "否"
                result.append(tmp_item)

                num += 1
                print(num)
        industry_list.extend(self.ind_data_process(all_ind_map))
        self.save_data_excel(result, industry_list)

    def query_alias_data(self, name_list):
        alias_cur_dict, cur_map = dict(), dict()
        for name in name_list:
            query_schema = {
                "db_name": "raw_data",
                "collection_name": "company_alias_name",
                "query_condition": {"$or": [{"alias_name": name}, {"cur_name": name}], "is_valid": True},
                "query_field": {"_id": 0, "alias_name": 1, "cur_name": 1}}
            query_result = self._data_server.call("query_item", query_schema)
            if query_result:
                for item in query_result:
                    cur_name = item.get("cur_name")
                    alias_name = item.get("alias_name")
                    cur_map.setdefault(alias_name, cur_name)
                    alias_cur_dict.setdefault(name, set())
                    alias_cur_dict[name].add(cur_name)
                    alias_cur_dict[name].add(alias_name)
            else:
                alias_cur_dict.setdefault(name, set())
                alias_cur_dict[name].add(name)
        return alias_cur_dict, cur_map

    def save_data_excel(self, result, industry_list):
        field_cfg = {
            'name': ('公司名称', 0),
            'cyl': ('产业链名称', 1),
            'ind': ('行业名称', 2),
            'SYAO2000': ('是否科技型中小企业', 3),
            'SYAO3000': ('是否技术创新示范企业', 4),
            'SYAO4000': ('是否专精特新/小巨人企业', 5),
            'SYAO5000': ('是否拥有工业设计中心', 6),
            'SYAO6000': ('是否技术先进型服务企业', 7),
            'SYAO7000': ('是否拥有工程技术研究中心', 8),
            'SYAO8000': ('是否拥有企业重点实验室', 9),
            'SYAO9000': ('是否拥有院士专家工作站', 10),
            'SYAOB000': ('是否服务型制造示范企业', 11),
            'SYAOC000': ('是否新型研发机构', 12),
            'SYAOD000': ('是否拥有企业技术中心', 13),
            'SYAOF000': ('是否拥有高新技术企业研究开发中心', 14)}
        industry_cfg = {
            'IndustryName': ('行业名称', 0),
            'compName': ('公司名称', 1),
            'is_zy': ('主营/非主营', 2)}
        self._excel_name = self.name_add_date("启元POC数据.xlsx")
        output_file_path = os.path.join(out_file_path, self._excel_name)
        xls_file = xlsxwriter.Workbook(output_file_path)
        self._save_to_excel(field_cfg, {"公司信息": result}, xls_file)
        num = 0
        ind_dict = dict()
        for idx in range(0, len(industry_list), 1000000):
            r_industry_list = industry_list[idx:idx + 1000000]
            num += 1
            ind_dict.setdefault(f"行业信息({num})", r_industry_list)
        self._save_to_excel(industry_cfg, ind_dict, xls_file)
        xls_file.close()
        print("finished.")

    def _save_to_excel(self, field_dict, sheet_item_dict, xls_file):
        """
        :param field_dict: {
            eg:  "字段名": (u"字段含义", 插入的列号),
                 "cname": (u"公司名称", 0)
        }
        :param sheet_item_dict: {
            Sheet名称:[data]
        }
        :return:
        """
        for sheet_name, item_list in sheet_item_dict.items():
            xls_sheet = xls_file.add_worksheet(name=sheet_name)
            for title in field_dict.values():
                xls_sheet.write(0, title[1], title[0])
            row_no = 1
            for result_item in item_list:
                for field_name, title in field_dict.items():
                    field_value = result_item.get(field_name, "")
                    if isinstance(field_value, decimal.Decimal):
                        field_value = float(field_value)
                    if isinstance(field_value, datetime):
                        field_value = DatetimeUtil.date_to_str(field_value)
                    if isinstance(field_value, date):
                        field_value = field_value.strftime("%Y-%m-%d")
                    if not isinstance(field_value, str):
                        field_value = json.dumps(field_value, ensure_ascii=False)
                    if field_value == "null" or field_value is None:
                        continue
                    xls_sheet.write(row_no, title[1], field_value)
                row_no += 1

    def read_excel_data(self, file_name, sheet="sheet1"):
        data_list = self._extract_data(file_name, sheet)
        return data_list

    def people_merge_ind(self):
        result = dict()
        file_name = self._in_file_path + "/启元POC数据整理20210126.xlsx"
        excel_data = self.read_excel_data(file_name, "Sheet1")
        for item in excel_data:
            ind = item.get("行业名称")
            name = item.get("公司名称")
            if ind and name:
                result.setdefault(name, ind)
        return result

    def ind_data_process(self, all_ind_map):
        result, filter = list(), set()
        ind_id_list = set(all_ind_map.keys())
        for ind_id in ind_id_list:
            for data_list in self.query_industry_data(ind_id):
                for item in data_list:
                    industryId = item["industryId"]
                    comp_name = item["compName"]
                    if not self._name_len_process(comp_name):
                        continue
                    if not self._name_prefix_process(comp_name):
                        continue
                    if not self._name_suffix_process(comp_name):
                        continue
                    _str = comp_name + str(industryId)
                    if _str in filter:
                        continue
                    filter.add(_str)
                    result.append(item)
        return result

    def query_industry_data(self, ind_id):
        sql_statement_list = [
            """
            SELECT id, compName, industryId, IndustryName,  "主营" as is_zy FROM `sq_comp_all_industry`  WHERE 
            id>{} and industryId=%s ORDER BY id ASC limit 1000;""" % ind_id,
            """
            SELECT a.id, companyname as compName, industryId, IndustryName,  "非主营" as is_zy FROM 
            `sq_sk_basicinfo` a, sq_comp_industry b  WHERE  a.secucode=b.secucode 
            and industryId=%s and a.id>{} and b.isMainIndustry=0 ORDER BY a.id ASC limit 1000;
            """ % ind_id]
        for sql_statement in sql_statement_list:
            obj_id, data_list, num = None, list(), 0
            while True:
                if obj_id is None:
                    query_schema = dict(db_key=self.__db_key, sql_statement=sql_statement.format(0))
                else:
                    query_schema = dict(db_key=self.__db_key, sql_statement=sql_statement.format(obj_id))
                result_list = self._data_server.call("query_sql_item", query_schema)
                num += 1
                if len(result_list) == 0:
                    break
                yield result_list
                obj_id = result_list[-1]["id"]

    @staticmethod
    def _name_len_process(name):
        # 长度=<4 or 长度>=30
        if len(name) <= 4 or len(name) >= 30:
            return None
        else:
            return name.strip()

    @staticmethod
    def _name_prefix_process(name):
        # 前缀：[(，（]
        pattern = re.compile(u"^[\?\[(（:：\.\*）a-zA-Z\d〔①？]")
        if pattern.findall(name):
            return None
        else:
            return name

    @staticmethod
    def _name_suffix_process(name):
        # 后缀：
        pattern = re.compile(r"(公司|有限合伙|\（有限合伙\）|普通合伙|\（普通合伙\）|投资中心|合伙企业|投资管理部)$")
        if pattern.findall(name):
            return name
        else:
            return None


if __name__ == '__main__':
    p = QiYuan()
    p.process()
