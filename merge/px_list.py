# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022-9-23
from copy import deepcopy

from core.excel_base import ExcelBase


class PXList(ExcelBase):
    def __init__(self):
        super(PXList, self).__init__()
        self.field = ["spectrumId", "spectrumName", "nameType", "fingerId", "dataStatus"]

    def process(self):
        result = list()
        for item in self.__gen_spt_info():
            _item = dict()
            feature_id = item["feature_id"]
            _item["nameType"] = 1
            _item["spectrumId"] = feature_id
            _item["spectrumName"] = item["spectrum_name"]
            _item["fingerId"] = self.get_finger_id(_item, ["spectrumId", "spectrumName", "nameType"])
            _item["dataStatus"] = 1
            result.append(_item)
            alias_name = item.get("alias_name")
            if alias_name:
                for alias in alias_name:
                    alias_item = dict()
                    alias_item["nameType"] = 2
                    alias_item["spectrumId"] = feature_id
                    alias_item["spectrumName"] = alias
                    alias_item["fingerId"] = self.get_finger_id(alias_item, ["spectrumId", "spectrumName", "nameType"])
                    alias_item["dataStatus"] = 1
                    result.append(alias_item)
        print(len(result))
        print(result[:5])
        self.save_to_mysql(self.field, result, "dwd_ms_cn_px_cmn_list")

    def get_finger_id(self, data, field):
        raw_data = self.fetch_dict(data, field)
        return self._gen_common_graph_id(raw_data)

    def save_to_mysql(self, valid_fields, result_list, table):
        save_schema = {
            "insert_template": "insert into {} (%s) values %s".format(table),
            "result_list": result_list,
            "field_list": valid_fields,
            "db_key": "seeyii_db"}
        self._data_server.call("insert_sql_list", save_schema)

    def __gen_spt_info(self):
        result = list()
        query_condition = dict()
        offset = None
        while True:
            if offset:
                query_condition["_id"] = {"$gt": offset}
            query_item = {
                "db_name": "raw_data",
                "collection_name": "investor_spectrum_info_v2",
                "query_condition": query_condition,
                "query_field": {
                    '_id': 1, "feature_id": 1, "spectrum_name": 1, "alias_name": 1},
                "sort_field": [("_id", 1)],
                "limit_n": 100}
            query_result = self._data_server.call("query_item", query_item)
            if not query_result:
                break
            result.extend(query_result)
            offset = query_result[-1]["_id"]
        return result


if __name__ == '__main__':
    p = PXList()
    p.process()
