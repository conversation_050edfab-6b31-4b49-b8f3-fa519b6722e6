# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2021/03/22
import re
import os
import json
import decimal
import xlsxwriter
from copy import deepcopy
from collections import defaultdict
from cfg.config import out_file_path
from datetime import datetime, date

from core.excel_base import ExcelBase

from utils.datetime_util import DatetimeUtil


class SchoolData(ExcelBase):
    """
    高校近三个月内招投标、重大固定资产项目立项、投融资信息
    """

    def __init__(self):
        super(SchoolData, self).__init__()
        self.sql_tmp = {
            "重大在建项目": """
                SELECT * from ext_build_project where publishDate BETWEEN '2020-12-22' AND '2021-03-31' and ownerCompany LIKE "%技校%"
                UNION 
                SELECT * from ext_build_project where publishDate BETWEEN '2020-12-22' AND '2021-03-31' and ownerCompany LIKE "%大学%" 
                UNION 
                SELECT * from ext_build_project where publishDate BETWEEN '2020-12-22' AND '2021-03-31' and ownerCompany LIKE "%学院%"
                UNION 
                SELECT * from ext_build_project where publishDate BETWEEN '2020-12-22' AND '2021-03-31' and ownerCompany LIKE "%学校%"
                UNION 
                SELECT * from ext_build_project where publishDate BETWEEN '2020-12-22' AND '2021-03-31' and ownerCompany LIKE "%中学%"
                UNION 
                SELECT * from ext_build_project where publishDate BETWEEN '2020-12-22' AND '2021-03-31' and ownerCompany LIKE "%高中%"
                UNION 
                SELECT * from ext_build_project where publishDate BETWEEN '2020-12-22' AND '2021-03-31' and ownerCompany LIKE "%初中%"
                UNION 
                SELECT * from ext_build_project where publishDate BETWEEN '2020-12-22' AND '2021-03-31' and ownerCompany LIKE "%小学%"
                UNION 
                SELECT * from ext_build_project where publishDate BETWEEN '2020-12-22' AND '2021-03-31' and ownerCompany LIKE "%幼儿园%"
                UNION 
                SELECT * from ext_build_project where publishDate BETWEEN '2020-12-22' AND '2021-03-31' and ownerCompany LIKE "%教育集团%"
                UNION 
                SELECT * from ext_build_project where publishDate BETWEEN '2020-12-22' AND '2021-03-31' and ownerCompany LIKE "%教育%"
                UNION 
                SELECT * from ext_build_project where publishDate BETWEEN '2020-12-22' AND '2021-03-31' and ownerCompany LIKE "%校友会%"
            """,
            "招投标事件": """SELECT * from merge_bidder_info where bidderTimeConfirm BETWEEN '2020-12-22' AND '2021-03-31';""",
            "融资新闻": """SELECT * from investment_event where updateTime BETWEEN '2020-12-22' AND '2021-03-31';""",
            "PEVC投资": """
                SELECT * from dws_me_trad_inv_event where  subject LIKE "%技校%"
                UNION 
                SELECT * from dws_me_trad_inv_event where   subject LIKE "%大学%" 
                UNION 
                SELECT * from dws_me_trad_inv_event where  subject LIKE "%学院%"
                UNION 
                SELECT * from dws_me_trad_inv_event where  subject LIKE "%学校%"
                UNION 
                SELECT * from dws_me_trad_inv_event where  subject LIKE "%中学%"
                UNION 
                SELECT * from dws_me_trad_inv_event where  subject LIKE "%高中%"
                UNION 
                SELECT * from dws_me_trad_inv_event where  subject LIKE "%初中%"
                UNION 
                SELECT * from dws_me_trad_inv_event where  subject LIKE "%小学%"
                UNION 
                SELECT * from dws_me_trad_inv_event where  subject LIKE "%幼儿园%"
                UNION 
                SELECT * from dws_me_trad_inv_event where  subject LIKE "%教育集团%"
                UNION 
                SELECT * from dws_me_trad_inv_event where  subject LIKE "%教育%"
                UNION 
                SELECT * from dws_me_trad_inv_event where  subject LIKE "%校友会%"
                """}
        self.sql_key = {
            "重大在建项目": "db_seeyii_128",
            "招投标事件": "db_seeyii_128",
            "融资新闻": "db_seeyii_128",
            "PEVC投资": "db_seeyii"}
        self.field_map = {
            "重大在建项目": {
                'ownerCompany': ('业主单位名称', 0),
                'projectName': ('项目名称', 1),
                'provinceArea': ('地区', 2),
                'publishDate': ('发布日期', 3),
                'industry': ('行业', 4),
                'period': ('建设周期', 5),
                'progress': ('进展阶段', 6),
                'amount': ('总投资金额（万元）', 7),
                'location': ('所在地', 8),
                'scale': ('建设内容及规模', 9),
                'content': ('项目简介', 10),
                'hostUnits': ('举办单位', 11),
                'legalPerson': ('法定代表人', 12),
                'openFund': ('开办资金（万元）', 13),
                'creditId': ('统一社会信用代码', 14),
                'address': ('住所', 15),
                'serviceScale': ('宗旨和业务范围', 16),
            },
            "招投标事件": {
                'projectInitiatorName': ('招标单位', 0),
                'projectBidderName': ('中标单位', 1),
                'bidderNO': ('招标编号', 2),
                'projectName': ('招标投标项目名称', 3),
                'bidderProxy': ('招标代理人', 4),
                'bidderTime': ('中标时间', 5),
                'amount': ('金额', 6),
                'websiteName': ('招标投标机构名称', 7),
                'proviceName': ('省', 8),
                'city': ('市', 9),
                'district': ('区', 10),
                'contractName': ('合同名称', 11),
                'name': ('单位名称', 12),
                'hostUnits': ('举办单位', 13),
                'legalPerson': ('法定代表人', 14),
                'openFund': ('开办资金（万元）', 15),
                'creditId': ('统一社会信用代码', 16),
                'address': ('住所', 17),
                'serviceScale': ('宗旨和业务范围', 18),
            },
            "融资新闻": {
                'investmentName': ('投资方', 0),
                'companyName': ('融资方简称', 1),
                'companyFullName': ('融资方公司全称', 2),
                'investmentAmount': ('金额', 3),
                'investmentTurn': ('融资轮次', 4),
                'updateTime': ('融资时间', 5),
                'introduction': ('事件简介', 6),
                'industryName': ('行业名称', 7),
                'province': ('省', 8),
                'city': ('市', 9),
                'district': ('区', 10),
                'name': ('单位名称', 11),
                'hostUnits': ('举办单位', 12),
                'legalPerson': ('法定代表人', 13),
                'openFund': ('开办资金（万元）', 14),
                'creditId': ('统一社会信用代码', 15),
                'address': ('住所', 16),
                'serviceScale': ('宗旨和业务范围', 17),
            },
            "PEVC投资": {
                'subject': ('投资主体', 0),
                'compName': ('被投企业', 1),
                'chgType': ('投资类型', 2),
                'chgDate': ('投资日期', 3),
                'chgPrice': ('投资价格', 4),
                'chgRatio': ('变更股比', 5),
                'chgAmount': ('投资数量', 6),
                'chgCash': ('投资金额', 7),
                'hostUnits': ('举办单位', 8),
                'legalPerson': ('法定代表人', 9),
                'openFund': ('开办资金（万元）', 10),
                'creditId': ('统一社会信用代码', 11),
                'address': ('住所', 12),
                'serviceScale': ('宗旨和业务范围', 13),
            }
        }
        self.pattern = re.compile(r"(大学|学院|技校|学校|中学|高中|初中|小学|幼儿园|教育集团|教育|校友会)")
        self.inv_type = {
            101: "投资进入",
            201: "市场增持（A股）",
            301: "市场增持（三板）",
            401: "定增进入（A股）",
            501: "定增进入（三板）",
            701: "IPO配售"}

    def process(self):
        output_file_path = os.path.join(out_file_path, self.name_add_date("院校POC.xlsx"))
        xls_file = xlsxwriter.Workbook(output_file_path, options={'strings_to_urls': False})
        for title, sql in self.sql_tmp.items():
            _k = self.sql_key[title]
            field_dict = self.field_map[title]
            result_data = self.query_mysql_data(_k, sql)
            data = self.data_process(title, result_data)
            xls_sheet = xls_file.add_worksheet(name=title)
            self._save_to_excel(field_dict, xls_sheet, data)
        xls_file.close()

    def data_process(self, title, result_data):
        result, name_set = list(), set()
        if title == "重大在建项目":
            name_set = {item["ownerCompany"] for item in result_data}
            if not name_set:
                return []
            base_dict = self.query_sydw_data(list(name_set))
            for item in result_data:
                name = item["ownerCompany"]
                base_info = base_dict.get(name) or dict()
                item.update(base_info)
                result.append(item)
        elif title == "PEVC投资":
            tmp_list = list()
            for item in result_data:
                chg_date = item.get("chgDate")
                if not chg_date or chg_date < date(2020, 12, 22):
                    continue
                name = item["subject"]
                name_set.add(name)
                tmp_list.append(item)
            if not name_set:
                return []
            base_dict = self.query_sydw_data(list(name_set))
            for n_item in tmp_list:
                name = n_item["subject"]
                inv_t = self.inv_type[n_item["chgType"]]
                n_item["chgType"] = inv_t
                base_info = base_dict.get(name) or dict()
                n_item.update(base_info)
                result.append(n_item)

        elif title == "招投标事件":
            tmp_dict, event = defaultdict(list), set()
            for item in result_data:
                finger = self._gen_common_graph_id(item)
                if finger in event:
                    continue
                event.add(finger)
                n1 = item.get("projectInitiatorName") or ""
                if self.pattern.findall(n1):
                    tmp_dict[n1].append(item)
                    continue
                n2 = item.get("projectBidderName") or ""
                if self.pattern.findall(n2):
                    tmp_dict[n2].append(item)
            base_dict = self.query_sydw_data(list(set(tmp_dict.keys())))
            for name, n_item_list in tmp_dict.items():
                base_info = base_dict.get(name) or dict()
                for i in n_item_list:
                    i.update(base_info)
                    i["name"] = name
                    result.append(i)

        elif title == "融资新闻":
            tmp_dict = defaultdict(list)
            for item in result_data:
                inv_list = json.loads(item.get("investmentName"))
                if "address" in item:
                    item.pop("address")
                orgname_list = list()
                for inv in inv_list:
                    orgname = inv.get("orgname") or ""
                    orgname_list.append(orgname)
                    if self.pattern.findall(orgname):
                        name_set.add(orgname)
                        tmp_dict[orgname].append(item)
                item["investmentName"] = ",".join(orgname_list)
            base_dict = self.query_sydw_data(list(name_set))
            for org, item_list in tmp_dict.items():
                base_info = base_dict.get(org) or dict()
                for i in item_list:
                    i.update(base_info)
                    i["name"] = org
                    result.append(i)
        return result

    def query_mysql_data(self, db_key, sql_statement):
        query_schema = dict(db_key=db_key, sql_statement=sql_statement)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list

    def query_sydw_data(self, company_list):
        result = dict()
        for idx in range(0, len(company_list), 100):
            names = company_list[idx: idx + 100]
            name_str = ','.join(['{!r}'.format(name) for name in names])
            sql = """
            select cName, legalPerson, openFund, creditId, address, serviceScale, hostUnits
            from dwd_ms_cn_sydw_base_info where cName in ({});"""
            query_schema = dict(db_key="seeyii_db", sql_statement=sql.format(name_str))
            result_list = self._data_server.call("query_sql_item", query_schema) or list()
            for item in result_list:
                name = item["cName"]
                result.setdefault(name, item)
        return result

    @staticmethod
    def _save_to_excel(field_dict, xls_sheet, item_list):
        for title in field_dict.values():
            xls_sheet.write(0, title[1], title[0])
        row_no = 1
        for result_item in item_list:
            for field_name, title in field_dict.items():
                field_value = result_item.get(field_name, "")
                if isinstance(field_value, decimal.Decimal):
                    field_value = float(field_value)
                if isinstance(field_value, datetime):
                    field_value = DatetimeUtil.date_to_str(field_value)
                if isinstance(field_value, date):
                    field_value = field_value.strftime("%Y-%m-%d")
                if not isinstance(field_value, str):
                    field_value = json.dumps(field_value, ensure_ascii=False)
                if field_value == "null":
                    continue
                xls_sheet.write(row_no, title[1], field_value)
            row_no += 1
        print("finished.")


if __name__ == '__main__':
    p = SchoolData()
    p.process()
