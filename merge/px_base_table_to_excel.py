# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022-9-19
import decimal
import json
import os
import xlsxwriter
from datetime import datetime, date
from cfg.config import out_file_path
from utils.datetime_util import DatetimeUtil
from copy import deepcopy
from collections import defaultdict
from core.excel_base import ExcelBase
from moduler.company_holder import CompanyHolder


class PX(ExcelBase):
    def __init__(self):
        super(PX, self).__init__()
        self.save_model = {
            "sy_cd_ms_base_pe_equ_info": {
                'cName': ('名称', 0),
                'legalRepresent': ('法定代表人', 1),
                'shareholder_name': ('股东', 2),
                'holdRatio': ('持股比例', 3),
            },
            "sy_cd_mm_cn_smjj_equ_info": {
                'cName': ('名称', 0),
                'legalRepresent': ('法定代表人', 1),
                'manageName': ('管理人', 2),
            },
            "sy_cd_ms_base_pe_wba_equ_info": {
                'compName': ('名称', 0),
                'legalPersonName': ('法定代表人', 1),
                'shareholder_name': ('股东', 2),
                'holdRatio': ('持股比例', 3),
            },
            "sy_cd_mm_cn_smjj_wba_equ_info": {
                'compName': ('名称', 0),
                'legalPersonName': ('法定代表人', 1),
            },
            "sy_cd_ms_base_pe_oth_equ_info": {
                'compName': ('名称', 0),
                'legalPersonName': ('法定代表人', 1),
                'shareholder_name': ('股东', 2),
                'holdRatio': ('持股比例', 3),
                'confLevel': ('可信度', 4),
            },
        }
        self.k_v = {
            "sy_cd_ms_base_pe_equ_info": "私募股权类机构基本信息表（备案）",
            "sy_cd_ms_base_pe_wba_equ_info": "私募股权类机构基本信息表（未备案）",
            "sy_cd_mm_cn_smjj_equ_info": "私募股权类基金基本信息表（备案）",
            "sy_cd_mm_cn_smjj_wba_equ_info": "私募股权类基金基本信息表（未备案）",
            "sy_cd_ms_base_pe_oth_equ_info": "其他私募股权类机构基本信息表",
        }

    def process(self, *args, **kwargs):
        result = defaultdict(list)
        # 1，已备案私募股权类投资机构，字段：名称、法人、股东、持股比例  sy_cd_ms_base_pe_equ_info
        org_dict = self.query_org_raw_data()
        org_holder = CompanyHolder.run(list(org_dict.keys()))
        for name, holders in org_holder.items():
            fr_dict = org_dict[name]
            for h in holders:
                h.update(fr_dict)
                result["sy_cd_ms_base_pe_equ_info"].append(h)
        print(2)
        # 2，已备案私募股权类投资基金，字段：名称、法人、管理人 sy_cd_mm_cn_smjj_equ_info
        fund_dict = self.query_fund_raw_data()
        fund_holder = CompanyHolder.run(list(fund_dict.keys()))
        for name, holders in fund_holder.items():
            fr_dict = fund_dict[name]
            for h in holders:
                h.update(fr_dict)
                result["sy_cd_mm_cn_smjj_equ_info"].append(h)
        print(3)
        # 3，未备案私募股权类投资机构，字段：名称、法人、股东、持股比例 sy_cd_ms_base_pe_wba_equ_info
        org_dict_1 = self.query_org_raw_data_2()
        org_holder_1 = CompanyHolder.run(list(org_dict_1.keys()))
        for name, holders in org_holder_1.items():
            fr_dict = org_dict_1[name]
            for h in holders:
                h.update(fr_dict)
                result["sy_cd_ms_base_pe_wba_equ_info"].append(h)
        print(4)
        # 4，未备案私募股权类投资基金，字段：名称、法人 sy_cd_mm_cn_smjj_wba_equ_info
        fund_list = self.query_fund_raw_data_1()
        result["sy_cd_mm_cn_smjj_wba_equ_info"].extend(fund_list)
        print(5)
        # 5，其他私募投资机构，字段：名称、法人、股东、持股比例、可信度 sy_cd_ms_base_pe_oth_equ_info
        org_dict_2 = self.query_org_raw_data_3()
        org_holder_2 = CompanyHolder.run(list(org_dict_2.keys()))
        for name, holders in org_holder_2.items():
            fr_dict = org_dict_2[name]
            for h in holders:
                h.update(fr_dict)
                result["sy_cd_ms_base_pe_oth_equ_info"].append(h)
        print("save")
        self.save_data_excel(result)

    def save_data_excel(self, result):
        self._excel_name = self.name_add_date("谱系基础表数据.xlsx")
        output_file_path = os.path.join(out_file_path, self._excel_name)
        xls_file = xlsxwriter.Workbook(output_file_path)
        for name, data_list in result.items():
            field_cfg = self.save_model[name]
            k = self.k_v[name]
            self._save_to_excel(field_cfg, {k: data_list}, xls_file)
        xls_file.close()
        print("finished.")

    def query_org_raw_data(self):
        sql = """
        select cName, legalRepresent from sy_cd_ms_base_pe_equ_info where dataStatus!=3
        """
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return {i["cName"]: i for i in result_list}

    def query_org_raw_data_2(self):
        sql = """
           select compName, legalPersonName
            from sy_cd_ms_base_pe_wba_equ_info where dataStatus!=3
           """
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return {i["compName"]: i for i in result_list}

    def query_org_raw_data_3(self):
        sql = """
           select compName, legalPersonName,confLevel
            from sy_cd_ms_base_pe_oth_equ_info where dataStatus!=3
           """
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return {i["compName"]: i for i in result_list}

    def query_fund_raw_data(self):
        sql = """
                select  a.fundName as cName, 
               b.legalPersonName as legalRepresent,a.manageName
                from sy_cd_mm_cn_smjj_equ_info as a 
                left join sy_cd_ms_base_gs_comp_info_new as b on
                a.fundName=b.compName and b.dataStatus!=3 and a.dataStatus!=3 
        """
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return {i["cName"]: i for i in result_list}

    def query_fund_raw_data_1(self):
        sql = """
        select compName, legalPersonName from sy_cd_mm_cn_smjj_wba_equ_info where dataStatus!=3 
        """
        query_schema = dict(db_key="polardb_seeyii_assets_database", sql_statement=sql)
        result_list = self._data_server.call("query_sql_item", query_schema) or list()
        return result_list

    def _save_to_excel(self, field_dict, sheet_item_dict, xls_file):
        """
        :param field_dict: {
            eg:  "字段名": (u"字段含义", 插入的列号),
                 "cname": (u"公司名称", 0)
        }
        :param sheet_item_dict: {
            Sheet名称:[data]
        }
        :return:
        """
        for sheet_name, item_list in sheet_item_dict.items():
            xls_sheet = xls_file.add_worksheet(name=sheet_name)
            for title in field_dict.values():
                xls_sheet.write(0, title[1], title[0])
            row_no = 1
            for result_item in item_list:
                for field_name, title in field_dict.items():
                    field_value = result_item.get(field_name, "")
                    if isinstance(field_value, decimal.Decimal):
                        field_value = float(field_value)
                    if isinstance(field_value, datetime):
                        field_value = DatetimeUtil.date_to_str(field_value)
                    if isinstance(field_value, date):
                        field_value = field_value.strftime("%Y-%m-%d")
                    if not isinstance(field_value, str):
                        field_value = json.dumps(field_value, ensure_ascii=False)
                    if field_value == "null" or field_value is None:
                        continue
                    xls_sheet.write(row_no, title[1], field_value)
                row_no += 1


if __name__ == '__main__':
    p = PX()
    p.process()
