# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022-9-26
from collections import defaultdict
from copy import deepcopy
from core.excel_base import ExcelBase


class PX(ExcelBase):
    def __init__(self):
        super(PX, self).__init__()
        # self.table_name = "dwd_ms_cn_px_pdt_info"
        # self.table_name = "dwd_ms_cn_px_invo_info"
        self.name_map = {
            "sy_cd_ms_cn_px_fms_pdt_info": "基金",
            "sy_cd_ms_cn_px_fms_invo_info": "机构",
        }

    def process(self, *args, **kwargs):
        result = list()
        px_old_map = self.gen_px_info()
        px_name_map = self.__gen_spt_info()
        px_inc, px_del = self.px_status_map(px_old_map, px_name_map)
        print(f"px_inc={len(px_inc)}")
        print(f"px_inc={len(px_del)}")
        for table in ["sy_cd_ms_cn_px_fms_pdt_info", "sy_cd_ms_cn_px_fms_invo_info"]:
            print(f"=============================={table}==============================")
            finger_id_157_list = self.query_fund_157_data(table + "_tmp0331")
            data_157_dict = self.add_finger_id(finger_id_157_list, table)
            print(f"data_157_dict = {len(data_157_dict)}")
            finger_id_124 = self.query_fund_124_data(table)
            data_124_dict = self.add_finger_id(finger_id_124, table)
            print(f"data_124_dict = {len(data_124_dict)}")
            add = data_157_dict.keys() - data_124_dict.keys()
            print(f"inc={len(add)}")
            _del = data_124_dict.keys() - data_157_dict.keys()
            print(f"del={len(_del)}")
            jj = data_157_dict.keys() & data_124_dict.keys()
            print(f"jj={len(jj)}")
            for f_id, item in data_157_dict.items():
                if f_id in add:
                    px_id = item["pedigreeId"]
                    name = px_name_map.get(px_id)
                    if not name:
                        name = px_old_map.get(px_id)
                    if px_id in px_inc:
                        item["px_status"] = "新增"
                    elif px_id in px_del:
                        item["px_status"] = "删除"
                    else:
                        item["px_status"] = "不变"
                    # if not name:
                    #     continue
                    item["px_name"] = name
                    item["px_id"] = px_id
                    item["data_status"] = "新增"
                    item["table"] = self.name_map[table]
                    result.append(item)
                if f_id in jj:
                    px_id = item["pedigreeId"]
                    name = px_name_map.get(px_id)
                    if not name:
                        name = px_old_map.get(px_id)
                    if px_id in px_inc:
                        item["px_status"] = "新增"
                    elif px_id in px_del:
                        item["px_status"] = "删除"
                    else:
                        item["px_status"] = "不变"
                    # if not name:
                    #     continue
                    item["px_name"] = name
                    item["px_id"] = px_id
                    item["data_status"] = "不变"
                    item["table"] = self.name_map[table]
                    result.append(item)

            for f_id, item in data_124_dict.items():
                if f_id in _del:
                    px_id = item["pedigreeId"]
                    name = px_name_map.get(px_id)
                    if not name:
                        name = px_old_map.get(px_id, px_id)
                    if px_id in px_inc:
                        item["px_status"] = "新增"
                    elif px_id in px_del:
                        item["px_status"] = "删除"
                    else:
                        item["px_status"] = "不变"
                    item["px_name"] = name
                    item["px_id"] = px_id
                    item["data_status"] = "删除"
                    item["table"] = self.name_map[table]
                    result.append(item)
            # print(f"result={len(result[table])}")
        self.save_data_excel({"sheet": result})

    @staticmethod
    def px_status_map(px_old_map, px_name_map):
        inc = px_name_map.keys() - px_old_map.keys()
        _del = px_old_map.keys() - px_name_map.keys()
        return inc, _del

    def save_data_excel(self, result):
        field_cfg = {
            'px_id': ('谱系id', 0),
            'px_name': ('谱系名称', 1),
            'px_status': ('谱系状态', 2),
            'compname': ('名称', 3),
            'data_status': ('数据状态', 4),
            'table': ('数据源', 5),
        }
        self._excel_name = self.name_add_date(f"谱系变化明细.xlsx")
        self.save_to_excel(field_cfg, result)

    def add_finger_id(self, data_list, table):
        result = dict()
        for item in data_list:
            item.pop("id")
            finger_id = self._gen_graph_id(item)
            item["finger_id"] = finger_id
            item["table"] = table
            # if finger_id in result:
            #     print(f"数据库重复数据={item}")
            result[finger_id] = item
        return result

    def query_fund_157_data(self, table):
        result_list = list()
        sql = """
              select id, pedigreeId, compname from %s where dataStatus!=3 and id > {} ORDER BY id ASC limit 1000
              """ % table
        for items in self._query_sql_iter_by_id(sql, "seeyii_assets_database_63306"):
            result_list.extend(items)
        print(f"157_result_list={len(result_list)}", sql)
        return result_list

    def query_fund_124_data(self, table):
        result_list = list()
        sql = """
              select id, pedigreeId, compname from %s where dataStatus!=3 and id > {} ORDER BY id ASC limit 1000
              """ % table
        for items in self._query_sql_iter_by_id(sql, "seeyii_assets_database_63306"):
            result_list.extend(items)
        print(f"124_result_list={len(result_list)}", sql)
        return result_list

    def __gen_spt_info(self):
        result = dict()
        # query_condition = dict()
        # offset = None
        # while True:
        #     if offset:
        #         query_condition["_id"] = {"$gt": offset}
        #     query_item = {
        #         "db_name": "raw_data",
        #         "collection_name": "investor_spectrum_info_v2",
        #         "query_condition": query_condition,
        #         "query_field": {
        #             '_id': 1, "feature_id": 1, "spectrum_name": 1, "keyword_1": 1},
        #         "sort_field": [("_id", 1)],
        #         "limit_n": 1000}
        #     query_result = self._data_server.call("query_item", query_item)
        #     if not query_result:
        #         break
        #     for item in query_result:
        #         result[item["feature_id"]] = item["spectrum_name"]
        #     offset = query_result[-1]["_id"]
        sql = """
              select id, pedigreeId, pedigreeName from %s where dataStatus!=3 and id > {} ORDER BY id ASC limit 1000
              """ % "sy_cd_ms_cn_px_fms_info_tmp0331"
        for items in self._query_sql_iter_by_id(sql, "seeyii_assets_database_63306"):
            for item in items:
                result[item["pedigreeId"]] = item["pedigreeName"]
        return result

    def gen_px_info(self):
        result = dict()
        # query_condition = dict()
        # offset = None
        # while True:
        #     if offset:
        #         query_condition["_id"] = {"$gt": offset}
        #     query_item = {
        #         "db_name": "raw_data",
        #         "collection_name": "investor_spectrum_info",
        #         "query_condition": query_condition,
        #         "query_field": {
        #             '_id': 1, "feature_id": 1, "spectrum_name": 1},
        #         "sort_field": [("_id", 1)],
        #         "limit_n": 1000}
        #     query_result = self._data_server.call("query_item", query_item)
        #     if not query_result:
        #         break
        #     for item in query_result:
        #         result[item["feature_id"]] = item["spectrum_name"]
        #     offset = query_result[-1]["_id"]
        sql = """
              select id, pedigreeId, pedigreeName from %s where dataStatus!=3 and id > {} ORDER BY id ASC limit 1000
              """ % "sy_cd_ms_cn_px_fms_info"
        for items in self._query_sql_iter_by_id(sql, "seeyii_assets_database_63306"):
            for item in items:
                result[item["pedigreeId"]] = item["pedigreeName"]
        return result


if __name__ == '__main__':
    p = PX()
    p.process()
