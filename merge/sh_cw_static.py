# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2024/1/22
from cfg.config import source_file_path
from core.excel_base import ExcelBase


class ShCwStatic(ExcelBase):
    def __init__(self):
        super().__init__()
        self.file_map = {
            "600084": "dwd_ms_cn_comp_ve_five_set_600084_20240123.csv",
            "104001": "dwd_ms_cn_comp_ve_main_inco_20240126.csv",
            "206020": "dwd_ms_cn_comp_ve_mpro_rat_20240126.csv",
            "104003": "dwd_ms_cn_comp_ve_npro_sk_20240126.csv",
            "700008": "dwd_ms_cn_comp_ve_bcm_first_700008.csv",
            "600083": "dwd_ms_cn_comp_ve_five_set_600083_20240125.csv",
        }
        self.name_map = {
            "600084": "资产负债率",
            "104001": "营业收入",
            "206020": "毛利率",
            "104003": "净利润",
            "700008": "经营性现金流规模",
            "600083": "净资产",
        }

    def process(self):
        a_items = self.query_a_name()
        three_items = self.query_three_name()
        csv_name_result, csv_result = set(), dict()
        for code, file_name in self.file_map.items():
            csv_list = self.read_excel_data(file_name)
            csv_result.setdefault(code, dict())
            for i in csv_list:
                if str(i["indicationid"]) != str(code):
                    continue
                csv_name_result.add(i["compcode"])
                csv_result[code].setdefault(i["compcode"], i["indicationvalue"])

        result,num = list(), 0
        for cmp_code in csv_name_result:
            num += 1
            print(num)
            item = dict()
            a = a_items.get(cmp_code, dict())
            three = three_items.get(cmp_code, dict())

            if a:
                item.update(a)
            elif three:
                item.update(three)
            else:
                item["compCode"] = cmp_code
                # base = self.query_cmp_info(cmp_code)
                # if base:
                #     item.update(base[cmp_code])
                # else:
                #     item["compCode"] =cmp_code

            for code, en in self.name_map.items():
                v = csv_result.get(code).get(cmp_code)
                if v:
                    item[code] = v
            result.append(item)
        self.save_data_excel(result)
        print(f"A股三板公司数据量：{len(a_items.keys()|three_items.keys())}")

    def query_cmp_info(self, code):
        result = dict()
        sql = f"""
                SELECT compCode, compName, "（非上市）在营企业" as listStatus from sy_cd_ms_base_normal_comp_list
                 where dataStatus!=3 and compCode={code};
                """
        query_schema = dict(db_key="xskv2", sql_statement=sql)
        result_list = self._data_server.call("query_sql_item", query_schema)
        for item in result_list:
            compCode = item["compCode"]
            result.setdefault(compCode, item)
        return result

    def save_data_excel(self, result):
        field_cfg = {
            'compCode': ('公司编号', 0),
            'compName': ('公司名称', 1),
            'ty': ('类型', 2),
            'listStatus': ('上市状态', 3),
            'listDate': ('上市时间', 4),
            '700008': ('经营性现金流规模', 5),
            '104001': ('营业收入', 6),
            '206020': ('毛利率', 7),
            '104003': ('净利润', 8),
            '600084': ('资产负债率', 9),
            '600083': ('净资产', 10)
        }
        self._excel_name = self.name_add_date("交行财务数据统计.xlsx")
        self.save_to_excel(field_cfg, {"sheet1": result})

    def query_a_name(self):
        result = dict()
        sql = """
        SELECT id, compCode,compName,if(listStatus=1,"上市", "非上市") as listStatus, dataStatus, "A股" as ty,listDate from sy_cd_ms_base_sk_stock where  
        id > {} ORDER BY id ASC limit 1000;
        """
        for items in self._query_sql_iter_by_id(sql, "xskv2"):
            for item in items:
                compCode = item["compCode"]
                dataStatus = item["dataStatus"]
                if dataStatus == 3:
                    continue
                result.setdefault(compCode, item)
        return result

    def query_three_name(self):
        result = dict()
        sql = """
        SELECT id, compCode,compName,if(listStatus=1,"上市", "非上市") as listStatus, dataStatus, "三板" as ty,listDate from sy_cd_ms_base_nq_stock where
        id > {} ORDER BY id ASC limit 1000;
        """
        for items in self._query_sql_iter_by_id(sql, "xskv2"):
            for item in items:
                compCode = item["compCode"]
                dataStatus = item["dataStatus"]
                if dataStatus == 3:
                    continue
                result.setdefault(compCode, item)
        return result

    def read_excel_data(self, file_name):
        import pandas as pd
        df = pd.read_csv(source_file_path + "/" + file_name)
        data_list = df.to_dict(orient="records")
        data_list = self.filter_data(data_list)
        return data_list


if __name__ == '__main__':
    p = ShCwStatic()
    p.process()
