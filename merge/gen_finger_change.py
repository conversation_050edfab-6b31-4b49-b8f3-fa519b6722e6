# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2022/12/8
from core.excel_base import ExcelBase


class Chenge(ExcelBase):
    def __init__(self):
        super(<PERSON><PERSON>, self).__init__()
        self.schema_db = "section_data"
        self.table_124 = "sy_cd_me_news_a_gg_ref"
        self.table_157 = "sy_cd_me_news_a_gg_ref_bak"
        self.schema_db_key = "section_data"
        self.db_124 = 'seeyii_db'
        self.db_157 = 'section_data'

    def process(self, *args, **kwargs):
        result = list()
        data_124_dict = self.query_raw_data(self.db_124, self.table_124)
        print(f"data_124_dict = {len(data_124_dict)}")
        data_157_dict = self.query_raw_data(self.db_157, self.table_157)
        print(f"data_157_dict = {len(data_157_dict)}")
        add = data_157_dict.keys() - data_124_dict.keys()
        print(f"inc={len(add)}")
        _del = data_124_dict.keys() - data_157_dict.keys()
        print(f"del={len(_del)}")
        for d in _del:
            data = data_124_dict[d]
            data["dataStatus"] = 3
            result.append(data)
        # result += [data_157_dict[i] for i in add]
        print(f"change = {len(result)}")
        print(result[:10])
        field_cfg = self.import_data_cfg()
        self._excel_name = self.name_add_date("{}.xlsx".format(self.table_124))
        self.save_to_excel(field_cfg, {"sheet1": result})

    def gen_finder_id(self, raw_dict):
        result = dict()
        self.field_list = [
            "url",
            "title",
            "publishDate",
            "refLoc",
            "stockCode",
            "downTime"]
        for _id, item in raw_dict.items():
            new_item = self.fetch_dict(item, self.field_list)
            finger_id = self._gen_graph_id(new_item)
            result.setdefault(finger_id, new_item)
        return result

    def query_raw_data(self, db_key, table):
        result = dict()
        sql = """SELECT * FROM %s WHERE id>'{}' order by id ASC limit 1000""" % table
        for result_list in self._query_sql_iter_by_id(sql, db_key):
            for item in result_list:
                data_status = item["dataStatus"]
                if data_status == 3:
                    continue
                finger_id = item["refLoc"]
                result.setdefault(finger_id, item)
        return result

    def import_data_cfg(self):
        data_dict = dict()
        item_list = self.query_table_schema()
        for idx, item in enumerate(item_list):
            data_dict.setdefault(item["column_name"],
                                 (item["column_comment"] if item["column_comment"] else item["column_name"], idx))
        return data_dict

    def query_table_schema(self):
        data_list = list()
        sql = """select column_name, column_comment 
        from information_schema.columns where table_schema ='{}' 
         and table_name = '{}';"""
        query_schema = dict(db_key=self.schema_db_key, sql_statement=sql.format(self.schema_db, self.table_157))
        result_list = self._data_server.call("query_sql_item", query_schema)
        for item in result_list:
            data_list.append(item)
        return data_list


if __name__ == '__main__':
    p = Chenge()
    a =  {
        "url": "http://www.cninfo.com.cn/finalpage/2022-03-28/1212683937.PDF",
        "ggsj": "2022-03-28",
        "stock_name": "锡业股份",
        "stock_code": "000960",
        "title": "锡业股份：云南锡业股份有限公司关于独立董事任期届满的提示性公告",
        "d_status": 300,
        "d_url": "/agg/2022-03-27/1212683937.PDF",
        "d_time": "2022-03-27T16:56:07.812Z"
    }
    p.gen_finder_id(a)
