# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2019/09/02

import abc
import json
from collections import OrderedDict
from itertools import count
import traceback

_t_counter = count()


class BaseField(object):
    def __init__(self, is_required=False, is_filter=False):
        self.id = _t_counter.__next__()
        self.is_required = is_required
        self.is_filter = is_filter

    @abc.abstractmethod
    def fill(self, *args, **kwargs):
        raise NotImplementedError

    @staticmethod
    def parameter(depend, data, *args):
        r = list()
        for dep in depend:
            r.append(data.get(dep))
        return list(args) + r


class Field(BaseField):
    def __init__(self, func, is_required=False, is_filter=False, default=None):
        super().__init__(is_required, is_filter)

        self.func = func
        self.default = default

    def fill(self, *args, **kwargs):
        return self.func(*args, **kwargs)


class CondField(BaseField):
    """
    depend:依赖
    is_required：如果没有值就报错
    default：默认值
    """
    def __init__(self, func, depend=list(), is_required=False, is_filter=False, default=None):
        super().__init__(is_required, is_filter)

        self.func = func
        self.depend = depend
        self.default = default

    def fill(self, data, *args, **kwargs):
        return self.func(*self.parameter(self.depend, data, *args), **kwargs)


class DataFillerMeta(type):
    def __new__(mcs, name, bases, attrs):
        cls = super(DataFillerMeta, mcs).__new__(mcs, name, bases, attrs)

        fields = list()
        for key, value in attrs.items():
            if isinstance(value, BaseField):
                fields.append((key, value))

        fields.sort(key=lambda x: x[1].id)
        cls.fields = list(map(lambda d: d[0], fields))
        return cls


class DataFiller(metaclass=DataFillerMeta):
    fields = None

    def __init__(self, *args, **kwargs):
        self.data = OrderedDict()
        self.exc_info_list = list()

        filter_key_list = list()
        for key, data_entry in self.iter_fields():
            try:
                if data_entry.is_filter:
                    filter_key_list.append(key)
                if isinstance(data_entry, Field):
                    value = data_entry.fill(*args, **kwargs)
                elif isinstance(data_entry, CondField):
                    value = data_entry.fill(self.data, *args, **kwargs)
                else:
                    value = None
                    self.exc_info_list.append(f"{key} is not mapping Field or CondField")
            except Exception as exc:
                value = None
                self.exc_info_list.append(error_message(exc))

            if data_entry.is_required and value is None:
                raise DefeatDataFill("{} is required".format(key))

            if value is None and hasattr(data_entry, 'default') and data_entry.default is not None:
                value = data_entry.default

            self.data[key] = value

        for filter_key in filter_key_list:
            self.data.pop(filter_key, None)

    def pp(self):
        print(json.dumps(self.data, indent=2, ensure_ascii=False))

    @classmethod
    def iter_fields(cls):
        for k in cls.fields:
            yield (k, getattr(cls, k))

    @classmethod
    def has_field(cls, k):
        return k in cls.fields


def error_message(exc):
    message = traceback.format_exc()
    message = " ## ".join(message.split("\n"))
    message = " ## ".join([message, str(exc)])
    return message


class DefeatDataFill(Exception):
    def __init__(self, msg=''):
        super(DefeatDataFill, self).__init__(msg)
