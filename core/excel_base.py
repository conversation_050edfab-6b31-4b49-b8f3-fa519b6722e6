# Copyright (c) 2015 Shiye Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2019/09/02
import traceback
import decimal
import pymongo
import json
import numpy
import os
import pandas as pd
import xlsxwriter
import hashlib
from datetime import datetime, date

from cfg.config import source_file_path, out_file_path
from db.engine.data_services import DataServer

from utils.datetime_util import DatetimeUtil


class ExcelBase(object):
    def __init__(self):
        self._data_server = DataServer.get_instance()
        self._in_file_path = source_file_path
        self._excel_name = None
        self._table_name = None

    @classmethod
    def run(cls, *args, **kwargs):
        c = cls()
        return c.process(*args, **kwargs)

    def process(self, *args, **kwargs):
        pass

    def _extract_data(self, filename, sheet_name) -> list:
        """
        read excel to list include dict
        :param filename:
        :param sheet_name:
        :return:
        """
        df = pd.read_excel(filename, sheet_name=sheet_name)
        data_list = df.to_dict(orient="records")
        data_list = self.filter_data(data_list)
        return data_list

    @staticmethod
    def name_add_date(_str=None):
        target_date = DatetimeUtil.get_today_base()
        date_base = DatetimeUtil.date_to_str(target_date, str_format="%Y%m%d")
        if _str:
            if _str.find("."):
                _type = _str[_str.find("."):]
                _str = _str[:-(len(_str) - _str.find("."))] + "_" + date_base + _type
            else:
                _str += "_" + date_base + ".xlsx"
        return _str

    def save_to_excel(self, field_dict, sheet_item_dict, output_file=None):
        """
        :param field_dict: {
            eg:  "字段名": (u"字段含义", 插入的列号),
                 "cname": (u"公司名称", 0)
        }
        :param sheet_item_dict: {
            Sheet名称:[data]
        }
        :return:
        """
        if output_file:
            output_file_path = output_file
        else:
            output_file_path = os.path.join(out_file_path,
                                            self._excel_name)
        xls_file = xlsxwriter.Workbook(output_file_path)
        for sheet_name, item_list in sheet_item_dict.items():
            xls_sheet = xls_file.add_worksheet(name=sheet_name)
            for title in field_dict.values():
                xls_sheet.write(0, title[1], title[0])
            row_no = 1
            for result_item in item_list:
                for field_name, title in field_dict.items():
                    field_value = result_item.get(field_name, "")
                    if isinstance(field_value, decimal.Decimal):
                        field_value = float(field_value)
                    if isinstance(field_value, datetime):
                        field_value = DatetimeUtil.date_to_str(field_value)
                    if isinstance(field_value, date):
                        field_value = field_value.strftime("%Y-%m-%d")
                    if not isinstance(field_value, str):
                        field_value = json.dumps(field_value, ensure_ascii=False)
                    if field_value == "null" or field_value is None:
                        continue
                    xls_sheet.write(row_no, title[1], field_value)
                row_no += 1
        xls_file.close()
        print("finished.")

    @staticmethod
    def filter_data(data_list: list):
        """
        替换读取excel的脏数据为空
        :param data_list:
        :return:
        """
        item_list = list()
        for data_dict in data_list:
            data_item = dict()
            for title, value in data_dict.items():
                if value is None:
                    value = ""
                if value is numpy.nan:
                    value = ""
                if value is pd.NaT:
                    value = ""
                if str(value) == "nan":
                    value = ""
                data_item[title] = value
            item_list.append(data_item)
        return item_list

    @staticmethod
    def fetch_dict(raw_dict, field_list):
        result = dict()
        for field in field_list:
            if isinstance(field, tuple):
                raw_value = raw_dict.get(field[0], None)
                if raw_value is None:
                    continue
                if len(field) == 3:
                    raw_value = field[2](raw_value)
                if raw_value is not None:
                    result[field[1]] = raw_value
            else:
                raw_value = raw_dict.get(field, None)
                if raw_value is None:
                    continue
                result[field] = raw_value
        return result

    def _query_iter_by_id(self, query_schema):
        limit_n = query_schema["limit_n"]
        query_condition, obj_id = query_schema["query_condition"], None
        query_schema["sort_field"] = [("_id", pymongo.ASCENDING)]
        while True:
            if obj_id is not None:
                query_condition["_id"] = {"$gt": obj_id}
            query_result = self._data_server.call("query_item", query_schema)
            if not query_result:
                return None
            yield query_result
            if len(query_result) < limit_n:
                break
            obj_id = query_result[-1]["_id"]

    def _query_sql_iter_by_id(self, sql_statement, db_key):
        obj_id = 0
        while True:
            if not obj_id:
                query_schema = dict(db_key=db_key, sql_statement=sql_statement.format(0))
            else:
                query_schema = dict(db_key=db_key, sql_statement=sql_statement.format(obj_id))
            result_list = self._data_server.call("query_sql_item", query_schema)
            if not result_list:
                break
            yield result_list
            obj_id = result_list[-1]["id"]

    @staticmethod
    def _hash(raw_str):
        if isinstance(raw_str, bytes):
            raw_str = str(raw_str, encoding="utf-8")
        raw_str = raw_str.lower().encode('utf-8')
        hash_calculator = hashlib.md5()
        hash_calculator.update(raw_str)
        return hash_calculator.hexdigest()

    @staticmethod
    def remainder(string_, mod=100):
        """
        对十六进制取余
            先转化为10进制，再对 mod 取余
        """
        return int(string_, 16) % mod

    def _gen_graph_id(self, raw_value):
        """
        生成指纹标识（仅适用于基础数据类型[number,basestring]嵌套的结构）
        :param raw_value: 关联图
        :return: 指纹标识
        """
        result_str = ""
        if isinstance(raw_value, dict) is True:
            keys = sorted(raw_value.keys())
            result_str += "".join(keys)
            for key in keys:
                value = raw_value[key]
                result_str += self._gen_graph_id(value)
        elif isinstance(raw_value, list) is True:
            raw_value = [str(raw_v) for raw_v in raw_value]
            result_str += "".join(sorted(raw_value))
        else:
            result_str += str(raw_value)
        return self._hash(result_str)

    def _gen_common_graph_id(self, raw_value):
        """
        生成指纹标识 (适用于复杂的基础数据类型)
        :param raw_value: 关联图
        :return: 指纹标识
        """
        result_str = ""
        if isinstance(raw_value, dict) is True:
            keys = sorted(raw_value.keys())
            result_str += "".join(keys)
            for key in keys:
                value = raw_value[key]
                result_str += self._gen_graph_id(value)
        elif isinstance(raw_value, list) is True:
            list_str = list()
            for value in raw_value:
                list_str.append(self._gen_graph_id(value))
            result_str += "".join(sorted(list_str))
        else:
            result_str += str(raw_value)
        return self._hash(result_str)

    @staticmethod
    def error_message(exc):
        message = traceback.format_exc()
        message = "\n".join(message.split("\n"))
        message = "\n".join([message, str(exc)])
        return message


if __name__ == '__main__':
    a = "江西银杉白水泥股份有限公司"
    p = ExcelBase()
    v = p._hash(a)
    print(p.remainder(v))
