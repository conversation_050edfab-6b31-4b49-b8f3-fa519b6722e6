# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2020/05/14
from core.thread_base import BaseMaster, BaseWorker
from utils.log_util import create_logger

LOGGER = create_logger("TEST", "donkey_relation.log")


class RelationAPIMaster(BaseMaster):
    def __init__(self):
        super(RelationAPIMaster, self).__init__()
        self.task_queue = None
        self.signal_queue = None
        self.result_queue = None
        self.put_signal = "put"
        self.finish_signal = "finish"
        self.worker_n = 10
        self.worker = RelationWorker

    def process(self):
        self.start_worker()
        name_list = []
        for name in name_list:
            self.allocating_task(name)
        self.wait_task_finish()
        # finish signal.
        self.allocating_finish_signal()


class RelationWorker(BaseWorker):
    def __init__(self, worker_name, task_queue, signal_queue, result_queue, data_server):
        super(RelationWorker, self).__init__(worker_name, task_queue, signal_queue, result_queue, data_server)
        self.result_queue = result_queue
        self.task_queue = task_queue
        self.signal_queue = signal_queue
        self.finish_signal = "finish"
        self._data_server = data_server

    def process_task(self, name):
        try:
            pass
        except Exception as e:
            print(e)
            LOGGER.error("worker_name=%s\terror=%s.name_list_dict=%s" % (self.name, str(e), name))
        finally:
            self.signal_queue.get(timeout=10)
