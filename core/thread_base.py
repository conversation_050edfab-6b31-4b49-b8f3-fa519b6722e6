# -*- encoding:utf-8 -*-
# Copyright (c) 2015 ShiYe Inc.
# All rights reserved.
# <AUTHOR> <EMAIL>
# Date:   2020/05/12
import queue
import time
import threading

from core.excel_base import ExcelBase
from utils.log_util import create_logger

LOGGER = create_logger("EXCEL", "panda_thread.log")


class BaseMaster(ExcelBase):
    def __init__(self):
        super(BaseMaster, self).__init__()
        self.task_queue = None
        self.signal_queue = None
        self.result_queue = None
        self.put_signal = "put"
        self.finish_signal = "finish"
        self.worker_n = 10
        self.worker = None

    def process(self, *args, **kwargs):
        pass

    def start_worker(self):
        self.task_queue = queue.Queue(0)
        self.signal_queue = queue.Queue(0)
        self.result_queue = queue.Queue(0)
        worker_list = list()
        for i in range(self.worker_n):
            worker_name = "".join(["base_info_worker_", str(i + 1)])
            worker_list.append(
                self.worker(
                    worker_name, self.task_queue, self.signal_queue,
                    self.result_queue, self._data_server))
        for worker in worker_list:
            worker.start()
            LOGGER.info("worker_name=%s\tstarted." % worker.name)

    def allocating_task(self, task_item):
        if self.task_queue.qsize() > 2000:
            while True:
                if self.task_queue.qsize() < 500:
                    break
                time.sleep(3)
        self.task_queue.put(task_item)
        self.signal_queue.put(self.put_signal)

    def wait_task_finish(self):
        while True:
            if self.signal_queue.qsize() == 0:
                break
            time.sleep(10)

    def allocating_finish_signal(self):
        for _ in range(self.worker_n):
            self.task_queue.put(self.finish_signal)
        time.sleep(10)


class BaseWorker(threading.Thread, ExcelBase):
    def __init__(self, worker_name, task_queue, signal_queue, result_queue, data_server):
        threading.Thread.__init__(self, name=worker_name)
        super(BaseWorker, self).__init__()
        self.result_queue = result_queue
        self.task_queue = task_queue
        self.signal_queue = signal_queue
        self.finish_signal = "finish"
        self._data_server = data_server

    def run(self):
        while True:
            try:
                task_item = self.task_queue.get(timeout=10)
                LOGGER.info("worker_name=%s\tgeted one task. %s" % (self.name, task_item))
            except:
                LOGGER.info("worker_name=%s\tno task." % self.name)
                continue
            if self.is_finished(task_item) is True:
                break
            self.process_task(task_item)

    def process_task(self, name):
        pass

    def is_finished(self, task_item):
        if task_item == self.finish_signal:
            return True
        return False
